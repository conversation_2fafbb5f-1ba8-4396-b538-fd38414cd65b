<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo.svg';
import PageLoader from '@/components/global/PageLoader.vue';
import GreenCheck from '@/components/states/GreenCheck.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';
import { executeWebkitMessage } from '@/util/facades/webkit-app.ts';
import { isMobileSubdomain } from '@/composables/subdomain.ts';

enum ForgotPasswordState {
  IDLE,
  SENT,
  ERROR,
}

const { t } = useI18n();
const componentState = reactive({
  global: ComponentStateType.OK as ComponentStateType,
  forgotPassword: ForgotPasswordState.IDLE,
});

const email = ref('');

const onForgotPasswordSubmit = async() => {
  componentState.global = ComponentStateType.LOADING;
  try {
    await customAxios.post('/user/forgot-password', {
      email: email.value,
    });
    email.value = '';
    componentState.forgotPassword = ForgotPasswordState.SENT;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('login.failed-to-reset-pw'),
      timeout: 6000,
    });
  }
  componentState.global = ComponentStateType.OK;
};

const onForgotPasswordSuccess = () => {
  executeWebkitMessage('onForgotPasswordSuccess');
};

</script>

<template>
  <div class="mx-auto w-[18rem] lw:w-[22rem]">
    <div
      v-if="componentState.global === ComponentStateType.OK"
      class="w-full grid gap-6"
    >
      <div
        v-if="componentState.forgotPassword === ForgotPasswordState.IDLE"
        class="w-fit mx-auto"
      >
        <SolarCloudLogo class="h-16 [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div
        v-if="[ForgotPasswordState.IDLE, ForgotPasswordState.ERROR].includes(componentState.forgotPassword)"
        class="grid gap-2 text-center"
      >
        <h1 class="text-3xl font-bold">
          {{ $t('login.forgotten-password') }}
        </h1>
        <p class="text-sm text-balance text-muted-foreground">
          {{ $t('login.forgot-password-desc') }}
        </p>
      </div>
      <form
        v-if="[ForgotPasswordState.IDLE, ForgotPasswordState.ERROR].includes(componentState.forgotPassword)"
        class="grid gap-4"
        @submit.prevent="onForgotPasswordSubmit"
      >
        <div class="grid gap-2">
          <Label for="email">{{ $t('misc.mail') }}</Label>
          <Input
            v-model="email"
            type="email"
            placeholder="<EMAIL>"
            required
            :disabled="(componentState.global as ComponentStateType) === ComponentStateType.LOADING"
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500"
          />
        </div>
        <Button
          type="submit"
          class="w-full"
        >
          {{ $t('misc.send') }}
        </Button>
      </form>
      <div
        v-else
        class="grid justify-items-center gap-4"
      >
        <GreenCheck class="w-16 h-16" />
        <div class="font-bold">
          {{ $t('misc.form-sent-successfully') }}!
        </div>
        <component
          :is="isMobileSubdomain ? 'div' : 'router-link'"
          :replace="true"
          :to="{name: routeMap.home.children.dashboardInstallations.name}"
          class="block"
          @click="isMobileSubdomain ? onForgotPasswordSuccess() : undefined"
        >
          <Button
            type="submit"
            class="w-fit"
          >
            {{ $t('misc.back-to-home') }}
          </Button>
        </component>
      </div>
    </div>
    <div v-else-if="componentState.global === ComponentStateType.LOADING">
      <PageLoader :flex-center="true" />
    </div>
  </div>
</template>
