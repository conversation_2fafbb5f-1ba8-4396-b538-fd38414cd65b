{"$schema": "https://shadcn-vue.com/schema.json", "style": "default", "typescript": true, "tailwind": {"config": "src/assets/styles.css", "css": "src/assets/styles.css", "baseColor": "slate", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@/shadcn-components", "composables": "@/shadcn-composables", "utils": "@/shadcn-utils", "ui": "@/shadcn-components/ui", "lib": "@/shadcn-lib"}, "iconLibrary": "lucide"}