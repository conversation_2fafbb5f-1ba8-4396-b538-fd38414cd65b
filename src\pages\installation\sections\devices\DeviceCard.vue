<template>
  <div class="bg-prim-col-foreground-1 dark:bg-prim-col-2 border border-gray-700 rounded-md p-4 text-white dark:hover:bg-prim-col-2/50 transition-colors">
    <div class="flex items-center justify-between mb-2">
      <h3 class="text-sm font-medium">
        {{ title }}
      </h3>
      <component
        :is="icon"
        class="h-4 w-4 text-gray-400"
      />
    </div>
    <div class="flex flex-col space-y-2">
      <div class="flex items-center justify-between">
        <span class="text-lg font-bold">
          {{ primaryMetric.value }}
          <span class="text-xs ml-1 text-gray-400">{{ primaryMetric.unit }}</span>
        </span>
        <span
          :class="[
            'px-2 py-1 rounded-full text-xs',
            status === 'online' ? 'bg-green-500/20 text-green-500' : 'bg-red-500/20 text-red-500'
          ]"
        >
          {{ status }}
        </span>
      </div>
      <div
        v-if="secondaryMetric"
        class="text-xs text-gray-400"
      >
        {{ secondaryMetric.value }} {{ secondaryMetric.unit }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Battery, Car, Home, CloudLightning, Plug, Thermometer } from 'lucide-vue-next';
import { computed } from 'vue';

const props = defineProps({
  type: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
  primaryMetric: {
    type: Object,
    required: true,
  },
  secondaryMetric: {
    type: Object,
    default: null,
  },
});

const icon = computed(() => {
  switch (props.type) {
    case 'battery_storage':
      return Battery;
    case 'ev_charger':
      return Car;
    case 'home':
      return Home;
    case 'grid':
      return CloudLightning;
    case 'domestic_water_heater_ac':
    case 'domestic_water_heater_dc':
      return Thermometer;
    default:
      return Plug;
  }
});
</script>
