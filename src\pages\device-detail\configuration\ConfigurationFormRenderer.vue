<script setup lang="ts">
import { ref, computed } from 'vue';
import type { DeviceInstance } from '@/pages/installation/types/installation-types';
import type { BackendFormSchema, BackendFormField, LaravelValidationError, FormErrors } from '../types/device-configuration-types';
import { customAxios } from '@/util/axios';
import GenericFormRenderer from '@/components/forms/GenericFormRenderer.vue';
import { deployToast, ToastType } from '@/util/toast';
import { useI18n } from 'vue-i18n';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { sleep } from '@/util/fakers/sleep';

interface Props {
  deviceDetail: DeviceInstance;
  installationId: string;
}

const props = defineProps<Props>();

const configSchema = ref<BackendFormSchema>();
const deviceConfig = ref<Record<string, any>>({ ...props.deviceDetail.configuration });
const notAvailable = ref(false);
const sending = ref(false);
const formErrors = ref<FormErrors>({});
const { t } = useI18n();

try {
  const schemaResponse = await customAxios.get<BackendFormSchema>(
    `/user/device/configuration-schema/${props.installationId}/${props.deviceDetail.id}`
  );
  configSchema.value = schemaResponse.data;
} catch {
  notAvailable.value = true;
}

const fields = computed<BackendFormField[]>(() => configSchema.value ?? []);

const processLaravelErrors = (laravelErrors: LaravelValidationError): FormErrors => {
  const processedErrors: FormErrors = {};

  Object.entries(laravelErrors.errors).forEach(([key, messages]) => {
    const fieldKey = key.startsWith('configuration.') ? key.replace('configuration.', '') : key;
    processedErrors[fieldKey] = messages;
  });

  return processedErrors;
};

const saveConfig = async() => {
  if (sending.value) {
    return;
  }

  formErrors.value = {};
  sending.value = true;

  await sleep(2000);

  try {
    await customAxios.put(`/user/device/configure/${props.installationId}/${props.deviceDetail.id}`, {
      configuration: deviceConfig.value,
    });

    deployToast(ToastType.SUCCESS, {
      text: t('device-detail.config-submitted-successfully'),
      timeout: 4000,
    });
  } catch (error: any) {
    if (error.response?.status === 422 && error.response?.data) {
      const laravelError = error.response.data as LaravelValidationError;
      formErrors.value = processLaravelErrors(laravelError);

      deployToast(ToastType.ERROR, {
        text: t('misc.validation-errors-found'),
        timeout: 6000,
      });
    } else {
      deployToast(ToastType.ERROR, {
        text: t('misc.failed-to-save-item'),
        timeout: 6000,
      });
    }
  } finally {
    sending.value = false;
  }
};

const onSendInstruction = async(field: BackendFormField) => {
  try {
    await customAxios.post(`/user/device/instruct/${props.installationId}/${props.deviceDetail.id}`, {
      instruction: field.value,
    });

    deployToast(ToastType.SUCCESS, {
      text: t('device-detail.config-submitted-successfully'),
      timeout: 4000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.failed-to-save-item'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <div v-if="notAvailable" class="p-4 text-gray-500">
    {{ $t('device-detail.configuration-not-available') }}
  </div>

  <form v-else class="0" @submit.prevent="saveConfig">
    <div class="grid grid-cols-5 gap-4">
      <GenericFormRenderer
        v-model="deviceConfig"
        :fields="fields"
        :errors="formErrors"
        field-class="flex flex-col gap-1"
        label-class="text-sm"
        @send-instruction="onSendInstruction"
      />
    </div>

    <div class="pt-4">
      <ShadCnButton
        class="flex items-center cursor-pointer px-4 py-2! h-fit gap-1 disabled:cursor-not-allowed"
        type="submit"
        :disabled="sending"
      >
        {{ sending ? `${t('misc.loading')}...` : t('device-detail.send-to-device') }}
      </ShadCnButton>
    </div>
  </form>
</template>

