<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { cn } from '@/shadcn-utils';
import type { HTMLAttributes } from 'vue';

const props = defineProps<{
  class?: HTMLAttributes['class']
  defaultValue?: string | number
  modelValue?: string | number
  rows?: number
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});
</script>

<template>
  <textarea
    v-model="modelValue"
    :rows="rows"
    :class="cn('ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500 h-10 flex min-h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50', props.class)"
  />
</template>
