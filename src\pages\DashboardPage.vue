<script lang="ts" setup>
import { computed, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import PageLoader from '@/components/global/PageLoader.vue';
import CreateOrEditInstallationDialog from '@/pages/dashboard/CreateOrEditInstallationDialog.vue';
import DashboardDivider from '@/pages/dashboard/DashboardDivider.vue';
import InstallationCard from '@/pages/dashboard/InstallationCard.vue';
import { routeMap } from '@/router/routes';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { useAuthStore } from '@/stores/auth-store';
import { useInstallationStore } from '@/stores/installation-store';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import type { InstallationDetailData } from './installation/types/installation-types';

const route = useRoute();
const router = useRouter();
const installationStore = useInstallationStore();
const authStore = useAuthStore();

const modals = reactive({
  installationCreate: {
    isOpened: false,
  },
});

const initials = computed(() => {
  const splitName = authStore.user?.name.split(' ');
  if (!splitName) {
    return '??';
  }
  return splitName[0]?.charAt(0) + (splitName[1]?.charAt(0) ?? '');
});

const onInstallationCreated = async(installationData: InstallationDetailData) => {
  await router.replace({ name: routeMap.home.children.installation.name, params: { installationId: installationData.id } });
};

initializeBasicBreadcrumbBehaviour(routeMap.home.meta.i18nTitle, routeMap.home.children.dashboardInstallations.name, true, route);

await installationStore.fetchInstallations();

if (installationStore.ownedInstallations?.length === 1 && !authStore.userHelpers.canServiceInstallation) {
  await router.replace({ name: routeMap.home.children.installation.name, params: { installationId: installationStore.ownedInstallations[0].id } });
}
</script>

<template>
  <div class="grid gap-4 max-w-(--breakpoint-3xl) mx-auto">
    <div class="flex flex-wrap gap-2 items-center justify-start bg-prim-col-foreground-1 p-4 relative select-none rounded-3xl">
      <div class="flex items-center gap-2">
        <div class="h-9 w-9 rounded-full bg-prim-col-selected-1 flex items-center justify-center text-center">
          {{ initials }}
        </div>
        <div>
          <div class="font-bold">
            {{ authStore.user?.name }}
          </div>
          <div class="text-xs">
            {{ authStore.user?.email }}
          </div>
        </div>
      </div>
      <DashboardDivider class="hidden sm:block" />
      <div class="flex items-center gap-2">
        <div>
          <div class="font-bold">
            {{ installationStore.installations?.length }}
          </div>
          <div class="text-xs">
            {{ $t('installation.title-counted', installationStore.installations?.length) }}
          </div>
        </div>
        <DashboardDivider />
        <div>
          <div class="font-bold">
            {{ installationStore.pairedDevices?.length }}
          </div>
          <div class="text-xs">
            {{ $t('installation.paired-devices-counted', installationStore.pairedDevices.length) }}
          </div>
        </div>
      </div>
      <div class="flex-1 md:flex-none md:ml-auto md:mr-0 flex items-center gap-4">
        <ShadCnButton
          type="submit"
          variant="default"
          size="icon"
          class="px-2 text-xs sm:text-sm rounded-[0.5rem] w-fit h-9 sm:h-10 sm:px-4 dark:text-white text-black"
          @click="modals.installationCreate.isOpened = true;"
        >
          {{ $t('installation.create-installation' ) }}
        </ShadCnButton>
      </div>
    </div>
    <section
      v-if="authStore.userHelpers.canServiceInstallation"
      class="mb-2 w-full"
    >
      <h2 class="font-bold text-lg xl:text-xl mb-4">
        {{ $t('service.title-short') }}
      </h2>
      <div class="w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 [&>a]:rounded-xl">
        <router-link
          :to="{name: routeMap.service.children.installations.name}"
          class="w-full bg-yellow-500 hover:bg-yellow-400 transition-colors duration-200"
          appear
        >
          <div class="py-6 px-4 text-black">
            <div class="font-bold">
              {{ $t('admin.installations.title') }}
            </div>
            <div class="text-xs">
              {{ $t('admin.installations.goto') }}
            </div>
          </div>
        </router-link>
      </div>
    </section>
    <section
      v-if="installationStore.ownedInstallations?.length"
      class="mb-2 w-full"
    >
      <h2 class="font-bold text-lg xl:text-xl mb-4">
        {{ $t('installation.my-installations') }}
      </h2>
      <div class="installations-container w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 [&>a]:rounded-xl">
        <transition-group
          appear
          :duration="1000"
        >
          <router-link
            v-for="installation in installationStore.ownedInstallations"
            :key="installation?.id"
            :to="{name: routeMap.home.children.installation.name, params: {installationId: installation.id }}"
            class="w-full bg-prim-col-foreground-1"
            appear
          >
            <InstallationCard :installation="installation" />
          </router-link>
        </transition-group>
      </div>
    </section>
    <section v-if="installationStore.foreignInstallations?.length">
      <h2 class="font-bold text-lg xl:text-xl mb-4">
        {{ $t('installation.installations-shared-with-me') }}
      </h2>
      <div class="installations-container grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4 [&>a]:rounded-xl">
        <transition-group
          appear
          :duration="1000"
        >
          <router-link
            v-for="installation in installationStore.foreignInstallations"
            :key="installation?.id"
            :to="{name: routeMap.home.children.installation.name, params: {installationId: installation.id }}"
            class="w-full bg-prim-col-foreground-1"
            appear
          >
            <InstallationCard :installation="installation" />
          </router-link>
        </transition-group>
      </div>
    </section>
    <div
      v-if="!installationStore.installations?.length && !authStore.userHelpers.canServiceInstallation"
      class="absolute-center flex flex-col items-center text-center select-none"
    >
      <div class="relative w-0 h-12 mb-6">
        <div class="w-24 h-12 bg-prim-col-foreground-1/50 rounded-md absolute -translate-x-1/2 -left-2 -bottom-2" />
        <div class="w-24 h-12 bg-prim-col-foreground-1/60 rounded-md absolute-center" />
        <div class="w-24 h-12 bg-prim-col-foreground-1/65 rounded-md absolute translate-x-1/2 -right-2 -top-2" />
      </div>
      <div class="text-prim-col-foreground-2">
        {{ $t('installation.no-installations') }}
      </div>
      <div class="text-prim-col-foreground-2 text-sm">
        {{ $t('installation.no-installations-desc') }}.
      </div>
    </div>
    <suspense>
      <CreateOrEditInstallationDialog
        v-model="modals.installationCreate.isOpened"
        @installation-created="onInstallationCreated"
      />
      <template #fallback>
        <teleport to="body">
          <div class="fixed w-screen h-dvh bg-black/60 z-50 top-0 left-0">
            <PageLoader :absolute-center="true" />
          </div>
        </teleport>
      </template>
    </suspense>
  </div>
</template>

<style lang="css" scoped>
/* we will explain what these classes do next! */
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>

<style lang="css">

.installations-container:has(> :hover) {
  a:not(:hover) {
    opacity: 0.4;
  }
}

.installations-container {
  > a {
    transition: opacity 0.4s ease, background-color 0.4s ease;
    &:hover {
      background: rgb(var(--prim-col-selected-1));
    }
  }
}
</style>
