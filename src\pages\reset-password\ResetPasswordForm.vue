<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo.svg';
import PasswordHelper from '@/components/forms/PasswordHelper.vue';
import PageLoader from '@/components/global/PageLoader.vue';
import GreenCheck from '@/components/states/GreenCheck.vue';
import RedFail from '@/components/states/RedFail.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { InputPassword } from '@/shadcn-components/ui/input-password';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';

enum ResetPasswordState {
  IDLE,
  SENT,
  ERROR,
}

const { t } = useI18n();
const componentState = reactive({
  global: ComponentStateType.OK as ComponentStateType,
  resetPassword: ResetPasswordState.IDLE,
});
const route = useRoute();
const badToken = ref(false);

const credentials = reactive({
  newPassword: '',
  repeatedNewPassword: '',
});
const { email } = route.query;
const { token } = route.params;

const onResetPasswordRequest = async() => {
  componentState.global = ComponentStateType.LOADING;
  try {
    await customAxios.post('/user/reset-password', {
      token,
      email,
      password: credentials.newPassword,
      password_confirmation: credentials.repeatedNewPassword,
    });
    componentState.resetPassword = ResetPasswordState.SENT;
  } catch (e: any) {
    if (e?.response?.data?.code === 'passwords.token') {
      badToken.value = true;
    }
    deployToast(ToastType.ERROR, {
      text: t('login.failed-to-reset-pw'),
      timeout: 6000,
    });
  }
  componentState.global = ComponentStateType.OK;
};

</script>

<template>
  <div class="mx-auto w-[18rem] lw:w-[22rem]">
    <div
      v-if="componentState.global === ComponentStateType.OK"
      class="w-full grid gap-6"
    >
      <div
        v-if="componentState.resetPassword === ResetPasswordState.IDLE"
        class="w-fit mx-auto"
      >
        <SolarCloudLogo class="h-16 [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div
        v-if="[ResetPasswordState.IDLE, ResetPasswordState.ERROR].includes(componentState.resetPassword)"
        class="grid gap-2 text-center"
      >
        <h1 class="text-3xl font-bold">
          {{ $t('login.reset-password') }}
        </h1>
        <p class="text-balance text-muted-foreground">
          {{ $t('login.reset-password-desc') }}
        </p>
      </div>
      <form
        v-if="[ResetPasswordState.IDLE, ResetPasswordState.ERROR].includes(componentState.resetPassword) && !badToken"
        class="grid gap-4"
        @submit.prevent="onResetPasswordRequest"
      >
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="password">{{ $t('login.new-password') }}</Label>
          </div>
          <InputPassword
            id="password"
            v-model="credentials.newPassword"
            type="password"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="password">{{ $t('login.repeat-new-password') }}</Label>
          </div>
          <InputPassword
            id="password"
            v-model="credentials.repeatedNewPassword"
            type="password"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <PasswordHelper :credentials="credentials" />
        <Button
          type="submit"
          class="w-full"
        >
          {{ $t('misc.send') }}
        </Button>
      </form>
      <div
        v-else-if="badToken"
        class="grid justify-items-center gap-4"
      >
        <RedFail class="w-16 h-16" />
        <div class="font-bold">
          {{ $t('misc.token-not-valid') }}!
        </div>
        <router-link
          :replace="true"
          :to="{name: routeMap.forgotPassword.name}"
          class="block"
        >
          <Button
            type="submit"
            class="w-fit"
          >
            {{ $t('login.reask-new-password') }}
          </Button>
        </router-link>
      </div>
      <div
        v-else
        class="grid justify-items-center gap-4"
      >
        <GreenCheck class="w-16 h-16" />
        <div class="font-bold">
          {{ $t('login.pw-successfully-set') }}!
        </div>
        <router-link
          :replace="true"
          :to="{name: routeMap.home.children.dashboardInstallations.name}"
          class="block"
        >
          <Button
            type="submit"
            class="w-fit"
          >
            {{ $t('login.signIn') }}
          </Button>
        </router-link>
      </div>
    </div>
    <div v-else-if="componentState.global === ComponentStateType.LOADING">
      <PageLoader :flex-center="true" />
    </div>
  </div>
</template>
