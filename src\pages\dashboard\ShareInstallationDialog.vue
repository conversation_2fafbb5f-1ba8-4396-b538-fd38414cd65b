<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { useInstallationStore } from '@/stores/installation-store';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import PageLoader from '@/components/global/PageLoader.vue';
import type { InstallationDetailData } from '../installation/types/installation-types';

interface Props {
  installation: InstallationDetailData,
}

const props = defineProps<Props>();

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const installationStore = useInstallationStore();
const newSharedInstallation = reactive({
  installation_id: props.installation?.id ?? '',
  userEmail: '',
});
const loading = ref(false);

if (installationStore.installations.length < 1) {
  await installationStore.fetchInstallations();
}

const emit = defineEmits(['installationShared']);

const onSubmit = async() => {
  loading.value = true;
  try {
    const { data: postResponse } = await customAxios.post<{name: string}>(`/user/${newSharedInstallation.installation_id}/share`, {
      share_to_user: newSharedInstallation.userEmail,
    });
    newSharedInstallation.installation_id = props.installation.id ?? '';
    newSharedInstallation.userEmail = '';
    loading.value = false;
    isOpened.value = false;
    emit('installationShared');
    deployToast(ToastType.SUCCESS, {
      text: t('installation.form.installation-share-success', { user: postResponse.name }),
      timeout: 6000,
    });
  } catch (e: any) {
    loading.value = false;
    if (e?.response?.status === 406) {
      deployToast(ToastType.ERROR, {
        text: t('installation.form.installation-share-user-exists'),
        timeout: 6000,
      });
      return;
    }
    deployToast(ToastType.ERROR, {
      text: t('installation.form.installation-share-fail'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      :style="{margin: 'auto'}"
    >
      <form
        autocomplete="off"
        class="bg-prim-col-1 p-6 rounded-2xl w-[35rem] max-w-[90vw] relative"
        @submit.prevent="onSubmit"
      >
        <h3 class="font-bold text-xl mb-2">
          {{ $t('installation.share-installation') }}
        </h3>
        <p class="text-black/50 dark:text-white/60 text-sm">
          {{ $t('installation.share-installation-desc') }}
        </p>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label
              for="new-device-installation"
              class="text-right min-w-28 w-28"
            >
              {{ $t('installation.title-base') }}
            </Label>
            <v-select
              id="new-shared-installation"
              v-model="newSharedInstallation.installation_id"
              :disabled="Boolean(installation)"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="installationStore.installations.filter(i => i.is_owner)"
              item-title="title"
              item-value="id"
              variant="solo-filled"
              :density="'compact'"
              width="100%"
              name="installation"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-installation-pairing-code"
              class="text-right min-w-28 w-28"
            >
              {{ $t('installation.user-email') }}
            </Label>
            <Input
              id="new-shared-installation-email"
              v-model="newSharedInstallation.userEmail"
              name="email"
              type="email"
              required
            />
          </div>
        </div>
        <div class="flex items-center justify-end gap-2">
          <Button
            type="button"
            variant="default"
            class="bg-gray-400/80 hover:bg-gray-400"
            @click="isOpened = false"
          >
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button type="submit">
            {{ $t('misc.save') }}
          </Button>
        </div>
        <div v-if="loading" class="absolute w-full h-full bg-white/50 left-0 top-0 rounded-2xl">
          <PageLoader :absolute-center="true" />
        </div>
      </form>
    </v-dialog>
  </Teleport>
</template>
