<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted, h } from 'vue';

export default defineComponent({
  props: {
    to: {
      type: Number,
      required: true,
    },
    dp: {
      type: Number,
      default: 0,
    },
    tag: {
      type: String,
      default: 'span',
    },
  },
  setup(props) {
    const previousCount = ref(0);
    const count = ref(0);
    const dirUp = ref(true);
    const intervalTiming = 40;
    let interval: ReturnType<typeof setInterval> | null = null;

    const displayCount = computed(() => {
      let currentCount = count.value;

      if ((dirUp.value && count.value > props.to) || (!dirUp.value && count.value < props.to)) {
        currentCount = props.to;
      }

      return props.dp !== 0 ? currentCount.toFixed(props.dp) : currentCount;
    });

    const increment = computed(() => {
      let inc = Math.ceil(props.to / 20);

      if (props.dp !== 0) {
        inc = parseFloat((Math.abs(props.to - previousCount.value) / 20).toFixed(props.dp));
      }

      return inc <= 0 ? (props.dp !== 0 ? 10 ** (-1 * props.dp) : 1) : inc;
    });

    const tick = () => {
      if (
        (dirUp.value && count.value + increment.value >= props.to) ||
        (!dirUp.value && count.value + increment.value <= props.to)
      ) {
        count.value = props.to;
        if (interval) {
          clearInterval(interval);
        }
        return;
      }

      if (!dirUp.value) {
        count.value = props.dp !== 0
          ? parseFloat((count.value - increment.value).toFixed(props.dp))
          : count.value - increment.value;
      } else {
        count.value = props.dp !== 0
          ? parseFloat((count.value + increment.value).toFixed(props.dp))
          : count.value + increment.value;
      }
    };

    watch(() => props.to, (newValue, oldValue) => {
      previousCount.value = oldValue;
      dirUp.value = newValue >= oldValue;

      if (interval) {
        clearInterval(interval);
      }
      interval = setInterval(tick, intervalTiming);
    });

    onMounted(() => {
      if (props.to < 0) {
        dirUp.value = false;
      }
      interval = setInterval(tick, intervalTiming);
    });

    return () => h(props.tag, {}, displayCount.value);
  },
});
</script>
