<script setup lang="ts">
import { onMounted } from 'vue';
import { Search, X, Check } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { ComponentStateType } from '@/util/types/components';
import type { UserData } from '@/stores/auth-store';
import PageLoader from '@/components/global/PageLoader.vue';
import AdminInstallationsPagination from '../admin/AdminInstallationsPagination.vue';
import { usePaginatedApi } from '@/composables/usePaginatedApi';

interface Props {
  selectedUser: UserData | null;
}

defineProps<Props>();

const emit = defineEmits<{
  'user-selected': [user: UserData];
}>();

const {
  data: users,
  componentState,
  currentPage,
  totalPages,
  totalItems,
  perPage,
  searchTerm,
  paginationLinks,
  handlePageChange,
  handlePerPageChange,
  refresh,
  initialize,
  clearSearch,
} = usePaginatedApi<UserData>({
  endpoint: '/service/users',
  initialPerPage: 10,
  searchParam: 'search',
  errorMessages: {
    fetchError: 'Failed to fetch users',
  },
});

const selectUser = (user: UserData) => {
  emit('user-selected', user);
};

onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="space-y-6">
    <p class="text-muted-foreground">
      {{ $t('admin.new-installation.step2a.description') }}
    </p>

    <div class="relative w-full max-w-md">
      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        v-model="searchTerm"
        type="text"
        :placeholder="$t('admin.new-installation.step2a.search-placeholder')"
        class="pl-10 pr-10"
      />
      <button
        v-if="searchTerm"
        class="absolute inset-y-0 right-0 flex items-center pr-3"
        @click="clearSearch"
      >
        <X class="h-4 w-4 text-muted-foreground hover:text-foreground" />
      </button>
    </div>

    <div
      v-if="componentState === ComponentStateType.LOADING"
      class="flex justify-center py-8"
    >
      <PageLoader />
    </div>

    <!-- Error State -->
    <div
      v-else-if="componentState === ComponentStateType.ERROR"
      class="text-center py-8"
    >
      <p class="text-destructive">
        {{ $t('misc.failed-to-get-data') }}
      </p>
      <Button
        variant="outline"
        class="mt-4"
        @click="refresh"
      >
        {{ $t('misc.retry') }}
      </Button>
    </div>

    <!-- Users Table -->
    <div
      v-else-if="componentState === ComponentStateType.OK"
      class="space-y-4"
    >
      <div class="border dark:border-[var(--color-border)] border-gray-300/70 rounded-lg dark:**:text-white **:text-black">
        <Table>
          <TableHeader>
            <TableRow class="border-gray-300/70">
              <TableHead>{{ $t('misc.name') }}</TableHead>
              <TableHead>{{ $t('misc.surname') }}</TableHead>
              <TableHead>{{ $t('misc.email') }}</TableHead>
              <TableHead class="text-right">
                {{ $t('misc.actions') }}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="user in users"
              :key="user.id"
              :class="{
                'bg-primary/5 border-primary': selectedUser?.id === user.id,
              }"
              class="border-gray-300/70"
            >
              <TableCell class="font-medium">
                {{ user.name ?? '-' }}
              </TableCell>
              <TableCell class="font-medium">
                {{ user.surname ?? '-' }}
              </TableCell>
              <TableCell class="text-muted-foreground">
                {{ user.email }}
              </TableCell>
              <TableCell class="text-right">
                <Button
                  v-if="selectedUser?.id !== user.id"
                  variant="outline"
                  size="sm"
                  @click="selectUser(user)"
                >
                  {{ $t('misc.select') }}
                </Button>
                <div
                  v-else
                  class="flex items-center justify-end text-primary"
                >
                  <Check class="h-4 w-4 mr-2" />
                  {{ $t('misc.selected') }}
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      <AdminInstallationsPagination
        :current-page="currentPage"
        :total-pages="totalPages"
        :has-prev="!!paginationLinks.prev"
        :has-next="!!paginationLinks.next"
        :per-page="perPage"
        :total-items="totalItems"
        @page-change="handlePageChange"
        @per-page-change="handlePerPageChange"
      />

      <!-- No Results -->
      <div
        v-if="users.length === 0"
        class="text-center py-8"
      >
        <p class="text-muted-foreground">
          {{ searchTerm ? $t('admin.new-installation.step2a.no-results') : $t('admin.new-installation.step2a.no-users') }}
        </p>
      </div>
    </div>

    <!-- Selected User Summary -->
    <div
      v-if="selectedUser"
      class="mt-6 p-4 dark:bg-primary/5 bg-gray-100 border border-primary rounded-lg"
    >
      <div class="flex items-center space-x-2">
        <Check class="h-4 w-4 text-primary" />
        <span class="font-medium">{{ $t('misc.selected-user') }}:</span>
      </div>
      <div class="mt-2 ml-6">
        <p class="font-medium">
          {{ [selectedUser.name, selectedUser.surname].filter(Boolean).join(' ') }}
        </p>
        <p class="text-sm text-muted-foreground">
          {{ [selectedUser.email, selectedUser.phone].filter(Boolean).join(', ') }}
        </p>
      </div>
    </div>
  </div>
</template>
