<script setup lang="ts">
import MiniChart from '@/components/charts/MiniChart.vue';
import { routeMap } from '@/router/routes.ts';
import { type AnyDevice, type DeviceMetric } from '@/util/types/devices/device-types.ts';
import { deviceTypeDetailAllowed, deviceTypeTitleMap, deviceValueMap, installationBlocks } from '@/util/types/devices/device-detail.ts';
import { computed, ref, watch } from 'vue';
import type { InstallationDetailData } from '../types/installation-types';
import type { TuyaDevice } from '../types/tuya-types';
import { DateTime } from 'luxon';
// import { customAxios } from '@/util/axios';

interface Props {
  installationDetail: InstallationDetailData,
  tuyaDevices: TuyaDevice[],
}

const props = defineProps<Props>();

const apiData = ref<AnyDevice[]>([]);

const deviceWorker = new Worker(new URL('@/workers/energetic-blocks-data.worker.ts', import.meta.url), {
  type: 'module',
});

deviceWorker.onmessage = (event: MessageEvent<AnyDevice[]>) => {
  apiData.value = event.data;
  // console.log(event.data[0]['id']);
  // customAxios.get(`/tuya/${props.installationDetail.id}/devices/${event.data[0]['id']}/energy-stats`);
};

watch(
  () => [props.installationDetail, props.tuyaDevices],
  () => {
    if (!deviceWorker.postMessage) {
      return;
    }
    deviceWorker.postMessage({
      installationDetail: props.installationDetail ? JSON.parse(JSON.stringify(props.installationDetail)) : undefined,
      tuyaDevices: props.tuyaDevices ? JSON.parse(JSON.stringify(props.tuyaDevices)) : undefined,
    });
  },
  { deep: true, immediate: true }
);

const groupedDevices = computed(() => installationBlocks.map(block => ({
  title: block.title,
  subtitle: block.subtitle,
  devices: apiData.value.filter(device => block.deviceTypes.includes(device.type)),
})));
</script>

<template>
  <div class="w-full mt-4">
    <h2 class="font-bold text-2xl">
      {{ $t('installation.energetic-blocks') }}
    </h2>
    <div class="w-full grid gap-4 mt-4">
      <div v-for="block in groupedDevices" :key="block.title" class="rounded-xl bg-prim-col-foreground-1 dark:bg-prim-col-foreground-1/70">
        <div class="flex flex-row items-center justify-between p-6">
          <div>
            <h3 class="text-2xl font-semibold leading-none tracking-tight">
              {{ block.title }}
            </h3>
            <p class="text-sm text-muted-foreground">
              {{ block.subtitle }}
            </p>
          </div>
        </div>

        <div class="px-4 pb-4 grid gap-2">
          <component
            :is="deviceTypeDetailAllowed.includes(device.type) ? 'router-link' : 'div'"
            v-for="device in block.devices"
            :key="device.id"
            :to="{ name: routeMap.home.children.deviceDetail.name, params: { installationId: installationDetail.id, deviceId: device.id } }"
            class="flex items-center gap-4 p-4 dark:bg-prim-col-foreground-1/90 dark:hover:bg-prim-col-foreground-2/70 bg-prim-col-foreground-2/30 hover:bg-prim-col-foreground-2/50 rounded-lg transition-colors duration-150"
          >
            <div class="flex items-center gap-2 w-full flex-wrap">
              <div class="flex flex-col gap-1 xl:w-52 xl:max-w-52 flex-[1_1_100%] xl:flex-none">
                <div>
                  <div class="text-xs bg-prim-col-foreground-contrast dark:text-prim-col-1 text-black w-fit px-1 py-0.5 rounded-md mb-1.5">
                    {{ deviceTypeTitleMap[device.type] }}
                  </div>
                  <div class="text-xs">
                    {{ device.vendor }}
                  </div>
                  <div class="font-medium line-clamp-1 break-all">
                    {{ device.model }}
                  </div>
                </div>

                <div class="flex items-center gap-1">
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="device.online === true
                      ? 'bg-green-500'
                      : device.online === false
                        ? 'bg-orange-400'
                        : (device.lastUpdated && DateTime.now().diff(DateTime.fromISO(device.lastUpdated), 'minutes').minutes <= 5
                          ? 'bg-green-500'
                          : 'bg-orange-400')"
                  />
                  <div class="text-xs">
                    <template v-if="device.online === true">
                      {{ $t('installation.device-online') }}
                    </template>
                    <template v-else-if="device.online === false">
                      {{ $t('installation.device-offline') }}
                    </template>
                    <template v-else-if="device.lastUpdated">
                      <template v-if="DateTime.now().diff(DateTime.fromISO(device.lastUpdated), 'minutes').minutes <= 5">
                        {{ $t('installation.device-online') }}
                      </template>
                      <template v-else>
                        {{ $t('installation.last-activity') }}: {{ DateTime.fromISO(device.lastUpdated).toFormat('dd.MM.yyyy HH:mm') }}
                      </template>
                    </template>
                    <template v-else>
                      {{ $t('misc.unknown-state') }}
                    </template>
                  </div>
                </div>
              </div>

              <div v-for="deviceValue in deviceValueMap[device.type].filter(deviceValue => device[deviceValue.key as keyof AnyDevice])" :key="deviceValue.key" class="flex flex-col flex-[1_1_100%] sm:flex-none sm:w-fit sm:min-w-40">
                <div class="dark:bg-white/15 bg-black/15 rounded-t-md px-3 py-1.5">
                  <h3 class="font-light text-xs">
                    {{ deviceValue.title }}
                  </h3>
                </div>
                <div class="dark:bg-white/10 bg-black/10 px-2 py-3 rounded-b-md">
                  <div class="font-medium dark:text-white text-black">
                    {{ (device[deviceValue.key as keyof AnyDevice] as unknown as DeviceMetric)?.value }} {{ (device[deviceValue.key as keyof AnyDevice] as unknown as DeviceMetric)?.unit ?? '' }}
                  </div>
                </div>
              </div>

              <div v-if="false" class="xl:ml-auto flex-[1_1_100%] sm:flex-none sm:w-40 h-[76px] block">
                <MiniChart />
              </div>

              <div v-if="false" class="flex flex-col flex-[1_1_100%] sm:flex-none">
                <div class="dark:bg-white/15 bg-black/15 rounded-t-md px-3 py-1.5">
                  <h3 class="font-light text-xs whitespace-nowrap">
                    {{ $t('installation.last-update') }}
                  </h3>
                </div>
                <div class="dark:bg-white/10 bg-black/5 px-2 py-3 rounded-b-md">
                  <div class="font-medium dark:text-white text-black">
                    {{ device.lastUpdated ?? '-' }}
                  </div>
                </div>
              </div>
            </div>
          </component>
          <div v-if="block.devices && block.devices.length === 0" class="px-2 text-gray-500">
            {{ $t('installation.no-supported-devices') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-muted-foreground {
  color: #6b7280;
}

.bg-card {
  background-color: white;
}

.text-card-foreground {
  color: #111827;
}
</style>
