<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { customAxios } from '@/util/axios';
import { resetBreadcrumb } from '@/util/facades/breadcrumb';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';
import { ChangelogDeviceType } from './changelog/types/ChangelogDeviceType';
import WebViewShadow from '@/components/global/WebViewShadow.vue';

const componentState = ref(ComponentStateType.OK);
const { t } = useI18n();
const selectedChangelog = ref(ChangelogDeviceType.WEB);
const changelogContent = ref<string>();

resetBreadcrumb();

const getChangelogRequest = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    const content = await customAxios.get(`/changelog/${selectedChangelog.value}`);
    changelogContent.value = content.data;
    componentState.value = ComponentStateType.OK;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.no-data'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

const selectChangelog = (deviceType: ChangelogDeviceType) => {
  selectedChangelog.value = deviceType;
  getChangelogRequest();
};

getChangelogRequest();

</script>

<template>
  <div class="p-4 relative">
    <section
      class="flex flex-col w-180 max-w-[75vw]"
    >
      <h2 class="font-bold text-2xl">
        {{ $t('settings.choose-platform') }}
      </h2>
      <ul class="select-none w-full flex items-center *:flex-1 *:text-center *:font-medium *:py-1.5 *:px-3 *:text-lg *:cursor-pointer *:transition-all *:duration-100 bg-black/15 p-1 rounded-2xl">
        <li class="rounded-l-xl" :class="[selectedChangelog === ChangelogDeviceType.WEB ? 'bg-prim-col-selected-1' : 'hover:bg-prim-col-foreground-2']" @click="selectChangelog(ChangelogDeviceType.WEB)">
          WEB
        </li>
        <li class="uppercase" :class="[selectedChangelog === ChangelogDeviceType.MOBILE_APPS ? 'bg-prim-col-selected-1' : 'hover:bg-prim-col-foreground-2']" @click="selectChangelog(ChangelogDeviceType.MOBILE_APPS)">
          {{ $t('misc.apps') }}
        </li>
        <li class="rounded-r-xl" :class="[selectedChangelog === ChangelogDeviceType.HARDWARE ? 'bg-prim-col-selected-1' : 'hover:bg-prim-col-foreground-2']" @click="selectChangelog(ChangelogDeviceType.HARDWARE)">
          HARDWARE
        </li>
      </ul>
      <WebViewShadow v-if="changelogContent" :html="changelogContent" />
    </section>
    <div
      v-if="componentState === ComponentStateType.LOADING"
      class="absolute bg-white/80 w-full h-full left-0 top-0"
    >
      <PageLoader :absolute-center="true" />
    </div>
  </div>
</template>
