{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "typings/*.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "strict": true, "importHelpers": true, "forceConsistentCasingInFileNames": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "lib": ["DOM", "ESNext"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}