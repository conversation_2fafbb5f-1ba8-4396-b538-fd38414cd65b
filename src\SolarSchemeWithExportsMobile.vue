<script setup lang="ts">
import { defineExpose, ref } from 'vue';
import { isLightModeEnabled } from '@/composables/theme.ts';
// TODO: In Illustrator, create path on each chart, make it opposite direction using  Object -> Path -> Reverse Path Direction
// TODO VUE: Make refs on path (chart) and circle (text) elements
// to auto-assign css in illustrator, 1. Create a new style in the Graphic Styles panel
// 2. Double click the new style and give it a custom name, like "my-style"
// 3. Apply that style to one or more elements
// 4. Export the SVG

const photovoltaicsGroup = ref<SVGElement>();
const batteryGroup = ref<SVGElement>();
const gridGroup = ref<SVGElement>();
const heatingGroup = ref<SVGElement>();
const tuvGroup = ref<SVGElement>();
const householdGroup = ref<SVGElement>();
const wallBoxGroup = ref<SVGElement>();

const photovoltaicsCurrentPowerCenter = ref<SVGCircleElement>();
const photovoltaicsMainCenter = ref<SVGCircleElement>();
const photovoltaicsArrowsOut = ref<SVGElement>();
const photovoltaicsCurrentPowerChart = ref<SVGPathElement>();

const batteryStateCenter = ref<SVGCircleElement>();
const batteryMainCenter = ref<SVGCircleElement>();
const batteryArrowsIn = ref<SVGElement>();
const batteryArrowsOut = ref<SVGElement>();
const batteryStateChart = ref<SVGPathElement>();

const gridMainCenter = ref<SVGCircleElement>();
const gridArrowsIn = ref<SVGElement>();
const gridArrowsOut = ref<SVGElement>();

const heatingMainCenter = ref<SVGCircleElement>();
const heatingArrowsIn = ref<SVGElement>();

const tuvStateCurrentTemperatureCenter = ref<SVGCircleElement>();
const tuvMainCenter = ref<SVGCircleElement>();
const tuvArrowsIn = ref<SVGElement>();
const tuvStateChart = ref<SVGPathElement>();

const householdMainCenter = ref<SVGCircleElement>();
const householdArrowsIn = ref<SVGElement>();

const wallBoxMainCenter = ref<SVGCircleElement>();
const wallboxArrowsIn = ref<SVGElement>();

defineExpose({
  wholeGroups: {
    photovoltaicsGroup,
    batteryGroup,
    gridGroup,
    heatingGroup,
    tuvGroup,
    householdGroup,
    wallBoxGroup,
  },
  dynamicTextCenterPoints: {
    photovoltaics: {
      currentPower: photovoltaicsCurrentPowerCenter,
      main: photovoltaicsMainCenter,
    },
    battery: {
      batteryState: batteryStateCenter,
      batteryMain: batteryMainCenter,
    },
    grid: {
      main: gridMainCenter,
    },
    heating: {
      main: heatingMainCenter,
    },
    tuv: {
      tuvStateCurrentTemperatureCenter: tuvStateCurrentTemperatureCenter,
      main: tuvMainCenter,
    },
    household: {
      main: householdMainCenter,
    },
    wallBox: {
      main: wallBoxMainCenter,
    },
  },
  arrows: {
    photovoltaics: {
      out: photovoltaicsArrowsOut,
    },
    battery: {
      in: batteryArrowsIn,
      out: batteryArrowsOut,
    },
    grid: {
      in: gridArrowsIn,
      out: gridArrowsOut,
    },
    heating: {
      in: heatingArrowsIn,
    },
    tuv: {
      in: tuvArrowsIn,
    },
    household: {
      in: householdArrowsIn,
    },
    wallBox: {
      in: wallboxArrowsIn,
    },
  },
  charts: {
    photovoltaics: {
      photovoltaicsCurrentPowerChart,
    },
    battery: {
      batteryStateChart,
    },
    tuv: {
      tuvStateChart,
    },
  }
});
</script>

<template>
  <svg
    id="Layer_1"
    version="1.1"
    :class="isLightModeEnabled ? '' : 'dark'"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 101.7990417 222.71492"
    style="enable-background:new 0 0 101.7990417 222.71492;"
    xml:space="preserve"
  >
    <line
      class="st0"
      x1="51.7911453"
      y1="37.5571098"
      x2="51.7911453"
      y2="171.4996185"
    />
    <g id="grid-group" ref="gridGroup">
      <g>
        <g>
          <g>
            <rect
              x="54.3070488"
              y="205.3500214"
              class="st1"
              width="0.0901255"
              height="1.0697789"
            />
            <path
              class="st1"
              d="M55.7005806,208.6001434v-0.795166l-2.9596291-0.8336487l-0.0112953-0.0837708
					c-0.0621223-0.4637146-0.1226768-0.9280701-0.1797791-1.3943176l-0.0175705-0.1443176h1.8614922v-0.6682892l-2.006134-0.5603638
					l-0.0094109-0.0862885c-0.070282-0.6409912-0.1358566-1.2845001-0.1964111-1.9298859l-0.0100403-0.1101379H51.087162
					l-0.0103531,0.1101379c-0.0605545,0.6472626-0.1264458,1.2929688-0.1970367,1.9358521l-0.0094147,0.0865936
					l-2.0055046,0.5543976v0.667984v1.0697784h0.0901222v-1.0697784h1.7716827l-0.0175705,0.1443176
					c-0.057415,0.4662476-0.1179695,0.930603-0.1800919,1.3943176l-0.0112953,0.0837708l-2.9593163,0.8336487v0.816391h2.7252579
					l-0.0222778,0.1477661c-0.0847511,0.5627594-0.1759377,1.1208801-0.2681389,1.6780243h0.2447662
					c0.0751419-0.4549408,0.1519737-0.9088898,0.2219772-1.3667755l0.0401611-0.261673l1.0771141,1.151474l-0.4418449,0.4769745
					h0.1038933l0.3900337-0.4211273l0.3900719,0.4211273h0.1038513l-0.4418411-0.4769745l1.0771141-1.151474l0.0401611,0.261673
					c0.0702019,0.4578857,0.1469116,0.9118347,0.2219772,1.3667755h0.2450829
					c-0.0921669-0.5570221-0.1833496-1.1150665-0.2681427-1.6780243l-0.022274-0.1477661h2.6365013v1.0485535h0.0901642v-1.0697784
					H55.7005806z M54.3178711,204.738678v0.5339966h-0.3589325l-0.575737-0.7947388L54.3178711,204.738678z M53.8641853,205.2726746
					h-1.2794838l0.7002983-0.7997589L53.8641853,205.2726746z M53.2191124,204.4318237l-0.6999855,0.7994385l-0.1201668-1.0281677
					L53.2191124,204.4318237z M52.3638191,205.9833221c0.0721664,0.5719757,0.1493492,1.1414337,0.2293549,1.7090149
					l0.0831451,0.5923615l-1.0021248-1.5875854l0.6453857-1.0911865L52.3638191,205.9833221z M51.1329689,203.9467621
					c0.0577316-0.5299377,0.1135788-1.0604858,0.1644058-1.5932312l0.0112953-0.1164093h0.6413116l0.0112953,0.1164093
					c0.0511398,0.5336914,0.106987,1.0654907,0.1650314,1.5966797l0.0156898,0.1430664l-1.0244026-0.0050201L51.1329689,203.9467621
					z M51.0297432,204.8553925c0.0232201-0.1920166,0.0464363-0.3840332,0.0680847-0.5766754l0.0125504-0.1145325h1.0378952
					l0.0125504,0.1145325c0.0200806,0.1800842,0.0420418,0.3592377,0.0636902,0.5387115l0.0545921,0.455246h-1.2995605
					L51.0297432,204.8553925z M50.9691887,205.3489227h1.3202744l0.0153732,0.1312714l-0.6755104,1.1428833l-0.6753922-1.1530914
					C50.9633484,205.3955536,50.9691887,205.3489227,50.9691887,205.3489227z M48.9410934,205.2726746v-0.5339966
					l0.9346695-0.2607422l-0.575737,0.7947388H48.9410934z M49.3944664,205.2726746l0.5788727-0.7997589l0.7012367,0.7997589
					H49.3944664z M50.0395393,204.4318237l0.8201523-0.2287292l-0.1201668,1.0281677L50.0395393,204.4318237z
					 M50.6654778,207.692337c0.0619659-0.437912,0.208725-1.5921783,0.272728-2.0980225l0.6462517,1.1031189l-1.0027504,1.5875854
					L50.6654778,207.692337z M51.6315231,206.7661438l0.1110687,0.1826019l1.0077705,1.5963745h-2.2420731L51.6315231,206.7661438z
					 M47.6346283,208.5451202v-0.6820984l1.3240356-0.3733673l-0.8091698,1.0554657H47.6346283z M48.2458153,208.5451202
					l0.8176422-1.066452l1.1436272,1.066452H48.2458153z M50.299015,208.5262909l-1.1643372-1.0858917l1.372982-0.3868561
					l-0.0266685,0.194519c-0.0476913,0.3460693-0.0963211,0.6912079-0.1465225,1.0353851L50.299015,208.5262909z
					 M51.633091,209.9099426l-0.0037651-0.0037689l-0.0037651,0.0037689l-1.2051239-1.2885742h2.4180908L51.633091,209.9099426z
					 M52.9599495,208.5259857l-0.0354538-0.242218c-0.0505142-0.3444977-0.099144-0.6896362-0.1465225-1.0357056
					l-0.0266685-0.194519l1.372982,0.3868561L52.9599495,208.5259857z M53.0515671,208.5451202l1.1433144-1.066452
					l0.8185806,1.066452H53.0515671z M54.2999878,207.4896545l1.3243523,0.3733673v0.6820984h-0.5148697L54.2999878,207.4896545z"
            />
            <rect
              x="47.5576782"
              y="208.6238708"
              class="st1"
              width="0.0901255"
              height="1.0697789"
            />
          </g>
          <g>
            <path
              class="st2"
              d="M49.1923218,205.8610382h-0.5787048c-0.0690384,0-0.1250381-0.0559998-0.1250381-0.1250458
					c0-0.0690155,0.0559998-0.1250305,0.1250381-0.1250305h0.5787048c0.0690193,0,0.1250381,0.056015,0.1250381,0.1250305
					C49.3173599,205.8050385,49.2613411,205.8610382,49.1923218,205.8610382z"
            />
            <path
              class="st2"
              d="M54.6452026,205.8610382h-0.5787926c-0.0690346,0-0.1250343-0.0559998-0.1250343-0.1250458
					c0-0.0690155,0.0559998-0.1250305,0.1250343-0.1250305h0.5787926c0.0690346,0,0.1250381,0.056015,0.1250381,0.1250305
					C54.7702408,205.8050385,54.7142372,205.8610382,54.6452026,205.8610382z"
            />
            <path
              class="st2"
              d="M49.1923218,206.2321625h-0.5787048c-0.0690384,0-0.1250381-0.056015-0.1250381-0.1250305
					c0-0.069046,0.0559998-0.1250458,0.1250381-0.1250458h0.5787048c0.0690193,0,0.1250381,0.0559998,0.1250381,0.1250458
					C49.3173599,206.1761475,49.2613411,206.2321625,49.1923218,206.2321625z"
            />
            <path
              class="st2"
              d="M54.6452026,206.2321625h-0.5787926c-0.0690346,0-0.1250343-0.056015-0.1250343-0.1250305
					c0-0.069046,0.0559998-0.1250458,0.1250343-0.1250458h0.5787926c0.0690346,0,0.1250381,0.0559998,0.1250381,0.1250458
					C54.7702408,206.1761475,54.7142372,206.2321625,54.6452026,206.2321625z"
            />
            <path
              class="st2"
              d="M47.8857803,209.1140442h-0.5787048c-0.0690384,0-0.1250381-0.0559998-0.1250381-0.1250458
					c0-0.0690155,0.0559998-0.1250305,0.1250381-0.1250305h0.5787048c0.0690231,0,0.1250381,0.056015,0.1250381,0.1250305
					C48.0108185,209.0580444,47.9548035,209.1140442,47.8857803,209.1140442z"
            />
            <path
              class="st2"
              d="M55.9516602,209.1140442h-0.5787086c-0.0690193,0-0.1250381-0.0559998-0.1250381-0.1250458
					c0-0.0690155,0.0560188-0.1250305,0.1250381-0.1250305h0.5787086c0.0690346,0,0.1250343,0.056015,0.1250343,0.1250305
					C56.0766945,209.0580444,56.0206947,209.1140442,55.9516602,209.1140442z"
            />
            <path
              class="st2"
              d="M47.8857803,209.4851685h-0.5787048c-0.0690384,0-0.1250381-0.0559998-0.1250381-0.1250305
					c0-0.069046,0.0559998-0.1250458,0.1250381-0.1250458h0.5787048c0.0690231,0,0.1250381,0.0559998,0.1250381,0.1250458
					C48.0108185,209.4291687,47.9548035,209.4851685,47.8857803,209.4851685z"
            />
            <path
              class="st2"
              d="M55.9516602,209.4851685h-0.5787086c-0.0690193,0-0.1250381-0.0559998-0.1250381-0.1250305
					c0-0.069046,0.0560188-0.1250458,0.1250381-0.1250458h0.5787086c0.0690346,0,0.1250343,0.0559998,0.1250343,0.1250458
					C56.0766945,209.4291687,56.0206947,209.4851685,55.9516602,209.4851685z"
            />
          </g>
        </g>
        <text transform="matrix(1 0 0 1 45.5452461 217.8095703)" class="st3 st4 st5">Sieť</text>
      </g>
      <path class="st0" d="M51.7894745,170.0594177v19.9750061" />
      <path
        class="actextrectangle"
        d="M63.8785515,200.1546326H39.4815331c-1.1000023,0-2-0.8999939-2-2v-6.1202087
		c0-1.0999908,0.8999977-2,2-2h24.3970184c1.1000023,0,1.9999962,0.9000092,1.9999962,2v6.1202087
		C65.8785477,199.2546387,64.9785538,200.1546326,63.8785515,200.1546326z"
      />
      <g id="dynamic-text_2_">

        <circle
          id="grid-center"
          ref="gridMainCenter"
          class="st6"
          cx="51.6800423"
          cy="195.0945282"
          r="0.1094337"
        />
      </g>
      <g id="arrows-out_2_" ref="gridArrowsOut" class="mobile-scheme vertical">
        <polygon class="st7" points="51.7894745,174.5174866 49.857666,178.686142 53.721283,178.686142 		" />
        <polygon class="st7" points="51.7894745,179.4113312 49.857666,183.5799866 53.721283,183.5799866 		" />
        <polygon class="st7" points="51.7894745,184.3051758 49.857666,188.4738312 53.721283,188.4738312 		" />
      </g>
      <g id="arrows-in_2_" ref="gridArrowsIn" class="mobile-scheme vertical">
        <polygon class="st7" points="51.7894745,178.7594452 53.721283,174.5907898 49.857666,174.5907898 		" />
        <polygon class="st7" points="51.7894745,183.6532898 53.721283,179.4846344 49.857666,179.4846344 		" />
        <polygon class="st7" points="51.7894745,188.5471344 53.721283,184.378479 49.857666,184.378479 		" />
      </g>
    </g>
    <g id="wall-box-group" ref="wallBoxGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 6.7564707 155.5214844)" class="st3 st4 st5">Wall Box</text>
        </g>
        <g>
          <path
            class="st1"
            d="M22.9689045,144.3547668L22.9689045,144.3547668l-0.0004139-0.4660492
				c0-0.0001831-0.0000992-0.0003052-0.0000992-0.0004883c0-0.0001678,0.0000896-0.0003204,0.0000896-0.000473l-0.0000114-0.0013885
				c0-0.0000763-0.0000458-0.0001526-0.0000458-0.0002289c0-0.000061,0.0000343-0.0001373,0.0000343-0.0001984
				c-0.0038605-0.1927948-0.0077324-0.38591-0.0141258-0.5792389c-0.006403-0.1934814-0.0153179-0.3867645-0.0261059-0.5798035
				c-0.0108223-0.1935425-0.0235424-0.3869019-0.0381355-0.5800323c-0.0146599-0.1938171-0.0312157-0.3874817-0.0502834-0.5807648
				c-0.0191231-0.1938019-0.0407219-0.3868256-0.0622883-0.5795288c-0.0000343-0.000351-0.0002575-0.0006104-0.0003014-0.000946
				c-0.0000458-0.000351,0.0000992-0.0006561,0.0000553-0.0009918l-0.006237-0.0449066l-0.0001888-0.0012207
				c-0.0032024-0.0205688-0.0070515-0.040863-0.0113583-0.0608521c-0.0052223-0.0242615-0.011179-0.0484467-0.0179291-0.0724335
				c-0.0068398-0.0243225-0.0144711-0.0484009-0.02285-0.0721741c-0.0068283-0.0193787-0.0141582-0.038559-0.0219898-0.0575714
				l-0.0005798-0.001358l-0.017807-0.0408478l-0.0012722-0.0027466l-0.0204163-0.0419769l-0.0013847-0.0027008l-0.0209522-0.0389099
				l-0.0014057-0.0025024l-0.0211983-0.0359497l-0.0014057-0.0023041l-0.0211754-0.0330658l-0.0015736-0.0023499
				l-0.0259838-0.0373383l-0.0017509-0.0024109l-0.0265884-0.0350952l-0.0017853-0.0022583l-0.02701-0.0328674l-0.0018082-0.0021362
				l-0.0272675-0.0307312l-0.0009136-0.0010071c-0.0212212-0.0230255-0.0433559-0.0452271-0.0663395-0.0664978
				c-0.0219803-0.02034-0.0447178-0.0398102-0.0681801-0.0584106c-0.0245113-0.019455-0.0498047-0.0379028-0.0757999-0.0553284
				c-0.0251598-0.0168762-0.0509872-0.0327911-0.0774403-0.0476837c-0.0359821-0.0202637-0.0729446-0.0385284-0.1106319-0.0549164
				c-0.0373421-0.0162354-0.0758781-0.0308533-0.1156311-0.0429688c-0.0397396-0.0121002-0.0786781-0.0211487-0.1160202-0.0299072
				c-0.0000229,0-0.0000553,0.0000153-0.0000782,0c-0.0000229,0-0.0000439-0.0000153-0.0000668-0.0000305
				c-0.0005016-0.0001221-0.0009937,0.0000763-0.0014954-0.0000458c-0.0005016-0.0001068-0.0008698-0.0004578-0.0013714-0.0005493
				c-0.1146049-0.0217743-0.2298222-0.0437012-0.3461895-0.0627289c-0.1145363-0.0187225-0.2294083-0.0345001-0.3443565-0.0481415
				c-0.1142483-0.0135498-0.2287731-0.0250092-0.343544-0.0341339c-0.1150169-0.00914-0.2302132-0.0159454-0.3454857-0.020462
				c-0.1134205-0.0044403-0.2269421-0.0066833-0.3404636-0.0066833h-0.0037823
				c-0.1147823,0.0000458-0.2295322,0.0023804-0.3441925,0.0069733c-0.1146927,0.0046082-0.2293072,0.0114594-0.3437443,0.0206299
				c-0.1141682,0.0091705-0.2281017,0.0206146-0.3417473,0.0341492c-0.1143131,0.0136261-0.22855,0.0293579-0.3424606,0.0480042
				c-0.1156635,0.0189514-0.2301903,0.040741-0.3441029,0.0623932c-0.0005016,0.0000916-0.0008698,0.0004425-0.0013714,0.0005493
				s-0.0009708-0.000061-0.0014725,0.0000458c0,0-0.0000343,0.0000305-0.0000458,0.0000305c-0.000021,0-0.0000439,0-0.0000668,0
				c-0.0370064,0.0086365-0.0755653,0.0175629-0.1149273,0.0294342c-0.0402756,0.0121613-0.0793476,0.0268402-0.1172256,0.0431671
				c-0.0387478,0.0167084-0.0767479,0.0353546-0.1137218,0.056076c-0.026453,0.0148163-0.0523033,0.0306549-0.0774956,0.0474091
				c-0.0259972,0.0173035-0.0513115,0.035614-0.0758781,0.0548859c-0.0238209,0.0186768-0.0469265,0.038269-0.0692406,0.0587006
				c-0.0233631,0.0213776-0.0458546,0.0436859-0.0674324,0.0668335c-0.0202503,0.0217285-0.0396957,0.04422-0.0582733,0.0673828
				c-0.0198479,0.024765-0.038702,0.050293-0.0565205,0.0765533c-0.0163784,0.0241241-0.0318642,0.0488586-0.0464344,0.0741272
				c-0.0162888,0.0282288-0.0314178,0.0571442-0.0453415,0.0866089l-0.0005245,0.0011597l-0.0158882,0.0351715l-0.0011044,0.0025787
				l-0.0177956,0.043808l-0.0005684,0.0014496c-0.0070953,0.0185547-0.0137234,0.0372925-0.0198708,0.0561829
				c-0.0070171,0.0215302-0.0134335,0.0432739-0.0192242,0.0652008c-0.0051975,0.0196991-0.0098724,0.0394897-0.0140686,0.0593414
				l-0.0003128,0.0015717l-0.0087013,0.0461578l-0.0004578,0.0027008l-0.005434,0.0361786
				c-0.0000439,0.0003357,0.0000896,0.0006104,0.0000458,0.0009308c-0.0000458,0.0003204-0.000246,0.0005646-0.0002899,0.0008698
				c-0.0241108,0.1928101-0.0482883,0.3860474-0.0689945,0.5803223c-0.0206184,0.1934509-0.0377445,0.3873596-0.0522041,0.5814209
				c-0.0144138,0.1933136-0.026207,0.3869019-0.0353661,0.5807037c-0.0091381,0.1934052-0.0156651,0.3871307-0.0186768,0.5810547
				c-0.0018864,0.1214905-0.0023994,0.2428436-0.0023994,0.3640442c0,0.071991,0.0001793,0.1439362,0.0003567,0.2158051l0,0
				l0.0003014,0.4658356c-0.0001678,0.0708313-0.0003452,0.1417236-0.0003452,0.2126617
				c0,0.1222229,0.0005131,0.2446136,0.0024319,0.367157c0.0030346,0.193924,0.009573,0.3876343,0.0187206,0.5810242
				c0.009182,0.1938324,0.0209866,0.3874359,0.0354118,0.5807648c0.0144825,0.1940918,0.0316086,0.3880005,0.052248,0.5814667
				c0.0207195,0.1942291,0.0448952,0.3874359,0.0690289,0.5802002c0.0000439,0.0003662,0.0002785,0.0006256,0.0003223,0.0009918
				c0.0000572,0.0003662-0.0000992,0.0006866-0.0000439,0.0010529l0.0067158,0.0436707l0.0001907,0.0011749
				c0.0035248,0.0207214,0.0076866,0.041153,0.0123062,0.0613098c0.0057335,0.0250244,0.012249,0.049942,0.0195904,0.0746613
				c0.0071182,0.0240021,0.0149956,0.04776,0.0236092,0.071228c0.0071392,0.0194702,0.0147934,0.0387573,0.0229378,0.0578461
				l0.0006142,0.0013885l0.0191002,0.0423126l0.0013275,0.0027618l0.0211315,0.042038l0.0014057,0.0026703l0.0216675,0.039032
				l0.0014267,0.0024719l0.0219135,0.0361176l0.0014381,0.0022736l0.0219021,0.0332947l0.0015945,0.0023193l0.0266991,0.0373993
				l0.0017738,0.0023651l0.0273018,0.0351562l0.0018063,0.0022278l0.0277138,0.032959l0.0018311,0.0020905l0.0279579,0.0307922
				l0.000927,0.0010071c0.021677,0.0230103,0.044281,0.045166,0.0677223,0.066391
				c0.0224133,0.0203094,0.0456085,0.0397644,0.0695076,0.0583038c0.0249691,0.0193939,0.0506973,0.0377808,0.0771275,0.05513
				c0.0255947,0.0167999,0.051857,0.0326385,0.0787239,0.0474243c0.0365276,0.0201416,0.0740261,0.0382385,0.1122379,0.0544739
				c0.0378551,0.0160828,0.0769043,0.0305176,0.117136,0.0424347c0.0402431,0.0119019,0.0796604,0.0207672,0.1175041,0.0293274
				c0.0000343,0.0000153,0.0000572-0.0000153,0.0000896,0c0.0000343,0.0000153,0.0000668,0.0000305,0.0001011,0.0000458
				c0.0005016,0.0001068,0.0009708-0.000061,0.0014725,0.0000305c0.0005016,0.0001068,0.0008698,0.0004578,0.0013828,0.0005493
				c0.1251469,0.0227661,0.2509422,0.0456696,0.377964,0.0652161c0.124979,0.0192261,0.2503166,0.03508,0.3757305,0.0484619
				c0.1251144,0.0133362,0.2505283,0.0242615,0.3761673,0.0324554c0.1254597,0.008194,0.2510853,0.0136871,0.3767471,0.0165558
				c0.0682678,0.0015564,0.136549,0.0023346,0.2048302,0.0023346c0.0591869,0,0.1183739-0.0005798,0.1775608-0.0017548
				c0.1279259-0.002533,0.255806-0.007782,0.3835201-0.0158234c0.1279812-0.0080719,0.2557163-0.0189362,0.3831501-0.0323334
				c0.1276798-0.0134125,0.25527-0.0294189,0.3824921-0.0488892c0.1292992-0.0198212,0.2573566-0.0431061,0.3847466-0.0662384
				c0.0005131-0.0000916,0.0008812-0.0004578,0.0013847-0.0005493c0.0005016-0.0001068,0.0009594,0.0000763,0.001461-0.0000305
				c0.0000324-0.0000153,0.0000668-0.0000458,0.0001011-0.0000458c0.0000324-0.0000153,0.0000553,0,0.0000877,0
				c0.0373096-0.0084229,0.0761585-0.0171509,0.1158314-0.0288239c0.0405884-0.0119629,0.0799847-0.026474,0.1181736-0.0426788
				c0.0390606-0.0165558,0.0773735-0.03508,0.1146603-0.0557098c0.0266876-0.0147705,0.0527725-0.0305481,0.0781994-0.047287
				c0.0262299-0.0172729,0.0517902-0.0355682,0.07658-0.0548401c0.024044-0.018692,0.0473614-0.0382843,0.0698986-0.0587463
				c0.0235634-0.0214081,0.0462799-0.0437317,0.068058-0.0669403c0.020462-0.0217896,0.0400982-0.0443268,0.0588627-0.0675812
				c0.0200386-0.0248413,0.0390835-0.0504761,0.0570679-0.0768433c0.0165462-0.0242157,0.0321884-0.049057,0.0469036-0.0744324
				c0.0164337-0.0283661,0.0317078-0.0573883,0.0457554-0.0869751l0.0005455-0.0011597l0.0160999-0.0355225l0.0011272-0.0026093
				l0.0179615-0.0440521l0.0005703-0.0014496c0.0071507-0.018631,0.0138226-0.0374298,0.0200272-0.0563965
				c0.0070839-0.0216675,0.0135651-0.0435486,0.0194016-0.0656281c0.0052547-0.0198669,0.009985-0.0398254,0.0142136-0.059845
				l0.0003128-0.0015717l0.0087471-0.0463562l0.0004559-0.0027161l0.005434-0.0361481
				c0.0000458-0.0003204-0.0000896-0.0006104-0.0000439-0.0009155c0.0000439-0.0003204,0.0002441-0.0005646,0.0002899-0.0008698
				c0.0241318-0.1927948,0.0483093-0.3860168,0.0690269-0.5802765c0.0206413-0.1934509,0.0377674-0.3873596,0.052248-0.5814362
				c0.0144272-0.1933136,0.0262299-0.3869019,0.0354004-0.5807037c0.0091496-0.1933746,0.0156879-0.3871002,0.0187225-0.5810089
				c0.0019073-0.1224518,0.0024204-0.244751,0.0024204-0.3668976
				C22.9692497,144.4967346,22.9690819,144.4257202,22.9689045,144.3547668z M22.2712402,140.336853
				c-0.0139465-0.0120697-0.0286961-0.0231628-0.0432777-0.0344849c0.0067616,0.0050964,0.0138798,0.0097351,0.0205173,0.0149841
				C22.2563114,140.3235626,22.2635975,140.3304291,22.2712402,140.336853z M22.4441166,141.0270538
				c0.0135555,0.1186523,0.0270996,0.2371521,0.0396404,0.3556061c0.0126076,0.1190643,0.0242558,0.2383575,0.0352001,0.3578339
				c0.0109215,0.1193237,0.021143,0.238739,0.0305691,0.3582611c0.009428,0.1195068,0.0180645,0.2391357,0.0259399,0.3588409
				c0.0078545,0.1193085,0.0149403,0.238678,0.021244,0.3581085c0.0063038,0.1194458,0.0118256,0.2389526,0.0166225,0.358551
				c0.0047989,0.1194458,0.0088806,0.2388763,0.0119839,0.3583069c0.0031013,0.1192474,0.0052204,0.238739,0.0073509,0.358429
				l0.0004139,0.4651184c0,0.000061,0.0000324,0.0001221,0.0000324,0.0001678c0,0.0000763-0.0000324,0.0001221-0.0000324,0.0001831
				c0.0004673,0.0840607,0.0009365,0.167984,0.0009365,0.2517548c0,0.0364227-0.0000896,0.0727997-0.0003014,0.1091614
				c-0.0007019,0.119873-0.0027885,0.2398071-0.0058689,0.3597565c-0.0030899,0.1199646-0.0071735,0.2398529-0.0123615,0.3596497
				c-0.0051651,0.1193542-0.011425,0.2386475-0.0187435,0.3578491s-0.0156975,0.2382965-0.0251579,0.3572388
				c-0.009428,0.1185455-0.0199375,0.2369537-0.0314178,0.3552551c-0.0114918,0.1183014-0.0239429,0.2363892-0.0377216,0.3541718
				c-0.0136909,0.1170654-0.0287189,0.2341614-0.0437908,0.351532c-0.0060806,0.0359802-0.0119267,0.0702667-0.0198269,0.1021576
				c-0.0079536,0.0322418-0.018465,0.0643921-0.0309048,0.0962372c-0.0117588,0.0301056-0.0250683,0.0594788-0.0399628,0.0879364
				c-0.0149288,0.0285492-0.0315189,0.0563049-0.0496044,0.0830841c-0.0180302,0.0267029-0.0374985,0.0523682-0.0583401,0.0767822
				c-0.020752,0.0243073-0.0429306,0.0474854-0.0663605,0.0693817c-0.0234966,0.0219727-0.0482311,0.0426483-0.0740376,0.0619202
				c-0.0257397,0.0192108-0.0524826,0.036972-0.0800838,0.0530701c-0.0276031,0.0161133-0.0563087,0.0307007-0.085886,0.0438385
				c-0.0296669,0.0131989-0.0596237,0.0246735-0.0897236,0.0338593c-0.0297012,0.009079-0.0617218,0.0164948-0.0953693,0.0242004
				c-0.0695858,0.0130157-0.1388149,0.0259857-0.2078648,0.0378265c-0.1041622,0.0178833-0.2088699,0.0334625-0.3139229,0.047226
				c-0.1050644,0.0137634-0.2103195,0.0256958-0.3157406,0.0356598c-0.1388359,0.0131073-0.2780304,0.0228119-0.4173813,0.0291443
				c-0.1307468,0.0059509-0.2616177,0.0089111-0.3924999,0.0089111l-0.0258617-0.0000305
				c-0.1368732-0.000412-0.2737236-0.0040741-0.4103947-0.0110474c-0.1366615-0.0069733-0.273201-0.0172577-0.4095039-0.0305634
				c-0.1345749-0.0131378-0.2686806-0.0292053-0.4019623-0.0492096c-0.1325989-0.0199127-0.2652893-0.043869-0.3987026-0.0679779
				c-0.0342178-0.007843-0.0668201-0.0153961-0.0970211-0.0246887c-0.0306263-0.0093994-0.0610619-0.0211487-0.0910969-0.0346985
				c-0.0296993-0.0133972-0.0585728-0.0283356-0.0864201-0.0447998c-0.0279484-0.0165253-0.0549812-0.0346832-0.0809441-0.0542755
				c-0.0258732-0.0195465-0.0506077-0.0404663-0.0740032-0.0627136c-0.0234413-0.0222626-0.0456219-0.0459442-0.0664406-0.0708008
				c-0.0208855-0.0249481-0.0403652-0.0510559-0.0583382-0.078186c-0.0179195-0.0270233-0.0342979-0.0549927-0.0489235-0.083725
				c-0.0146275-0.028717-0.0276356-0.0584564-0.0391388-0.088974c-0.0115471-0.0306091-0.0213318-0.0614166-0.0287857-0.0922241
				c-0.0073738-0.030365-0.0128975-0.0629578-0.018631-0.0971985c-0.0152187-0.1185303-0.0303917-0.2367859-0.0441933-0.3549805
				c-0.0138569-0.1186371-0.0263748-0.2375946-0.0379105-0.3567657c-0.0115376-0.1191559-0.0220795-0.2384338-0.0315189-0.3578339
				c-0.0094166-0.1189728-0.0177383-0.2380829-0.025013-0.3572998c-0.0072632-0.1192017-0.0134773-0.2385254-0.0185986-0.3578796
				c-0.0051003-0.1189575-0.0091038-0.2379913-0.0121288-0.3571014c-0.0030346-0.1191101-0.005064-0.2381897-0.0057449-0.3572083
				c-0.0002003-0.0352478-0.0002899-0.0705261-0.0002899-0.1058502c0-0.0837402,0.0004673-0.1676483,0.000948-0.2516785
				c0-0.0000916-0.0000439-0.0001526-0.0000439-0.0002441c0-0.0000763,0.0000439-0.0001526,0.0000439-0.0002289
				l-0.0003128-0.4681549c0-0.000061-0.0000324-0.0001068-0.0000324-0.0001831c0-0.000061,0.0000324-0.0001068,0.0000324-0.0001678
				c-0.0004673-0.0845795-0.000948-0.1690063-0.000948-0.2532959c0-0.0359039,0.0000896-0.0717773,0.0002899-0.1076355
				c0.0007038-0.119873,0.0027676-0.2397919,0.005846-0.3597565c0.0030804-0.1199646,0.0071526-0.2398529,0.0123405-0.3596344
				c0.0051537-0.1193695,0.0114021-0.2386627,0.0187206-0.3578644c0.0073071-0.1192017,0.0156765-0.2382965,0.0251369-0.357254
				c0.0094166-0.1185303,0.0199146-0.2369537,0.031395-0.3552551s0.0239201-0.2364044,0.0376892-0.354187
				c0.0136662-0.1170349,0.0286942-0.2340851,0.0437565-0.3514404c0.0058575-0.0349579,0.0114918-0.0682068,0.0190563-0.0992126
				c0.007597-0.0312653,0.0176048-0.0624847,0.0294437-0.0934296c0.0115356-0.0301971,0.0246334-0.0596619,0.0393162-0.0882416
				c0.0147266-0.028656,0.0311165-0.0565491,0.0490017-0.0834656c0.0178394-0.026825,0.0371304-0.0526428,0.0577927-0.0772095
				c0.0205727-0.0244446,0.0425854-0.0477905,0.0658474-0.0698547c0.0233517-0.0221558,0.0479298-0.0430145,0.0736027-0.0624847
				c0.0255947-0.0193939,0.0522137-0.0373535,0.0797043-0.0536499c0.0274906-0.0163116,0.056097-0.0310974,0.0855732-0.0444641
				c0.0295773-0.0133972,0.0594559-0.0251007,0.0894909-0.0345154c0.0295658-0.0092773,0.0614853-0.0168915,0.0949898-0.024826
				c0.0879612-0.0169525,0.1754417-0.0338135,0.2627449-0.0487823c0.0879288-0.0150757,0.1763248-0.0283966,0.2650223-0.0404205
				c0.0588303-0.0079803,0.1177158-0.0153656,0.1766472-0.0221252c0.1177158-0.013504,0.2357674-0.0244446,0.3539982-0.0328827
				c0.117794-0.0084229,0.2357674-0.0143433,0.3538074-0.017746c0.0802517-0.0023193,0.1605263-0.003479,0.240799-0.003479
				l0.0222473,0.0000305c0.0876942,0.0002289,0.1753769,0.0018463,0.2630138,0.004837
				c0.0018406,0.0000763,0.0036697,0.0002441,0.0055122,0.0003052c0.1139889,0.0039673,0.2279015,0.0101929,0.3415909,0.0188446
				c0.1074524,0.0081635,0.2146702,0.0187988,0.3216972,0.0311432c0.0074978,0.0008545,0.0150299,0.001297,0.0225258,0.002182
				c0.1024437,0.0119781,0.2043953,0.0264282,0.3059788,0.04245c0.0300121,0.0046387,0.0599136,0.0098572,0.0898476,0.0148468
				c0.0933838,0.0159912,0.1868,0.032959,0.2807636,0.0508118c0.0346413,0.0082245,0.0675774,0.0161743,0.0980244,0.0258942
				c0.0308609,0.0098419,0.0614853,0.0221558,0.0916538,0.0363159c0.0288181,0.0135345,0.0568008,0.0285492,0.0837669,0.0450592
				c0.0270557,0.016571,0.053196,0.0346985,0.0782661,0.0542297c0.0249786,0.0194855,0.0488338,0.0402985,0.0713482,0.0623627
				c0.0225582,0.0221252,0.0438805,0.045578,0.063839,0.0701904c0.0200272,0.0246735,0.0386696,0.0504761,0.0558186,0.07724
				c0.0171032,0.0266724,0.0326786,0.0542297,0.0465355,0.0825043s0.0261173,0.0575256,0.0369072,0.0875244
				c0.0108109,0.0300751,0.0199146,0.0603027,0.0267315,0.0904999
				C22.4340858,140.9613495,22.4390068,140.9933929,22.4441166,141.0270538z M17.9899731,140.3179779
				c0.0061588-0.0048218,0.012764-0.0090637,0.0190239-0.0137482c-0.013546,0.010437-0.0272236,0.0206604-0.0402431,0.0317535
				C17.975893,140.3300476,17.9826775,140.3237,17.9899731,140.3179779z M17.8746223,147.8199463l-0.0261955-0.0288544
				l-0.025919-0.0308075l-0.0255489-0.0328979l-0.025013-0.0350342l-0.0204067-0.0310059l-0.020462-0.0337219l-0.0202389-0.0364685
				l-0.0197697-0.0393372l-0.0179844-0.0398254c-0.0070744-0.0166321-0.0137787-0.0334625-0.0200157-0.0504456
				c-0.0075417-0.0205841-0.0144482-0.0414124-0.0206852-0.0624084c-0.0064259-0.021637-0.0121498-0.0435486-0.0172253-0.0656738
				c-0.0040627-0.0177155-0.0076427-0.0353851-0.0106335-0.052948l-0.0064049-0.0416412
				c-0.0240765-0.1924438-0.0480957-0.3844299-0.0685921-0.5765381c-0.0204506-0.1916809-0.0374432-0.3840485-0.0518246-0.5768127
				c-0.0143242-0.1920013-0.0260506-0.3842773-0.0351658-0.576767c-0.0090923-0.1920471-0.0155754-0.3842163-0.0185757-0.5763702
				c-0.0018978-0.1213989-0.0024109-0.243042-0.0024109-0.3648529c0-0.0707245,0.0001678-0.1415253,0.0003471-0.2123718
				c0-0.0000458-0.0000229-0.0000763-0.0000229-0.0001221s0.0000229-0.000061,0.0000229-0.0001068l-0.0003128-0.4661255
				c0-0.0000153-0.0000114-0.0000458-0.0000114-0.000061c0-0.0000305,0.0000114-0.0000458,0.0000114-0.0000763
				c-0.0001793-0.0718689-0.0003567-0.143692-0.0003567-0.2154388c0-0.1207886,0.0005131-0.2413635,0.0023861-0.3617554
				c0.0029907-0.1921539,0.0094624-0.3843231,0.0185318-0.5764008c0.0091038-0.1924591,0.0208073-0.3847198,0.0351219-0.5766907
				c0.0143604-0.192749,0.0313511-0.3851013,0.0517807-0.5767822c0.0204945-0.1922302,0.0445156-0.3843231,0.0686035-0.5768738
				l0.005064-0.033783l0.008213-0.0435791c0.0036812-0.0174408,0.0077763-0.0347443,0.0122948-0.0518799
				c0.005064-0.0191803,0.0106869-0.0382385,0.0168457-0.0571442c0.0053558-0.0164337,0.0111237-0.0327301,0.0172939-0.0489044
				l0.0168362-0.0414429l0.0148163-0.0328064c0.0121937-0.0258179,0.0254383-0.0511322,0.0397186-0.075882
				c0.012764-0.0221558,0.0263519-0.0438538,0.0407219-0.0650177c0.0156097-0.0230103,0.0321541-0.0454102,0.0495586-0.0671234
				c0.0162907-0.0203247,0.0333481-0.0400391,0.0511112-0.0590973c0.0189209-0.0203094,0.0386467-0.039856,0.0591316-0.0585938
				c0.0074291-0.0068054,0.0154305-0.01297,0.0230503-0.0195465c-0.0249138,0.0221405-0.0489788,0.0452271-0.0716171,0.0696411
				c-0.0241432,0.0260315-0.0469475,0.053299-0.0682907,0.081665c-0.0214329,0.0284882-0.0413933,0.0580597-0.0597801,0.0885773
				c-0.0184422,0.0305939-0.0353107,0.0621338-0.0505066,0.0945129c-0.0151291,0.0322266-0.0285168,0.0651245-0.0402985,0.0985107
				c-0.0117931,0.0334473-0.0221462,0.0678558-0.0302906,0.1031952c-0.0081558,0.0354004-0.0137005,0.0697174-0.0190563,0.1024628
				c-0.0000782,0.0004883,0.0001221,0.0009308,0.0000553,0.0014191s-0.000391,0.0008545-0.0004463,0.0013275
				c-0.0238972,0.1919861-0.0478401,0.3843994-0.0683575,0.5778351c-0.0204849,0.1930237-0.0374775,0.3865051-0.0518475,0.5801544
				c-0.0143147,0.1929779-0.0260181,0.3862305-0.0351105,0.5796967c-0.0090599,0.1928406-0.0155296,0.3859863-0.0185204,0.5793457
				c-0.0018635,0.1206055-0.0023766,0.2410736-0.0023766,0.3613892c0,0.0724792,0.0001907,0.1448975,0.0003681,0.2172546l0,0
				l0.0003128,0.4703674c-0.0001793,0.0709686-0.0003452,0.1420135-0.0003452,0.2131042
				c0,0.1216583,0.0005016,0.2434845,0.0023975,0.365448c0.0030136,0.1933289,0.0094948,0.3864746,0.0185757,0.5792847
				c0.0091057,0.1934509,0.0208302,0.3867035,0.0351677,0.5796661c0.0143814,0.193634,0.031395,0.3871155,0.0518799,0.5801239
				c0.0205288,0.1933899,0.0444927,0.3857574,0.0683918,0.5776825c0.0000668,0.0004883,0.000391,0.0008698,0.0004578,0.001358
				c0.0000668,0.0005035-0.0001354,0.0009308-0.0000572,0.0014191c0.0054111,0.0330353,0.0110245,0.067688,0.0192909,0.1034088
				c0.0082779,0.0357361,0.0187988,0.0705261,0.0308151,0.1043091c0.0119934,0.033783,0.02565,0.0670624,0.0410919,0.0996552
				c0.015295,0.0323029,0.0322762,0.0637817,0.050808,0.0942841c0.0185986,0.0306396,0.0387936,0.060318,0.060482,0.0888977
				c0.0217228,0.0286255,0.044939,0.0561218,0.06954,0.0823669c0.0225601,0.0240631,0.0465469,0.0467987,0.0713158,0.0686188
				c-0.007431-0.0063629-0.0152283-0.0122986-0.0224819-0.0188599
				C17.9133701,147.8594818,17.893589,147.8400879,17.8746223,147.8199463z M17.9746666,147.9122772
				c0.0123959,0.0104218,0.0254269,0.0200043,0.03829,0.0298462c-0.0059681-0.004425-0.0122604-0.0083923-0.0181293-0.0129547
				C17.9878979,147.9237976,17.9814606,147.9178314,17.9746666,147.9122772z M22.2493954,147.9286804
				c-0.0057354,0.0044556-0.0118828,0.0083313-0.0177059,0.0126801c0.0126171-0.0096588,0.0253696-0.0191193,0.0375195-0.0293274
				C22.2625256,147.9174957,22.2562122,147.9233856,22.2493954,147.9286804z M22.8196468,144.9324036
				c-0.0030003,0.1921387-0.0094833,0.3843079-0.0185757,0.576355c-0.0091038,0.1924744-0.0208302,0.3847198-0.0351562,0.5767059
				c-0.0143814,0.192749-0.0313721,0.3851166-0.0518227,0.5767822c-0.0204964,0.192215-0.0445385,0.3843079-0.0686264,0.5768127
				l-0.0050659,0.0337067l-0.0083122,0.0440674c-0.0037041,0.0174713-0.0078087,0.0348663-0.012373,0.0520782
				c-0.0051098,0.0193024-0.0107765,0.0384979-0.0170021,0.0575104c-0.0054226,0.0165863-0.0112572,0.0330353-0.0175171,0.0493317
				l-0.0168915,0.0414124l-0.0151291,0.0333405c-0.0122833,0.0258484-0.0256386,0.051239-0.0399857,0.076004
				c-0.0129089,0.0222626-0.0266323,0.0440521-0.0411358,0.0652924c-0.0157757,0.0231171-0.0324783,0.0455933-0.0500507,0.0673828
				c-0.0164566,0.0203857-0.0336819,0.0401611-0.0516224,0.0592804c-0.0191002,0.02034-0.0390167,0.0399323-0.059679,0.0587006
				c-0.007206,0.0065308-0.0149612,0.0124512-0.0223465,0.0187836c0.024847-0.0219421,0.0488892-0.0447845,0.0714931-0.0690002
				c0.0243893-0.0260925,0.0474167-0.0534515,0.0689716-0.0819092c0.0216446-0.0285797,0.0417938-0.0582733,0.06036-0.088913
				c0.0186195-0.0307159,0.0356579-0.0624237,0.0509987-0.0949249c0.0152721-0.0324097,0.0287952-0.0654907,0.0406876-0.0990448
				c0.0119057-0.0336151,0.0223484-0.068222,0.030571-0.103775c0.0082226-0.0355835,0.0138016-0.0700989,0.0191898-0.1030121
				c0.0000782-0.0005035-0.0001221-0.0009308-0.0000553-0.0014191s0.0003891-0.0008698,0.0004559-0.0013733
				c0.0239105-0.1919556,0.0478649-0.3843842,0.0684032-0.5778046c0.0204849-0.1930237,0.0374985-0.3865051,0.0518684-0.5801544
				c0.0143261-0.1929626,0.0260525-0.3862152,0.0351562-0.5796967c0.0090809-0.1928253,0.015564-0.3859711,0.0185642-0.5793304
				c0.0018864-0.1215668,0.0023994-0.2429657,0.0023994-0.3642273c0-0.0715332-0.0001793-0.1430054-0.0003567-0.2144318l0,0
				l-0.0004234-0.4685211c0-0.0002441-0.000145-0.0004425-0.000145-0.0006866c0-0.0002594,0.0001335-0.0004578,0.0001335-0.0007172
				c-0.003849-0.1930695-0.0077095-0.3864746-0.0140915-0.5800629c-0.006382-0.1937256-0.015274-0.3872375-0.026041-0.5804901
				c-0.010788-0.1934509-0.0234394-0.3867188-0.0379772-0.5797577c-0.0145607-0.1933289-0.0310059-0.3864899-0.0499382-0.5793152
				c-0.0189781-0.1931763-0.040411-0.3856049-0.0618095-0.5777283c-0.0000553-0.0004578-0.0003452-0.0008087-0.0004025-0.0012665
				c-0.0000553-0.000473,0.0001354-0.000885,0.0000668-0.0013428c-0.0048409-0.0326385-0.0098515-0.0668945-0.0174942-0.1022797
				c-0.0076523-0.0354156-0.017561-0.0699005-0.0289402-0.1034241c-0.0113792-0.0335083-0.0244122-0.0665588-0.0392284-0.0989227
				c-0.0146809-0.0320892-0.0310383-0.063385-0.0489559-0.0937653c-0.0179844-0.0304565-0.0375538-0.0600128-0.0586185-0.0884705
				c-0.0211086-0.0285187-0.0437126-0.0559235-0.0676994-0.0821075c-0.0225143-0.0245667-0.0464363-0.0478363-0.07127-0.0700989
				c0.0079098,0.006897,0.0161991,0.0133514,0.0238972,0.0204773c0.0200596,0.0185699,0.0394058,0.0379639,0.0579147,0.0580597
				l0.0255508,0.0287933l0.0251694,0.0306549l0.0248585,0.0328064l0.0242653,0.0348663l0.0197258,0.0307922l0.0197811,0.0335693
				l0.0195465,0.0362854l0.0190449,0.0391693l0.016758,0.0384064c0.0068169,0.0165405,0.0131989,0.0332336,0.019146,0.0501099
				c0.0073299,0.020813,0.0140018,0.0418701,0.0199585,0.0630951c0.0059032,0.0209656,0.0111351,0.0421753,0.0157433,0.0635529
				c0.0037823,0.0175934,0.0070953,0.03508,0.0098076,0.052475l0.0059128,0.0426178
				c0.0215321,0.1924591,0.0430431,0.3845978,0.0620098,0.576828c0.0189438,0.1920319,0.0354118,0.3845825,0.0499935,0.5774231
				c0.0145264,0.1921692,0.0271778,0.3845673,0.0379448,0.5771637c0.0107327,0.1920624,0.0196037,0.3842316,0.0259628,0.5764465
				c0.0063477,0.1918945,0.0101967,0.3842468,0.0140572,0.576889l0,0l0.0000114,0.0007019l0.0004234,0.4658508
				c0,0.0000305,0.0000114,0.0000305,0.0000114,0.000061c0,0.0000153-0.0000114,0.0000458-0.0000114,0.000061
				c0.0001678,0.0709534,0.0003471,0.1418457,0.0003471,0.212677
				C22.8220577,144.6895447,22.8215427,144.8110657,22.8196468,144.9324036z"
          />
          <path
            class="st1"
            d="M20.1216469,143.0398712c-0.5961246,0.000061-1.0792408,0.4831696-1.0793076,1.0792999
				c0.0000668,0.5961151,0.4831829,1.0792236,1.0793076,1.0792999c0.5961151-0.0000763,1.0792313-0.4831848,1.079298-1.0792999
				C21.2008781,143.5230408,20.717762,143.0399323,20.1216469,143.0398712z M20.7379456,144.7354584
				c-0.1580505,0.1578979-0.3752975,0.2552338-0.6162987,0.2552795c-0.2410107-0.0000458-0.4582577-0.0973816-0.6163063-0.2552795
				c-0.1578941-0.1580505-0.2552261-0.3752747-0.2552814-0.6162872c0.0000553-0.2410126,0.0973873-0.458252,0.2552814-0.6163177
				c0.1580486-0.1578827,0.3752956-0.2552185,0.6163063-0.2552795c0.2410011,0.000061,0.4582481,0.0973969,0.6162987,0.2552795
				c0.1578922,0.1580658,0.2552242,0.3753052,0.2552814,0.6163177
				C20.9931698,144.3601837,20.8958378,144.5774078,20.7379456,144.7354584z"
          />
        </g>
      </g>
      <path class="st0" d="M34.6316109,162.9806366c2.2271919-0.017395,1.7828026,0,3.9617805,0h12.9760323" />
      <path
        class="actextrectangle"
        d="M32.6721954,168.1892395H8.0128689c-1.0999999,0-2-0.9000092-2-2v-6.2136993
		c0-1.1000061,0.9000001-2,2-2h24.6593266c1.0999985,0,2,0.8999939,2,2v6.2136993
		C34.6721954,167.2892303,33.7721939,168.1892395,32.6721954,168.1892395z"
      />
      <g id="static-texts_3_" />
      <g id="dynamic-charts_6_" />
      <g id="dynamic-texts_2_">

        <circle
          id="household-center"
          ref="wallBoxMainCenter"
          class="st6"
          cx="20.2320862"
          cy="163.0823822"
          r="0.1104445"
        />
      </g>
      <g id="arrows-in_6_" ref="wallboxArrowsIn" class="mobile-scheme horizontal">
        <polygon class="st7" points="35.6626892,162.9806366 39.8698502,164.9302979 39.8698502,161.0309906 		" />
        <polygon class="st7" points="40.6017303,162.9632416 44.8088913,164.9129028 44.8088913,161.0135956 		" />
        <polygon class="st7" points="45.5407753,162.9632416 49.7479362,164.9129028 49.7479362,161.0135956 		" />
      </g>
    </g>
    <g id="heating-group" ref="heatingGroup">
      <g>
        <text transform="matrix(1 0 0 1 69.5792007 128.8563995)" class="st3 st4 st5">Kúrenie</text>
        <path
          class="st1"
          d="M81.1678696,119.4308624c0.4739761-0.020462,0.6627502-0.7245331,1.1785736-0.4232254
			c0.4463501,0.2607346,0.1268005,1.1540451-0.0860367,1.5044785c-0.5797272,0.9545441-1.1634521,0.3217926-1.5888977-0.3517914
			l-0.2865906-0.0381851c-0.2473984,0.3599472,0.1979294,0.8211212-0.1145935,1.1485596
			c-0.4651642,0.4873581-1.2146759-0.3870392-1.4079285-0.7855377c-0.4690933-0.9672928,0.3263855-1.1110001,1.1039734-1.1401367
			l0.1801224-0.2347336c0.019577-0.0739136-0.1486282-0.1874313-0.2082138-0.223259
			c-0.2595367-0.1560059-0.6728439-0.1325073-0.7209778-0.5158463c-0.0866165-0.6897354,1.0999298-0.8032532,1.5751114-0.7505875
			c0.8943176,0.0991287,0.5273666,0.9982147,0.2636261,1.5251465L81.1678696,119.4308624z M80.1846542,119.7699585
			c0.1253738-0.1040268,0.2481384,0.0572739,0.3699265,0.0685806c0.1036224,0.0096207,0.1746292-0.0967636,0.2928848-0.0190659
			c0.0479279,0.0314865,0.2673187,0.4416122,0.3598099,0.547226c0.0695038,0.0793686,0.3382111,0.3326492,0.435936,0.3236542
			c0.1450806-0.0133591,0.4064331-0.4451447,0.4626617-0.5801544c0.0713654-0.1713562,0.3073044-0.9517593-0.0679016-0.8378906
			c-0.0529022,0.0160599-0.1773987,0.1768036-0.2417908,0.2258453c-0.1756821,0.133812-0.6701202,0.3482971-0.877243,0.2498398
			c-0.1075668-0.0511322-0.0670166-0.2281342-0.0945358-0.3177795c-0.0321884-0.104866-0.1605835-0.1129761-0.1550293-0.2587585
			c0.0033493-0.0880966,0.2478409-0.4495773,0.2967834-0.6132736c0.0461731-0.1544571,0.1431122-0.4956589-0.0408554-0.5757141
			c-0.303894-0.1322403-0.812851-0.0395813-1.1130066,0.0815353c-0.1469955,0.0593185-0.329567,0.1117706-0.2630386,0.3051758
			c0.0208282,0.0605698,0.3042068,0.1221695,0.3900375,0.1595154c0.1846008,0.0803146,0.5089798,0.3227692,0.5903778,0.5088272
			c0.1058731,0.2419968-0.0718918,0.2043381-0.1728058,0.3131866c-0.0879974,0.0949097-0.0409164,0.2694778-0.181282,0.3132324
			c-0.0898514,0.0280075-0.2483749-0.0170975-0.3695679-0.011734c-0.1685257,0.0074463-0.6072922,0.11409-0.7365494,0.2178802
			c-0.0993195,0.0797501-0.0000381,0.2728729,0.039299,0.3686981c0.1170578,0.2851181,0.3716888,0.6065521,0.6359558,0.7658844
			c0.4836731,0.2916107,0.2614899-0.2154922,0.2433243-0.4356461
			C79.9732819,120.3902206,80.045639,119.8853073,80.1846542,119.7699585z M84.1835403,122.0328903v-0.1475143h-5.131424
			c-0.2027359-0.0000381-0.3864365-0.0826416-0.5207977-0.2167969c-0.1341553-0.1343613-0.2167511-0.3180618-0.2167892-0.5207901
			v-3.5105896c0.0000381-0.2027359,0.082634-0.3864365,0.2167892-0.5207977
			c0.1343613-0.1341553,0.3180618-0.2167587,0.5207977-0.2167969h5.131424
			c0.2027359,0.0000381,0.3864365,0.0826416,0.5207977,0.2167969c0.1341553,0.1343613,0.2167511,0.3180618,0.2167892,0.5207977
			v3.5105896c-0.0000381,0.2027283-0.082634,0.3864288-0.2167892,0.5207901
			c-0.1343613,0.1341553-0.3180618,0.2167587-0.5207977,0.2167969V122.0328903v0.1475143
			c0.2840805,0.0000381,0.5429306-0.1165085,0.7294159-0.3031998c0.1866989-0.1864929,0.3032455-0.445343,0.3032074-0.7294159
			v-3.5105896c0.0000381-0.2840805-0.1165085-0.5429306-0.3032074-0.7294159
			c-0.1864853-0.1866989-0.4453354-0.3032455-0.7294159-0.3032074h-5.131424
			c-0.2840805-0.0000381-0.5429306,0.1165085-0.7294159,0.3032074c-0.1866989,0.1864853-0.3032455,0.4453354-0.3032074,0.7294159
			v3.5105896c-0.0000381,0.2840729,0.1165085,0.542923,0.3032074,0.7294159
			c0.1864853,0.1866913,0.4453354,0.3032379,0.7294159,0.3031998h5.131424V122.0328903z M82.7332458,117.9560165h1.6208344
			c0.0814743,0,0.147522-0.0660477,0.147522-0.147522c0-0.0814667-0.0660477-0.1475143-0.147522-0.1475143h-1.6208344
			c-0.0814743,0-0.147522,0.0660477-0.147522,0.1475143C82.5857239,117.8899689,82.6517715,117.9560165,82.7332458,117.9560165
			 M83.3406525,121.2479935h1.0221481c0.0814667,0,0.1475143-0.0660477,0.1475143-0.147522
			s-0.0660477-0.1475143-0.1475143-0.1475143h-1.0221481c-0.0814743,0-0.147522,0.06604-0.147522,0.1475143
			S83.2591782,121.2479935,83.3406525,121.2479935 M83.3738022,120.1541519h1.0221481
			c0.0814743,0,0.147522-0.06604,0.147522-0.1475143s-0.0660477-0.1475143-0.147522-0.1475143h-1.0221481
			c-0.0814667,0-0.1475143,0.06604-0.1475143,0.1475143S83.2923355,120.1541519,83.3738022,120.1541519 M83.4066849,119.1055908
			h1.0221481c0.0814743,0,0.147522-0.0660477,0.147522-0.1475143c0-0.0814743-0.0660477-0.147522-0.147522-0.147522h-1.0221481
			c-0.0814667,0-0.1475143,0.0660477-0.1475143,0.147522C83.2591705,119.0395432,83.3252182,119.1055908,83.4066849,119.1055908"
        />
      </g>
      <path class="st0" d="M51.8743324,137.2566071h15.6471977" />
      <path
        class="actextrectangle"
        d="M94.0258789,142.3356934H69.5223465c-1.0999985,0-2-0.8999939-2-2v-6.1581726
		c0-1.0999908,0.9000015-2,2-2h24.5035324c1.0999985,0,2,0.9000092,2,2v6.1581726
		C96.0258789,141.4356995,95.1258774,142.3356934,94.0258789,142.3356934z"
      />
      <g id="static-texts" />
      <g id="dynamic-charts_3_" />
      <g id="dynamic-text_3_">

        <circle
          id="heating-center"
          ref="heatingMainCenter"
          class="st6"
          cx="81.6642685"
          cy="137.2566071"
          r="0.1098441"
        />
      </g>
      <g id="arrows-in_3_" ref="heatingArrowsIn" class="mobile-scheme horizontal">
        <polygon class="st7" points="57.2099037,137.2565918 53.0256119,135.3175354 53.0256119,139.1956329 		" />
        <polygon class="st7" points="62.122097,137.2565918 57.9378052,135.3175354 57.9378052,139.1956482 		" />
        <polygon class="st7" points="67.0342941,137.2392883 62.8500023,135.3002319 62.8500023,139.1783447 		" />
      </g>
    </g>
    <g id="household-group" ref="householdGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 4.1706285 103.5049591)" class="st3 st4 st8">Domácnosť</text>
        </g>
        <g>
          <path
            class="st9"
            d="M25.2765045,95.5438843l-2.4073143-1.6713028v-2.6355133
				c0-0.0980301-0.0787449-0.1767731-0.1767731-0.1767731s-0.1767712,0.078743-0.1767712,0.1767731v2.7287216
				c0,0.0578537,0.027319,0.1124878,0.0755291,0.1462402l2.0176125,1.3997116H23.721714
				c-0.0980282,0-0.1767731,0.078743-0.1767731,0.1767731v1.8127136c0,0.0980301,0.0787449,0.1767731,0.1767731,0.1767731
				s0.1767712-0.078743,0.1767712-0.1767731v-1.6359406h1.2767773c0.07794,0,0.1462383-0.0498199,0.1703434-0.1237411
				C25.3681049,95.6660156,25.3391781,95.584053,25.2765045,95.5438843z"
          />
          <path
            class="st9"
            d="M21.4509945,91.1085052c-0.0345497,0.0337524-0.0538349,0.0787506-0.0538349,0.125351L21.359396,92.908371
				l-1.6094303-1.1683044c-0.0594597-0.0417786-0.1382046-0.0466003-0.2097168-0.003212l-5.4076185,3.8745193
				c-0.0626736,0.0449982-0.0891895,0.1269531-0.0658875,0.1992722c0.0241051,0.072319,0.0915995,0.1221313,0.1695404,0.1221313
				h1.5226507v1.5684509c0,0.0980301,0.0795469,0.1767731,0.1775751,0.1767731c0.0980291,0,0.1767721-0.078743,0.1767721-0.1767731
				v-1.745224c0-0.0980225-0.078743-0.1767654-0.1767721-0.1767654h-1.1482153l4.855608-3.4792023l1.7813816,1.2952576
				c0.050621,0.0353546,0.1181164,0.0417862,0.1840038,0.0144653c0.0578518-0.0305328,0.0956173-0.0899963,0.0964203-0.1558838
				l0.0449963-2.0119858c0.0016079-0.0482101-0.0144634-0.0915985-0.050621-0.1301651
				C21.6325893,91.0474396,21.5265255,91.0442276,21.4509945,91.1085052z M21.5087471,91.1685715l-0.0000992,0.0000992
				l-0.0006027-0.0007019L21.5087471,91.1685715z"
          />
          <path
            class="st9"
            d="M22.7821102,90.2916412l0.0356541,0.0085373v-0.0727158l0.0233021-0.1233444l-0.0532322,0.0198898
				c-0.0286255-0.0590591-0.080452-0.1092758-0.1805878-0.1404114c-0.0795479-0.0241089-0.1856117-0.0417862-0.2788181-0.0578537
				l-0.0136604,0.0818558l0,0l-0.0128555-0.0850677c-0.0956173-0.0176773-0.204092-0.0353546-0.2884598-0.0594635
				c-0.049818-0.0160675-0.0859756-0.028923-0.1084747-0.0417786l-0.0080357-0.0048218l0.0056248-0.0032196
				c0.025713-0.0080338,0.0658894-0.0192795,0.1301689-0.032135c0.0916004-0.0160751,0.2008781-0.0273209,0.3173866-0.0385742
				l0.0449963-0.0048141c0.1711483-0.0160751,0.3487244-0.0321426,0.4893379-0.0674973
				c0.0867786-0.0224991,0.154274-0.0514221,0.205698-0.0883865c0.0420837-0.0302353,0.0462017-0.0777435,0.0653858-0.1189194
				l0.0535336,0.0192871l-0.0043182-0.1087799c0-0.0001984,0.0003014-0.0002975,0.0003014-0.0005035l0.0024109-0.1349869
				l-0.0380669,0.0190811c-0.0203896-0.033844-0.0499191-0.0689011-0.1033516-0.0978241
				c-0.0562458-0.0305328-0.1293659-0.0514221-0.2273941-0.0690994c-0.1510582-0.0241089-0.3463116-0.0305328-0.5351372-0.0353546
				l-0.0457993-0.0016098c-0.2065029-0.0048218-0.3623829-0.0096436-0.4949627-0.0257111
				c-0.079546-0.0080338-0.1406136-0.0208893-0.1823959-0.0353546l-0.0087376-0.0029144l0.0095406-0.0035172
				c0.0417824-0.0160675,0.1060638-0.032135,0.1952534-0.0449905c0.1486492-0.0241089,0.3262253-0.0385742,0.5262985-0.0530319
				c0.2514992-0.0176773,0.513443-0.0385742,0.7167301-0.0819626c0.103653-0.0224991,0.4146118-0.0867767,0.4274673-0.3262253
				l0.0682983-0.0498123l-0.077137-0.0289307c-0.0112495-0.0401764-0.0329437-0.0739212-0.0650845-0.104454
				c-0.1598988-0.151062-0.5447788-0.1365967-1.157053-0.0980301c-0.2249832,0.0160675-0.4804993,0.0273209-0.6870003,0.0241089
				c-0.1904316-0.0064316-0.2748013-0.0289307-0.3117619-0.046608l-0.0064278-0.003212l0.0064278-0.003212
				c0.0369606-0.0257187,0.1261501-0.0610733,0.3382778-0.0883865c0.206501-0.0273209,0.4780865-0.0369644,0.7175331-0.046608
				l0.0425854-0.0016022c0.3045311-0.0128555,0.6195068-0.0257187,0.8517208-0.0610733
				c0.1365967-0.0208893,0.2394466-0.0498123,0.3149757-0.0867767c0.1454353-0.0739212,0.1759682-0.1735535,0.1759682-0.2474823
				c-0.000803-0.0658875-0.0554409-0.1189194-0.1269531-0.1205215c-0.0008049,0-0.0024109,0-0.0040188,0
				c-0.0691013,0-0.1245441,0.0482101-0.129364,0.1124878v0.003212c-0.0160713,0.0144653-0.0875835,0.0594635-0.3479195,0.0916061
				c-0.1880226,0.0241013-0.4282722,0.0353546-0.6596813,0.0449905l-0.0699062,0.0032196
				c-0.3414917,0.0112457-0.6436119,0.0241013-0.8814507,0.0610657c-0.1373997,0.0224991-0.2410526,0.0514221-0.3181896,0.0899887
				c-0.1341858,0.0658875-0.1719513,0.1558838-0.1783791,0.226593l-0.0642796,0.0417786l0.0682983,0.0337524
				c0.0104446,0.0482101,0.0353527,0.0915985,0.0739212,0.1269531c0.1647205,0.157486,0.5688858,0.1446304,1.2197285,0.1060638
				c0.2081089-0.0128555,0.4049683-0.0257111,0.5729027-0.0257111l0.0658875,0.0016022
				c0.1703434,0.0048218,0.2523022,0.0224991,0.2900677,0.0369644l0.0072308,0.003212l-0.0064278,0.0048218
				c-0.0305328,0.0208893-0.1004391,0.0530319-0.2635517,0.0835648c-0.1607018,0.0289307-0.3639889,0.0449982-0.5608501,0.0610657
				l-0.079546,0.0064316c-0.2281971,0.0176773-0.4435387,0.0337448-0.6122742,0.0674973
				c-0.0948143,0.0176773-0.1759701,0.0433884-0.2378387,0.0755234c-0.0687008,0.0388718-0.1000385,0.083168-0.1185188,0.1248474
				l-0.0373631-0.012352l0.0131569,0.1106796l-0.0067291,0.10466l0.0351543-0.0129547
				c0.0195847,0.0403748,0.049818,0.0816574,0.1118889,0.1158066c0.0538349,0.0305328,0.1261501,0.0530319,0.2346249,0.0723114
				c0.1647186,0.0257111,0.3768444,0.0321426,0.5970078,0.0369644c0.1679325,0.0048218,0.3414917,0.0096436,0.4780865,0.0257111
				c0.10285,0.0112457,0.1615067,0.0257111,0.1920395,0.0369644l0.0093403,0.0034103l-0.0093403,0.0046234
				c-0.0313358,0.0160675-0.0827618,0.0321426-0.1518631,0.0466003c-0.1004391,0.0192871-0.2249832,0.0321426-0.3455086,0.0449982
				l-0.0570488,0.0048218c-0.1598988,0.0144653-0.3262253,0.028923-0.4523773,0.0594559
				c-0.079546,0.0192871-0.1382027,0.043396-0.1807899,0.072319c-0.0602627,0.0401764-0.0883846,0.0867767-0.1012421,0.1269531
				l-0.1149006,0.0417862l0.1084728,0.0626678c0.0096416,0.0610733,0.0409794,0.1141052,0.0916004,0.1590958
				c0.0433903,0.0369644,0.1012421,0.0674973,0.1711483,0.0916061c0.1116867,0.0385666,0.2434635,0.0626678,0.3921127,0.0883865
				c0.0707073,0.0096359,0.1607018,0.0257111,0.2257862,0.0417786l0.0105457,0.0025101l-0.0105457,0.0039215
				c-0.0273209,0.0112457-0.0586567,0.0208893-0.0827618,0.0273132l0.024004,0.0785446c-0.0000992,0,0.0001011,0,0,0
				l-0.0440922-0.072113c-0.0658875,0.0192871-0.1221333,0.0353546-0.1703434,0.0690994
				c-0.1566849,0.1044617-0.2217693,0.2683716-0.1984673,0.4997864c0.0064278,0.0658875,0.0634766,0.1173096,0.1357937,0.1173096
				h0.000803c0.0393715-0.003212,0.0707092-0.0192795,0.0940094-0.0466003
				c0.0224991-0.0273209,0.0329456-0.0626755,0.0289268-0.0964203c-0.0192833-0.1896286,0.0458012-0.233017,0.0835648-0.2587357
				c0.0144634-0.0096359,0.0514259-0.0208893,0.102047-0.0353546c0.0691013-0.0208893,0.1406136-0.0417786,0.2024841-0.078743
				C22.7351036,90.3993073,22.75951,90.3454742,22.7821102,90.2916412z"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="51.7079582"
        y1="110.6792984"
        x2="34.4383621"
        y2="110.6792984"
      />
      <path
        class="actextrectangle"
        d="M32.4383621,115.7531052H8.0128689c-1.0999999,0-2-0.9000015-2-2v-6.1303635
		c0-1.0999985,0.9000001-2,2-2h24.4254932c1.1000023,0,2,0.9000015,2,2v6.1303635
		C34.4383621,114.8531036,33.5383644,115.7531052,32.4383621,115.7531052z"
      />
      <g id="static-texts_2_" />
      <g id="dynamic-charts_5_" />
      <g id="dynamic-texts_1_">

        <circle
          id="household-center"
          ref="householdMainCenter"
          class="st6"
          cx="20.1160717"
          cy="110.6879272"
          r="0.1095434"
        />
      </g>
      <g id="arrows-in_5_" ref="householdArrowsIn" class="mobile-scheme horizontal">
        <polygon class="st7" points="35.9871979,110.6965485 40.1600304,112.6302948 40.1600304,108.7628021 		" />
        <polygon class="st7" points="40.8859444,110.6792984 45.0587769,112.6130447 45.0587769,108.7455521 		" />
        <polygon class="st7" points="45.784687,110.6792984 49.9575233,112.6130447 49.9575233,108.7455521 		" />
      </g>
    </g>
    <g id="tuv-group" ref="tuvGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 75.3693466 74.6008224)" class="st3 st4 st5">TÚV</text>

          <linearGradient
            id="SVGID_1_"
            gradientUnits="userSpaceOnUse"
            x1="50.6952591"
            y1="126.3301315"
            x2="67.4142227"
            y2="126.3301315"
            gradientTransform="matrix(1 0 0 1 22.8876152 -67.0901871)"
          >
            <stop offset="0" style="stop-color:#FD1A16" />
            <stop offset="1" style="stop-color:#C2000B" />
          </linearGradient>
          <path
            class="st10"
            d="M74.9725952,64.987587c-0.5683365-1.0872459-0.8897247-2.3240013-0.8897247-3.6358185
				c0-4.3406715,3.5188141-7.8594818,7.8594818-7.8594818c4.3406754,0,7.8594818,3.5188103,7.8594818,7.8594818
				c0,1.3103485-0.3206711,2.5457993-0.8878098,3.632164"
          />
          <g class="st11" />
        </g>
        <g>
          <path
            class="st1"
            d="M71.2017136,65.5810089c-0.0594635-0.7310715-0.6540909-1.3287506-1.389122-1.3791733l-0.0031662-0.0002213
				l-3.7639999-0.0001144l-0.0028229,0.0002289c-0.7386475,0.0447617-1.3390427,0.645607-1.3983917,1.3793945l-0.0003357,0.0036163
				l0.0001144,7.3241806l0.0002213,0.0039597c0.0577698,0.6756744,0.5728073,1.2453232,1.2445297,1.3575745l0.0056534,0.0010147
				l0.4360199,0.0179749l0.1599655,0.2457657l0.0274658,0.0420532h0.0501938h0.0789108l-0.0070114,0.2303925l0.0473633,0.2784348
				l0.2524338,0.1745377L66.9447098,75.2612l0.3922729-0.002037l0.0409241-0.0001144l0.2242889-0.2492676l0.023735-0.0263367
				v-0.4061813h0.5939484v0.3705673v0.0325546l0.1896896,0.2369461l0.0270233,0.0338058l0.4581757,0.0099487l0.0409241,0.0009003
				l0.2393188-0.251297l0.0254364-0.02668l0.0010147-0.4067459h0.0764236h0.0180893l0.0721207-0.0291672L69.40065,74.534874
				l0.1203995-0.2427139l0.0851212,0.0009079l0.4728775-0.0481644c0.6146317-0.1553268,1.068512-0.702919,1.1226654-1.3320236
				l0.0003357-0.0038452v-7.324295L71.2017136,65.5810089z M68.6302414,74.8596573v-0.2823944h0.1697922v0.2823944H68.6302414z
				 M67.0458908,74.8596573v-0.2823944h0.1697922v0.2823944H67.0458908z M65.044281,65.6911163
				c0.0030594-0.5595856,0.4538879-1.0494156,1.0181046-1.0879593l3.7290726,0.0001068
				c0.5619507,0.0449982,1.0069046,0.530304,1.0101852,1.0878525H65.044281z M65.0459824,70.641655v-4.5259323h5.7539597v4.5259323
				H65.0459824z M69.7584457,73.8903885l-3.6711884-0.0001068c-0.546814-0.0281525-1.0002441-0.472084-1.0416183-1.0169678
				l0.0003433-1.8024216h5.7539597l0.0003433,1.8019638C70.7580032,73.4203415,70.3071747,73.8598709,69.7584457,73.8903885z"
          />
          <path
            class="st1"
            d="M70.2497482,66.7292252l-0.0803757,0.0671463v3.1259689l0.2041626-0.0040665
				c0.0135651-1.0369797,0.0137863-2.0808563-0.0001144-3.1177216L70.2497482,66.7292252z"
          />
          <path
            class="st1"
            d="M67.8603897,69.0933685c-0.5842209,0.0656815-0.522049,1.0083771,0.1352081,0.9316177
				C68.5559692,69.9596481,68.5000076,69.0213547,67.8603897,69.0933685z M68.060936,69.7700653
				c-0.1534042,0.1070557-0.3692093,0.0011292-0.388092-0.1822281l0.2133255-0.2782135
				C68.1506958,69.2753754,68.2722168,69.6223145,68.060936,69.7700653z"
          />
          <path
            class="st1"
            d="M67.8259125,66.8857956c-0.6669769,0.0640945-1.0032883,0.8445663-0.6286545,1.3940887
				c0.3704529,0.54319,1.1820145,0.5001221,1.4976425-0.0748367C69.0426331,67.5718765,68.542511,66.8168335,67.8259125,66.8857956z
				 M68.4335403,68.2032394c-0.2306213,0.2783203-0.6523972,0.3286285-0.9307175,0.0914536
				c-0.442009-0.3768997-0.2570648-1.0791397,0.3076019-1.1864243v0.4285583l0.0341415,0.0610504l0.141983,0.0112991
				l0.0402451-0.0549393v-0.4459686C68.5356216,67.1907959,68.7662354,67.8014679,68.4335403,68.2032394z"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="51.6178169"
        y1="82.3766327"
        x2="68.4319458"
        y2="82.3766327"
      />
      <path
        class="actextrectangle"
        d="M94.1595306,87.2585602H70.1568298c-1.0999985,0-2-0.9000015-2-2v-5.9796829
		c0-1.1000061,0.9000015-2,2-2h24.0027008c1.0999985,0,2,0.8999939,2,2v5.9796829
		C96.1595306,86.3585587,95.2595291,87.2585602,94.1595306,87.2585602z"
      />
      <g id="static-texts_1_" />
      <g id="dynamic-charts_4_">
        <path
          id="daily-consumption-chart_00000135677735334506898200000017998103950785139363_"
          ref="tuvStateChart"
          class="acchartcustom"
          d="
			M88.9140244,64.9839325c0.5671387-1.0863647,0.8878098-2.3218155,0.8878098-3.632164
			c0-4.3406715-3.5188065-7.8594818-7.8594818-7.8594818c-4.3406677,0-7.8594818,3.5188103-7.8594818,7.8594818
			c0,1.3118172,0.3213882,2.5485725,0.8897247,3.6358185"
        />
      </g>
      <g id="dynamic-texts">

        <circle
          id="current-temperature"
          ref="tuvStateCurrentTemperatureCenter"
          class="st6"
          cx="81.9423523"
          cy="60.5513382"
          r="0.0717752"
        />

        <circle
          id="heating-center_1_"
          ref="tuvMainCenter"
          class="st6"
          cx="82.0502701"
          cy="82.2687149"
          r="0.1079141"
        />
      </g>
      <g id="arrows-in_4_" ref="tuvArrowsIn" class="mobile-scheme horizontal">
        <polygon class="st7" points="57.2755241,82.3851242 53.1647568,80.4801407 53.1647568,84.2901077 		" />
        <polygon class="st7" points="62.1014099,82.3851318 57.9906387,80.4801483 57.9906387,84.2901154 		" />
        <polygon class="st7" points="66.9272919,82.3681335 62.8165207,80.46315 62.8165207,84.2731171 		" />
      </g>
    </g>
    <g id="battery-group" ref="batteryGroup">
      <g class="st11" />

      <linearGradient
        id="SVGID_00000047765945421442614770000006290741525215718313_"
        gradientUnits="userSpaceOnUse"
        x1="-14.3669128"
        y1="34.2885246"
        x2="2.1597667"
        y2="34.2885246"
        gradientTransform="matrix(1 0 0 1 25.0514336 1.843267)"
      >
        <stop offset="0" style="stop-color:#4FA0CA" />
        <stop offset="0.0751259" style="stop-color:#4484BA" />
        <stop offset="0.1881396" style="stop-color:#3660A7" />
        <stop offset="0.3060003" style="stop-color:#2A4498" />
        <stop offset="0.4272921" style="stop-color:#222D8C" />
        <stop offset="0.553003" style="stop-color:#1B1D84" />
        <stop offset="0.6850783" style="stop-color:#16127E" />
        <stop offset="0.8277422" style="stop-color:#130D7A" />
        <stop offset="1" style="stop-color:#110B79" />
      </linearGradient>

      <path
        style="fill:none;stroke:url(#SVGID_00000047765945421442614770000006290741525215718313_);stroke-linecap:round;stroke-miterlimit:10;"
        d="
		M12.0633593,41.8091354c-0.5613804-1.0739479-0.8788376-2.2955742-0.8788376-3.5913467
		c0-4.2875748,3.4757662-7.76334,7.763339-7.76334s7.76334,3.4757652,7.76334,7.76334
		c0,1.2943192-0.3167458,2.5146599-0.8769512,3.5877342"
      />
      <path
        class="actextrectangle"
        d="M31.0189362,63.2492752H6.883852c-1.0999999,0-2-0.9000015-2-2v-6.0268669
		c0-1.0999985,0.9000001-2,2-2h24.1350842c1.0999985,0,2,0.9000015,2,2v6.0268669
		C33.0189362,62.3492737,32.1189346,63.2492752,31.0189362,63.2492752z"
      />
      <g>
        <text transform="matrix(1 0 0 1 7.8387427 49.9283485)" class="st3 st4 st13">Batéria</text>

        <linearGradient
          id="SVGID_00000054262276550745556450000013688645528028505480_"
          gradientUnits="userSpaceOnUse"
          x1="-14.3633804"
          y1="34.3197212"
          x2="2.1632991"
          y2="34.3197212"
          gradientTransform="matrix(1 0 0 1 25.0514336 1.843267)"
        >
          <stop offset="0" style="stop-color:#CCE70B" />
          <stop offset="1" style="stop-color:#80C41C" />
        </linearGradient>

        <path
          style="fill:none;stroke:url(#SVGID_00000054262276550745556450000013688645528028505480_);stroke-linecap:round;stroke-miterlimit:10;"
          d="
			M12.0668917,41.840332c-0.5613804-1.0739479-0.8788376-2.2955742-0.8788376-3.5913467
			c0-4.2875748,3.4757662-7.7633381,7.763339-7.7633381s7.76334,3.4757633,7.76334,7.7633381
			c0,1.294323-0.3167458,2.5146599-0.8769512,3.587738"
        />
        <g class="st11" />
        <g>
          <path
            class="st1"
            d="M36.1100388,40.6240158H35.28899c0.0149498-0.0383453,0.0249748-0.0793266,0.0249748-0.1228294
				c0-0.4002647-0.3275032-0.7277107-0.7277069-0.7277107h-0.295845c-0.4002075,0-0.7277107,0.3275032-0.7277107,0.7277107
				c0,0.0435028,0.010025,0.0844841,0.0249748,0.1228294h-0.8210487c-0.6583519,0-1.1938744,0.5355797-1.1938744,1.1939316
				v7.7755127c0,0.6583519,0.5355225,1.1939316,1.1938744,1.1939316h3.3434105c0.6583519,0,1.1939316-0.5355797,1.1939316-1.1939316
				v-7.7755127C37.3039703,41.1595955,36.7683907,40.6240158,36.1100388,40.6240158z M36.9982758,49.5934601
				c0,0.4897919-0.3984451,0.888237-0.888237,0.888237h-3.3434105c-0.4897919,0-0.888237-0.3984451-0.888237-0.888237v-7.7755127
				c0-0.4897919,0.3984451-0.888237,0.888237-0.888237h3.3434105c0.4897919,0,0.888237,0.3984451,0.888237,0.888237V49.5934601z"
          />
          <polygon
            class="st1"
            points="34.6846352,42.8810768 33.3091278,46.0141258 34.582737,45.6829834 33.9969101,48.0773582
				35.5252075,44.7914658 34.2261543,45.1481094 			"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="33.0189362"
        y1="58.1316872"
        x2="51.6178169"
        y2="58.1316872"
      />
      <g id="static-text_1_" />
      <g id="dynamic-text_1_">

        <circle
          id="battery-center"
          ref="batteryMainCenter"
          class="st6"
          cx="18.8429699"
          cy="58.2358437"
          r="0.1084242"
        />

        <circle
          id="daily-energy-big"
          ref="batteryStateCenter"
          class="st6"
          cx="19.0258236"
          cy="37.5171013"
          r="0.0708972"
        />
      </g>
      <g id="dynamic-charts_1_">
        <path
          id="daily-energy-chart_00000026128279700219883950000002246298926386432161_"
          ref="batteryStateChart"
          class="acchartcustom"
          d="
			M25.8413143,41.8055229c0.5602055-1.0730743,0.8769512-2.2934151,0.8769512-3.5877342
			c0-4.2875748-3.4757652-7.76334-7.76334-7.76334s-7.763339,3.4757652-7.763339,7.76334
			c0,1.2957726,0.3174572,2.5173988,0.8788376,3.5913467"
        />
      </g>
      <g id="arrows-out_1_" ref="batteryArrowsOut" class="mobile-scheme horizontal">
        <polygon class="st7" points="38.0296211,58.1402245 33.8994179,56.2262344 33.8994179,60.0542145 		" />
        <polygon class="st7" points="42.8783188,58.1402245 38.7481155,56.2262344 38.7481155,60.0542145 		" />
        <polygon class="st7" points="47.7270164,58.1231499 43.5968132,56.2091599 43.5968132,60.0371399 		" />
      </g>
      <g id="arrows-in_1_" ref="batteryArrowsIn" class="mobile-scheme horizontal">
        <polygon class="st7" points="36.220993,58.1316872 40.3511963,60.0456772 40.3511963,56.2176971 		" />
        <polygon class="st7" points="41.0696907,58.1146126 45.199894,60.0285988 45.199894,56.2006226 		" />
        <polygon class="st7" points="45.9183884,58.1146126 50.0485916,60.0286026 50.0485916,56.2006226 		" />
      </g>
    </g>
    <g id="photovoltaics-group" ref="photovoltaicsGroup">
      <g>
        <g>
          <path
            class="st1"
            d="M53.0748711,11.6778641h-6.5540543l-1.1335907-1.1332016h6.5544434L53.0748711,11.6778641z
				 M46.6039734,11.4770336h5.9860802l-0.7315407-0.7315407h-5.9864731L46.6039734,11.4770336z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M54.4434204,13.0464125h-6.5544472l-1.1331978-1.1335926h6.5544434L54.4434204,13.0464125z
				 M47.9721298,12.845582h5.9864731l-0.7315407-0.7319317h-5.9864731L47.9721298,12.845582z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M55.7903938,14.3929958h-6.5544434l-1.1335945-1.1332006h6.5544434L55.7903938,14.3929958z
				 M49.3191071,14.1921654h5.9864693l-0.7319336-0.7315397h-5.9864693L49.3191071,14.1921654z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M54.1225624,11.3558292c-0.0235367,0-0.0474625-0.0082369-0.0662918-0.0251036
				c-0.0415764-0.036479-0.0458908-0.1000233-0.0090218-0.1416016c0.393425-0.4463768,0.6099434-1.0202341,0.6099434-1.6148796
				c0-1.3465834-1.0955429-2.4421291-2.4421272-2.4421291c-1.2296944,0-2.2715034,0.9182501-2.4233017,2.1361761
				c-0.0066681,0.0549145-0.0541306,0.0953159-0.1121826,0.087079c-0.0549126-0.0066681-0.0941391-0.0568762-0.0870781-0.1121826
				c0.1643524-1.3179493,1.2920609-2.3119025,2.6225624-2.3119025c1.4571953,0,2.6429596,1.1857619,2.6429596,2.6429591
				c0,0.6436768-0.2345657,1.2642107-0.6601524,1.7474594C54.1778679,11.3444538,54.1504097,11.3558292,54.1225624,11.3558292z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M49.0814056,9.6746597h-1.0955467c-0.0553055,0-0.1004143-0.0451088-0.1004143-0.1004152
				c0-0.0553074,0.0451088-0.1004152,0.1004143-0.1004152h1.0955467c0.0553055,0,0.1004143,0.0451078,0.1004143,0.1004152
				C49.1818199,9.6295509,49.1367111,9.6746597,49.0814056,9.6746597z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M56.3112984,10.4101219c-0.007061,0-0.0145149-0.0007839-0.021965-0.0023537l-1.0696602-0.2392702
				c-0.0541267-0.0121593-0.088253-0.065897-0.0760956-0.1200275c0.0121613-0.0541296,0.0619774-0.0882559,0.1200294-0.0760956
				l1.0696564,0.2392702c0.0541306,0.0121593,0.0882568,0.0658979,0.0760956,0.1200275
				C56.3987694,10.3783503,56.3571892,10.4101219,56.3112984,10.4101219z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M55.0898399,8.3978958c-0.0423622,0-0.0815849-0.0270653-0.095314-0.0694275
				c-0.0172577-0.0525608,0.0113754-0.109437,0.0643272-0.1263037l1.0418091-0.3396854
				c0.0525589-0.0180435,0.1090431,0.0117674,0.1263008,0.0643282c0.0172615,0.0525613-0.0113716,0.109437-0.0643272,0.1263037
				l-1.0418053,0.3396854C55.11063,8.396327,55.1000404,8.3978958,55.0898399,8.3978958z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M54.0554848,7.1321154c-0.0215721,0-0.0431442-0.0066681-0.0615807-0.0207887
				c-0.043541-0.0341258-0.0517769-0.0972776-0.0180435-0.1412091l0.6703491-0.8668652
				c0.0341263-0.0443239,0.0972786-0.0521688,0.1412086-0.0180435c0.043541,0.0341253,0.0517769,0.0972772,0.0180435,0.1412086
				l-0.6703491,0.8668656C54.1151085,7.1187792,54.0852966,7.1321154,54.0554848,7.1321154z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M52.3974609,6.540215c-0.0019646,0-0.0039253,0-0.006279,0
				c-0.0553055-0.00353-0.0972748-0.050992-0.0941391-0.1066909l0.0666847-1.0939765
				c0.0035286-0.0529532,0.0474586-0.0941391,0.1004143-0.0941391c0.0019608,0,0.0039215,0,0.0062752,0
				c0.0553055,0.00353,0.0972786,0.050992,0.0941391,0.1066909l-0.0666809,1.0939765
				C52.4943428,6.4990292,52.4504128,6.540215,52.3974609,6.540215z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M50.648037,6.9610958c-0.0345154,0-0.0682487-0.018043-0.0870781-0.0502076L50.013382,5.9616513
				c-0.0274544-0.0482464-0.0109825-0.109437,0.0368729-0.1372867c0.0462837-0.0270648,0.1086502-0.0117674,0.1372871,0.0368714
				l0.5475769,0.9492369c0.0274544,0.0482464,0.0109825,0.109437-0.0368729,0.1372867
				C50.6825562,6.9567814,50.6652985,6.9610958,50.648037,6.9610958z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M49.5011101,8.1080256c-0.0172615,0-0.0345192-0.0043144-0.050209-0.0133362l-0.9488449-0.5479689
				c-0.0478554-0.0278497-0.0643272-0.0894322-0.0368729-0.1372867c0.0278511-0.0482459,0.0898247-0.0647206,0.1372871-0.036871
				l0.9488449,0.5479689c0.0478554,0.0278492,0.0643311,0.0894322,0.0368729,0.1372867
				C49.5693588,8.089982,49.5356255,8.1080256,49.5011101,8.1080256z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M55.3044014,12.4482365c-0.0258904,0-0.051384-0.0098066-0.0709991-0.0294189l-0.751545-0.751544
				c-0.0392227-0.0392256-0.0392227-0.1027689,0-0.1419935c0.0392265-0.0392246,0.1027718-0.0392246,0.1419945,0l0.751545,0.751545
				c0.0392265,0.0392246,0.0392265,0.1027679,0,0.1419926C55.3557854,12.4384298,55.3302879,12.4482365,55.3044014,12.4482365z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M49.6733055,9.3965559l-0.5244331-0.751152c-0.0317726-0.0455008-0.0203972-0.1082602,0.0247116-0.1400318
				c0.0451088-0.0309877,0.1074753-0.0207891,0.1400299,0.0247107l0.4091148,0.5864096l0.5864067-0.4095058
				c0.0458946-0.0317717,0.1078682-0.0207891,0.1400337,0.0247116c0.0317726,0.0455008,0.0207901,0.1082602-0.0247116,0.1400318
				L49.6733055,9.3965559z"
          />
        </g>
      </g>
      <text transform="matrix(1 0 0 1 34.4874153 21.7261753)" class="st3 st4 st5">Fotovoltika</text>
      <path
        class="actextrectangle"
        d="M64.210495,34.9667282H39.4633827c-1.1000023,0-2-0.9000015-2-2v-6.2449837
		c0-1.1000004,0.8999977-2,2-2H64.210495c1.0999985,0,2,0.8999996,2,2v6.2449837
		C66.210495,34.0667267,65.3104935,34.9667282,64.210495,34.9667282z"
      />

      <linearGradient
        id="SVGID_00000088833323794041569850000014112641261923391907_"
        gradientUnits="userSpaceOnUse"
        x1="-54.0469322"
        y1="85.2335892"
        x2="-37.9299164"
        y2="85.2335892"
        gradientTransform="matrix(1 0 0 1 117.3722 -76.7460327)"
      >
        <stop offset="0" style="stop-color:#3BA02A" />
        <stop offset="1" style="stop-color:#247C27" />
      </linearGradient>

      <path
        style="fill:none;stroke:url(#SVGID_00000088833323794041569850000014112641261923391907_);stroke-linecap:round;stroke-miterlimit:10;"
        d="
		M64.6809158,14.0151081c-0.5465698-1.0456123-0.855648-2.2350063-0.855648-3.4965897
		c0-4.1744485,3.3840599-7.5585079,7.558506-7.5585079c4.1744537,0,7.5585098,3.3840592,7.5585098,7.5585079
		c0,1.26017-0.3083878,2.4483128-0.8538132,3.4930744"
      />
      <path class="st0" d="M51.836937,53.8466911c-0.0000763,0-0.0001373-1.863739-0.0001373-4.1416435V34.9667282" />
      <g id="static-text" />
      <g id="dynamic-charts">
        <path
          id="current-power-chart"
          ref="photovoltaicsCurrentPowerChart"
          class="acchartcustom"
          d="
			M78.0884705,14.0115929c0.5454254-1.0447617,0.8538132-2.2329044,0.8538132-3.4930744
			c0-4.1744485-3.3840561-7.5585079-7.5585098-7.5585079c-4.1744461,0-7.558506,3.3840592-7.558506,7.5585079
			c0,1.2615833,0.3090782,2.4509773,0.855648,3.4965897"
        />
      </g>
      <g id="dynamic-text">

        <circle
          id="photovoltaics-center"
          ref="photovoltaicsMainCenter"
          class="st6"
          cx="51.836937"
          cy="29.8442345"
          r="0.1107828"
        />

        <circle
          id="current-power-big"
          ref="photovoltaicsCurrentPowerCenter"
          class="st6"
          cx="71.3147736"
          cy="9.645669"
          r="0.0690266"
        />
      </g>
      <g id="arrows-out" ref="photovoltaicsArrowsOut" class="mobile-scheme vertical">
        <polygon class="st7" points="51.8368683,40.0366898 53.7924957,35.8166428 49.8812447,35.8166428 		" />
        <polygon class="st7" points="51.8368683,44.9908638 53.7924957,40.770813 49.8812447,40.770813 		" />
        <polygon class="st7" points="51.8368683,49.945034 53.7924957,45.724987 49.8812447,45.724987 		" />
      </g>
    </g>
  </svg>
</template>

<style lang="css" scoped>
svg {
  &.dark {
    text {
      fill: #ffffff;
    }
    .acchartcustom {
      stroke: oklch(0 0 0 / 0.75);
    }
    .actextrectangle {
      fill: oklch(0 0 0 / 0.25);
      stroke: oklch(0 0 0 / 0.35);
    }
  }
}
</style>

<style scoped lang="css">
svg {
  .main-chart-text {
    fill: #000!important;
  }
  &.dark {
    .main-chart-text {
      fill: #fff!important;
    }
  }
  g:has(>.st7) {
    display: none;
    &.arrow-animate {
      display: unset;
    }
  }
}
</style>

<style scoped lang="css">
  .acchartcustom{fill:none;stroke:#D3D2CB;stroke-width:1.15;stroke-linecap:round;stroke-miterlimit:10;}
  .actextrectangle{fill:#FFFFFF;stroke:#C6C1B8;stroke-width:0.25;stroke-miterlimit:10;}
  .st0{fill:none;stroke:#C6C1B8;stroke-width:0.25;stroke-miterlimit:10;}
  .st1{fill:#4768AE;}
  .st2{fill:#2A2323;}
  .st3{fill:#262B36;}
  .st4{font-family:'Roboto';}
  .st5{font-size:7.0999999px;}
  .st6{fill:none;}
  .st7{fill:#C6C1B8;}
  .st8{font-size:6px;}
  .st9{fill:#4568AE;}
  .st10{fill:none;stroke:url(#SVGID_1_);stroke-linecap:round;stroke-miterlimit:10;}
  .st11{opacity:0.5;}

  .st12{fill:none;stroke:url(#SVGID_00000131338763998300544370000005393891989029336204_);stroke-linecap:round;stroke-miterlimit:10;}
  .st13{font-size:7.1024418px;}

  .st14{fill:none;stroke:url(#SVGID_00000066491585123410422630000004070021045039891126_);stroke-linecap:round;stroke-miterlimit:10;}

  .st15{fill:none;stroke:url(#SVGID_00000005244839964560330940000018206030574422134913_);stroke-linecap:round;stroke-miterlimit:10;}
</style>