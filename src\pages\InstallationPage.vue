<script lang="ts" setup>
import { useRoute } from 'vue-router';
import DeltaDataContainer from '@/pages/installation/components/DeltaDataContainer.vue';
import FlowAndDevicesDetailData from '@/pages/installation/components/FlowAndDevicesDetailData.vue';
import InstallationBar from '@/pages/installation/components/InstallationBar.vue';
import InstallationInfo from '@/pages/installation/components/InstallationInfo.vue';
import PowerAndBattery from '@/pages/installation/components/PowerAndBattery.vue';
import ChartSection from '@/pages/installation/sections/ChartSection.vue';
import EnergeticBlocks from '@/pages/installation/sections/EnergeticBlocks.vue';
import { routeMap } from '@/router/routes';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import type { InverterCurrentDataTransformed, DeltaData } from '@/util/types/api-responses';
import { PlusIcon } from 'lucide-vue-next';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import { executeWebkitMessage } from '@/util/facades/webkit-app.ts';
import type { InstallationDetailData } from './installation/types/installation-types';
import { ref } from 'vue';
import type { TuyaDevice } from './installation/types/tuya-types';
import TuyaAuthDialog from './installation/components/Tuya/TuyaAuthDialog.vue';
import type { WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';
import { useAuthStore } from '@/stores/auth-store';
// import DeviceGrid from '@/pages/installation/sections/devices/DeviceGrid.vue';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  waterHeaterCurrentData: WaterHeaterCurrentDataTransformed,
  deltaData: DeltaData,
  installationDetail: InstallationDetailData,
  tuyaDevices: TuyaDevice[],
}

const props = defineProps<Props>();
const emit = defineEmits(['installationUpdated']);
const authStore = useAuthStore();

const tuyaAuthModalShown = ref(false);

const route = useRoute();

const onAppPlusButtonClick = () => {
  const jsonMessage = JSON.stringify({
    installationId: props.installationDetail.id
  });
  executeWebkitMessage('onStartPairing', jsonMessage);
};

// const showTuyaAuthModal = () => {
//   tuyaAuthModalShown.value = true;
// };

initializeBasicBreadcrumbBehaviour(routeMap.home.meta.i18nTitle, routeMap.home.children.installation.name, true, route);
</script>

<template>
  <div class="mx-auto max-w-(--breakpoint-3xl) grid gap-4">
    <InstallationBar
      :installation="installationDetail"
      :inverter-current-data="inverterCurrentData"
      @installation-updated="emit('installationUpdated')"
    />
    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 [&>section]:rounded-xl w-full">
      <section
        class="bg-prim-col-foreground-1 h-full p-1 col-span-2 sm:col-span-1"
      >
        <InstallationInfo :inverter-current-data="inverterCurrentData" :installation-detail="installationDetail" />
      </section>
      <section
        class="bg-prim-col-foreground-1 h-full col-span-2 md:col-span-2 lg:col-span-1 grid grid-cols-2 place-items-center"
        :class="[!inverterCurrentData ? 'opacity-40 pointer-events-none' : '']"
      >
        <PowerAndBattery :inverter-current-data="inverterCurrentData" />
      </section>
      <section
        class="bg-prim-col-foreground-1 h-full row-start-4 lg:row-start-1 lg:col-start-3 col-span-full lg:col-span-2"
        :class="[!inverterCurrentData || !deltaData ? 'opacity-40 pointer-events-none' : '']"
      >
        <DeltaDataContainer
          :delta-data="deltaData"
          :inverter-current-data="inverterCurrentData"
        />
      </section>
      <section
        class="bg-prim-col-foreground-1 h-fit lg:h-full col-span-full slg:h-full row-start-3 sm:row-start-2"
        :class="[!inverterCurrentData ? 'opacity-40 pointer-events-none' : '']"
      >
        <FlowAndDevicesDetailData
          :inverter-current-data="inverterCurrentData"
          :water-heater-current-data="waterHeaterCurrentData"
          :installation-detail="installationDetail"
        />
      </section>
      <section
        class="min-h-[25rem] col-span-full relative"
        :class="[!inverterCurrentData ? 'opacity-40 pointer-events-none' : '']"
      >
        <ChartSection
          :installation="installationDetail"
        />
      </section>
      <section
        class="col-span-full"
      >
        <EnergeticBlocks
          :installation-detail="installationDetail"
          :tuya-devices="tuyaDevices"
        />
      </section>
      <!--      <section class="col-span-full">-->
      <!--        <h2 class="text-xl font-bold mb-4">Zariadenia v domácnosti</h2>-->
      <!--        <div class="grid grid-cols-6 gap-4 [&>.device]:rounded-xl [&>.device]:bg-prim-col-foreground-1 [&>.device]dark:bg-prim-col-foreground-1">-->
      <!--          <div v-for="item in [1,2,3,4,5,6]" class="device w-full h-44" />-->
      <!--        </div>-->
      <!--        <DeviceGrid />-->
      <!--      </section>-->
    </div>
    <button
      v-if="isMobileSubdomain && (installationDetail.is_owner || authStore.userHelpers.canServiceInstallation)"
      type="button"
      class="sticky ml-auto -mr-2 bottom-3 -right-3 w-20 h-20 rounded-full bg-prim-col-selected-1 z-50 flex items-center justify-center border-[0.15rem] border-prim-col-1/30"
      @click="onAppPlusButtonClick"
    >
      <PlusIcon class="w-12 h-12" />
    </button>
  </div>
  <suspense v-if="tuyaAuthModalShown">
    <TuyaAuthDialog v-model:open="tuyaAuthModalShown" :installation-id="installationDetail.id" @authenticated="emit('installationUpdated')" />
  </suspense>
</template>

<style>
.dark {
  .chart-date-input {
    color: white;
    &::-webkit-calendar-picker-indicator {
      background-color: rgb(var(--prim-col-foreground-1))!important;
    }
  }
}

.chart-date-input {
  color: black;
  &::-webkit-calendar-picker-indicator {
    background-color: rgb(var(--prim-col-foreground-contrast))!important;
  }
}
</style>
