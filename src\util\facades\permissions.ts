import { AvailablePermissions, AvailableRoles } from '@/util/types/roles-and-permissions';
import type { UserData } from '@/stores/auth-store';

export const hasAnyRole = (user: UserData | undefined, roleNames: string[]): boolean => {
  if (!user?.roles) {return false;}
  return roleNames.some(roleName => user.roles.some(role => role.name === roleName));
};

export const hasAnyPermission = (user: UserData | undefined, permissions: AvailablePermissions[]): boolean => {
  if (!user?.permissions) {return false;}
  return permissions.some(permission => user.permissions.includes(permission));
};

export const hasAllPermissions = (user: UserData | undefined, permissions: AvailablePermissions[]): boolean => {
  if (!user?.permissions) {return false;}
  return permissions.every(permission => user.permissions.includes(permission));
};

export const hasAdminAccess = (user: UserData | undefined): boolean => {
  return hasAnyRole(user, [
    AvailableRoles.SUPER_ADMIN,
    AvailableRoles.ORGANIZATION_ADMIN,
  ]);
};
