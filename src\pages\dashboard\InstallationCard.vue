<script setup lang="ts">
import { computed } from 'vue';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types.ts';

interface Props {
  installation: InstallationDetailData
}

const props = defineProps<Props>();

const pairedDevices = computed(() => props.installation.deviceInstances);
</script>

<template>
  <div
    class="block p-4 relative select-none"
  >
    <div class="flex flex-col">
      <div class="text-xs">
        {{ $t('installation.title-base') }}
      </div>
      <h2 class="font-bold text-lg">
        {{ installation.title }}
      </h2>
    </div>
    <div class="text-xs w-full flex items-center flex-wrap mt-1.5 gap-1">
      <div
        class="dark:bg-prim-col-foreground-2 bg-black/15 w-fit px-2 py-0.5 rounded-md"
      >
        {{ pairedDevices.length }} {{ $t('installation.device-counted', pairedDevices.length).toLowerCase() }}
      </div>
    </div>
  </div>
</template>
