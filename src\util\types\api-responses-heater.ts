export type ApiMetricKeysWaterHeater = 'energy_total'|'power'|'temp'|'temp_max';

export type MetricValueOverrides = object;

export type MetricValueType<K extends ApiMetricKeysWaterHeater> =
  K extends keyof MetricValueOverrides ? MetricValueOverrides[K] : number;

export type WaterHeaterMetricMap = {
  [K in ApiMetricKeysWaterHeater]: {
    name: K;
    time: string;
    type: string;
    unit: string;
    value: MetricValueType<K>;
  };
};

export type ApiMetricWaterHeater = WaterHeaterMetricMap[ApiMetricKeysWaterHeater];

export type WaterHeaterCurrentDataResponse = {
  device_id: string;
  metrics: ApiMetricWaterHeater[];
  time: string;
  expired?: boolean;
}[]

export type WaterHeaterCurrentDataTransformed = {
  metrics: WaterHeaterMetricMap;
  time: string;
  expired?: boolean;
}

export type ApiRangeMultiDataResponse = {
  data: {
    type: string;
    unit: string;
    data: {
      t: string;
      v: number;
      avg?: number;
      delta?: number;
    }[]
  };
  metric: ApiMetricKeysWaterHeater;
}[]

export type DeltaDataWaterHeater = Record<ApiMetricKeysWaterHeater, {value: number, unit: string}>;