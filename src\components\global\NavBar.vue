<script lang="ts" setup>
import { CircleUser, HelpCircle } from 'lucide-vue-next';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo.svg';
import HouseHoldPicker from '@/components/global/HouseHoldPicker.vue';
import MobileSidebarSheet from '@/components/global/MobileSidebarSheet.vue';
import GetSupportDialog from '@/components/global/GetSupportDialog.vue';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import { routeMap } from '@/router/routes';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/shadcn-components/ui/dropdown-menu';
import { useAuthStore } from '@/stores/auth-store';
import ThemeToggler from './ThemeToggler.vue';
import { useScreenWidth } from '@/composables/useScreenWidth';
import { isLightModeEnabled } from '@/composables/theme';
import { useLocale } from '@/composables/useLocale';

const { logout } = useAuthStore();
const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const { screenWidth } = useScreenWidth();
const { locale, availableLocales, setLocale } = useLocale();

const supportDialogOpened = ref(false);

const onLogoutPress = async() => {
  await router.replace({ path: routeMap.home.path });
  logout();
};

const onChangeMode = () => {
  isLightModeEnabled.value = !isLightModeEnabled.value;
};

const onSupportClick = () => {
  supportDialogOpened.value = true;
};

</script>

<template>
  <header class="sticky top-0 z-30 py-2 flex flex-col gap-2 sm:py-4 border-b border-prim-col-foreground-2 bg-prim-col-2 px-4 sm:h-auto sm:px-6">
    <div class="flex items-center gap-4 relative">
      <mobile-sidebar-sheet />
      <div v-if="route.name === routeMap.home.children.installation.name">
        <HouseHoldPicker />
      </div>
      <div class="hidden sm:block absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2">
        <SolarCloudLogo class="h-9 w-auto [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div class="ml-auto mr-0 flex items-center gap-2">
        <button
          id="support-button"
          class="hidden sm:flex items-center justify-center support-button"
          :title="$t('misc.get-support')"
          :aria-label="$t('misc.get-support')"
          @click="onSupportClick"
        >
          <HelpCircle class="h-6 w-6 text-prim-col-foreground-contrast hover:text-prim-col-selected-1 transition-colors" />
        </button>
        <ThemeToggler v-if="screenWidth > 640" />
        <DropdownMenu
          v-if="!isMobileSubdomain"
          :modal="false"
        >
          <DropdownMenuTrigger as-child>
            <ShadCnButton
              variant="default"
              size="icon"
              class="min-w-10 min-h-10 rounded-full bg-prim-col-foreground-2/50 hover:bg-prim-col-foreground-2/80"
            >
              <CircleUser class="h-5 w-5 text-white" />
              <span class="sr-only">Toggle user menu</span>
            </ShadCnButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              <div class="flex flex-col">
                <div>{{ authStore.user?.name }}</div>
                <div class="text-xs">
                  {{ authStore.user?.email }}
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuItem v-if="screenWidth <= 640" class="cursor-pointer bg-prim-col-foreground-2/20 hover:bg-prim-col-foreground-2/40 focus:bg-prim-col-foreground-2/40 focus:text-black dark:focus:text-white mb-0.5" @click.stop.prevent="onSupportClick">
              <div class="flex items-center gap-1.5 text-sm">
                <button
                  id="support-button-mob"
                  class="flex items-center justify-center support-button"
                  :aria-label="$t('misc.get-support')"
                >
                  <HelpCircle class="h-6 w-6 text-prim-col-foreground-contrast hover:text-prim-col-selected-1 transition-colors" />
                </button>
                <div>{{ $t('misc.get-support') }}</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem v-if="screenWidth <= 640" class="cursor-pointer bg-prim-col-foreground-2/20 hover:bg-prim-col-foreground-2/40 focus:bg-prim-col-foreground-2/40 focus:text-black dark:focus:text-white mb-0.5" @click.stop.prevent="onChangeMode">
              <div class="flex items-center gap-1.5 text-sm">
                <ThemeToggler />
                <div>{{ $t('misc.toggle-theme') }}</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem class="p-0 bg-transparent cursor-pointer focus:text-black dark:focus:text-white hover:bg-transparent! mb-0.5">
              <div class="w-full grid grid-cols-2 gap-0.5">
                <button
                  v-for="localeVal in availableLocales"
                  :key="localeVal"
                  :class="[locale === localeVal ? 'dark:bg-white/10 bg-black/10' : 'hover:dark:bg-white/10 hover:bg-black/10']"
                  class="w-full py-1 font-medium rounded-sm cursor-pointer"
                  @click.stop.prevent="setLocale(localeVal)"
                >
                  {{ localeVal.toUpperCase() }}
                </button>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem class="cursor-pointer bg-prim-col-foreground-2/20 hover:bg-prim-col-foreground-2/40 focus:bg-prim-col-foreground-2/40 focus:text-black dark:focus:text-white">
              <router-link
                :to="{ name: routeMap.settings.name }"
              >
                {{ $t('misc.settings') }}
              </router-link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              class="cursor-pointer bg-prim-col-foreground-2/40 hover:bg-prim-col-foreground-2/50 focus:bg-prim-col-foreground-2/50 focus:text-black dark:focus:text-white"
              @click="onLogoutPress"
            >
              {{ $t('login.log-out') }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <GetSupportDialog v-model="supportDialogOpened" />
  </header>
</template>

<style lang="css">
@import "open-props/easings";

.support-button {
  --size: 1.65rem;
  background: none;
  border: none;
  padding: 0;
  inline-size: var(--size);
  block-size: var(--size);
  aspect-ratio: 1;
  border-radius: 50%;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  outline-offset: 5px;
}

</style>
