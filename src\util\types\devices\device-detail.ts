import { type AnyDeviceKeys, DeviceTypes } from '@/util/types/devices/device-types.ts';
import i18n from '@/i18n';

export const deviceValueMap: Record<DeviceTypes, { title: string; key: keyof AnyDeviceKeys}[]> = {
  [DeviceTypes.INVERTER]: [
    { title: 'Aktuálny výkon', key: 'currentPower' },
    { title: 'Výroba dnes', key: 'dailyPower' },
    { title: 'Celk<PERSON>', key: 'totalPower' },
  ],
  [DeviceTypes.PANEL]: [
    { title: 'Počet', key: 'count' }
  ],
  [DeviceTypes.DONGLE]: [
    { title: 'Počet modulov', key: 'count' }
  ],
  [DeviceTypes.AC]: [
    { title: 'Aktuálne', key: 'currentConsumption' },
    { title: 'Spotreba dnes', key: 'todayConsumption' },
    { title: '<PERSON>lk<PERSON>', key: 'totalConsumption' },
  ],
  [DeviceTypes.DATA_LOGGER]: [
    { title: 'Aktuálne', key: 'currentPower' },
    { title: 'TPA', key: 'tpa' },
    { title: 'TS-04', key: 'ts04' },
  ],
  [DeviceTypes.WALL_PLUG]: [
    { title: 'Aktuálne', key: 'currentConsumption' },
    { title: 'Spotreba dnes', key: 'todayConsumption' },
    { title: 'Celkovo', key: 'totalConsumption' },
  ],
  [DeviceTypes.THERMOMETER]: [
    { title: 'Teplota', key: 'temperature' },
    { title: 'Vlhkosť', key: 'humidity' },
  ],
  [DeviceTypes.AC_WATER_HEATER]: [],
  [DeviceTypes.DC_WATER_HEATER]: [],
};

export const deviceTypeTitleMap: Record<DeviceTypes, string> = {
  [DeviceTypes.INVERTER]: 'Inverter',
  [DeviceTypes.PANEL]: 'Panel',
  [DeviceTypes.DATA_LOGGER]: 'Data logger',
  [DeviceTypes.DONGLE]: 'Dongle',
  [DeviceTypes.AC]: 'Klimatizacia',
  [DeviceTypes.THERMOMETER]: 'Teplomer',
  [DeviceTypes.WALL_PLUG]: 'Smart zásuvka',
  [DeviceTypes.AC_WATER_HEATER]: 'AC Ohrev vody',
  [DeviceTypes.DC_WATER_HEATER]: 'DC Ohrev vody',
};

export const installationBlocks = [
  {
    title: i18n.global.t('installation.photovoltaics'),
    subtitle: i18n.global.t('installation.my-plant'),
    deviceTypes: [DeviceTypes.INVERTER, DeviceTypes.PANEL, DeviceTypes.DONGLE, DeviceTypes.DATA_LOGGER, DeviceTypes.DATA_LOGGER, DeviceTypes.AC_WATER_HEATER],
  },
  {
    title: i18n.global.t('installation.household'),
    subtitle: i18n.global.t('installation.my-electroinstallation'),
    deviceTypes: [DeviceTypes.WALL_PLUG, DeviceTypes.THERMOMETER, DeviceTypes.AC],
  }
];

export const deviceTypeDetailAllowed = [DeviceTypes.INVERTER, DeviceTypes.AC_WATER_HEATER];