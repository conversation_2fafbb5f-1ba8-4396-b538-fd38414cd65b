<script lang="ts" setup>
import { ChevronLeft } from 'lucide-vue-next';
import { useRouter } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo-w-text.svg?skipsvgo';
import ForgotPasswordForm from '@/pages/forgot-password/ForgotPasswordForm.vue';
import { routeMap } from '@/router/routes';
import { isMobileSubdomain } from '@/composables/subdomain.ts';

const router = useRouter();
</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2 bg-prim-col-1 lg:bg-transparent">
      <router-link
        :to="{name: routeMap.home.children.dashboardInstallations.name}"
        class="hidden bg-prim-col-2 lg:flex items-center justify-center"
      >
        <router-link
          :to="{name: routeMap.home.children.dashboardInstallations.name}"
          class="hidden bg-prim-col-2 lg:flex items-center justify-center"
        >
          <SolarCloudLogo class="h-52 [&_.st1]:fill-prim-col-selected-1!" />
        </router-link>
      </router-link>
      <div class="flex items-center justify-center py-12 bg-prim-col-1">
        <ForgotPasswordForm />
      </div>
    </div>
    <div
      v-if="!isMobileSubdomain"
      class="cursor-pointer bg-prim-col-foreground-1 hover:bg-prim-col-foreground-2/80 active:bg-prim-col-foreground-2/80 absolute top-6 right-6 sm:top-4 sm:right-4 rounded-full w-10 h-10 flex items-center justify-center"
      @click="router.back()"
    >
      <ChevronLeft class="relative right-px text-white" />
    </div>
  </div>
</template>
