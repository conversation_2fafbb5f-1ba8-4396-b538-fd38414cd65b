<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeft, ArrowRight, Check } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
import { routeMap } from '@/router/routes';
import { resetBreadcrumb } from '@/util/facades/breadcrumb';
import UserSelectionStep from '@/components/wizard/UserSelectionStep.vue';
import ExistingUserSelectionStep from '@/components/wizard/ExistingUserSelectionStep.vue';
import NewUserCreationStep from '@/components/wizard/NewUserCreationStep.vue';
import InstallationCreationStep from '@/components/wizard/InstallationCreationStep.vue';
import type { UserData } from '@/stores/auth-store';

enum WizardStep {
  USER_SELECTION = 1,
  USER_DETAILS = 2,
  INSTALLATION_CREATION = 3,
  SUCCESS = 4,
}

enum UserSelectionType {
  EXISTING = 'existing',
  NEW = 'new',
}

interface WizardState {
  currentStep: WizardStep;
  userSelectionType: UserSelectionType | null;
  selectedUser: UserData | null;
  createdUser: UserData | null;
  installationId: string | null;
}

const router = useRouter();
const wizardState = ref<WizardState>({
  currentStep: WizardStep.USER_SELECTION,
  userSelectionType: null,
  selectedUser: null,
  createdUser: null,
  installationId: null,
});

resetBreadcrumb();

const currentUser = computed(() => {
  return wizardState.value.selectedUser || wizardState.value.createdUser;
});

const canGoNext = computed(() => {
  switch (wizardState.value.currentStep) {
    case WizardStep.USER_SELECTION:
      return wizardState.value.userSelectionType !== null;
    case WizardStep.USER_DETAILS:
      return currentUser.value !== null;
    case WizardStep.INSTALLATION_CREATION:
      return false; // Handled by form submission
    case WizardStep.SUCCESS:
      return false;
    default:
      return false;
  }
});

const canGoBack = computed(() => {
  return wizardState.value.currentStep > WizardStep.USER_SELECTION;
});

const stepTitle = computed(() => {
  switch (wizardState.value.currentStep) {
    case WizardStep.USER_SELECTION:
      return 'admin.new-installation.step1.title';
    case WizardStep.USER_DETAILS:
      return wizardState.value.userSelectionType === UserSelectionType.EXISTING
        ? 'admin.new-installation.step2a.title'
        : 'admin.new-installation.step2b.title';
    case WizardStep.INSTALLATION_CREATION:
      return 'admin.new-installation.step3.title';
    case WizardStep.SUCCESS:
      return 'admin.new-installation.step4.title';
    default:
      return '';
  }
});

const handleUserSelectionTypeChange = (type: UserSelectionType) => {
  wizardState.value.userSelectionType = type;
  wizardState.value.selectedUser = null;
  wizardState.value.createdUser = null;
};

const handleUserSelected = (user: UserData) => {
  wizardState.value.selectedUser = user;
};

const handleUserCreated = (user: UserData) => {
  wizardState.value.createdUser = user;
};

const handleInstallationCreated = (installationId: string) => {
  wizardState.value.installationId = installationId;
  wizardState.value.currentStep = WizardStep.SUCCESS;
};

const goNext = () => {
  if (!canGoNext.value) {
    return;
  }

  if (wizardState.value.currentStep === WizardStep.USER_SELECTION) {
    wizardState.value.currentStep = WizardStep.USER_DETAILS;
  } else if (wizardState.value.currentStep === WizardStep.USER_DETAILS) {
    wizardState.value.currentStep = WizardStep.INSTALLATION_CREATION;
  }
};

const goBack = () => {
  if (!canGoBack.value) {return;}

  if (wizardState.value.currentStep === WizardStep.USER_DETAILS) {
    wizardState.value.currentStep = WizardStep.USER_SELECTION;
  } else if (wizardState.value.currentStep === WizardStep.INSTALLATION_CREATION) {
    wizardState.value.currentStep = WizardStep.USER_DETAILS;
  }
};

const goToInstallationDetail = () => {
  if (wizardState.value.installationId) {
    router.push({
      name: routeMap.home.children.installation.name,
      params: { installationId: wizardState.value.installationId },
    });
  }
};

const goBackToInstallations = () => {
  router.push({ name: routeMap.service.children.installations.name });
};
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          {{ $t('service.new-installation.title') }}
        </h1>
        <p class="text-muted-foreground">
          {{ $t('admin.new-installation.description') }}
        </p>
      </div>
      <Button
        variant="outline"
        @click="goBackToInstallations"
      >
        <ArrowLeft class="h-4 w-4 mr-2" />
        {{ $t('misc.back') }}
      </Button>
    </div>

    <div class="flex items-center justify-center py-4">
      <div
        v-for="step in 4"
        :key="step"
        class="flex items-center"
      >
        <div
          class="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
          :class="{
            'bg-primary border-primary text-primary-foreground': step < wizardState.currentStep || step === wizardState.currentStep,
            'border-1 border-muted-foreground text-muted-foreground': step > wizardState.currentStep,
          }"
        >
          <Check
            v-if="step < wizardState.currentStep"
            class="h-4 w-4"
          />
          <span v-else>{{ step }}</span>
        </div>
        <div
          v-if="step < 4"
          class="w-8 sm:w-16 h-0.5 mx-2 transition-colors"
          :class="{
            'bg-primary': step < wizardState.currentStep,
            'bg-muted-foreground': step >= wizardState.currentStep,
          }"
        />
      </div>
    </div>

    <!-- Step Content -->
    <div class="dark:bg-card bg-white rounded-xl p-6 min-h-[400px]">
      <h2 class="text-xl font-semibold mb-4">
        {{ $t(stepTitle) }}
      </h2>

      <!-- Step 1: User Selection -->
      <UserSelectionStep
        v-if="wizardState.currentStep === WizardStep.USER_SELECTION"
        :selected-type="wizardState.userSelectionType"
        @selection-changed="handleUserSelectionTypeChange"
      />

      <!-- Step 2a: Existing User Selection -->
      <ExistingUserSelectionStep
        v-else-if="wizardState.currentStep === WizardStep.USER_DETAILS && wizardState.userSelectionType === UserSelectionType.EXISTING"
        :selected-user="wizardState.selectedUser"
        @user-selected="handleUserSelected"
      />

      <!-- Step 2b: New User Creation -->
      <NewUserCreationStep
        v-else-if="wizardState.currentStep === WizardStep.USER_DETAILS && wizardState.userSelectionType === UserSelectionType.NEW"
        @user-created="handleUserCreated"
      />

      <!-- Step 3: Installation Creation -->
      <InstallationCreationStep
        v-else-if="wizardState.currentStep === WizardStep.INSTALLATION_CREATION"
        :selected-user="currentUser"
        @installation-created="handleInstallationCreated"
      />

      <!-- Step 4: Success -->
      <div
        v-else-if="wizardState.currentStep === WizardStep.SUCCESS"
        class="text-center space-y-4"
      >
        <div class="text-green-600 text-6xl">
          <Check class="h-16 w-16 mx-auto" />
        </div>
        <h3 class="text-2xl font-bold text-green-600">
          {{ $t('admin.new-installation.success.title') }}
        </h3>
        <p class="text-muted-foreground">
          {{ $t('admin.new-installation.success.description') }}
        </p>
        <div class="flex justify-center space-x-4 pt-4">
          <Button
            variant="outline"
            @click="goBackToInstallations"
          >
            {{ $t('admin.new-installation.success.back-to-list') }}
          </Button>
          <Button @click="goToInstallationDetail">
            {{ $t('admin.new-installation.success.view-installation') }}
          </Button>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div
      v-if="wizardState.currentStep !== WizardStep.SUCCESS"
      class="flex justify-between"
    >
      <Button
        variant="outline"
        :disabled="!canGoBack"
        @click="goBack"
      >
        <ArrowLeft class="h-4 w-4 mr-2" />
        {{ $t('misc.back') }}
      </Button>
      <Button
        :disabled="!canGoNext"
        @click="goNext"
      >
        {{ $t('misc.next') }}
        <ArrowRight class="h-4 w-4 ml-2" />
      </Button>
    </div>
  </div>
</template>
