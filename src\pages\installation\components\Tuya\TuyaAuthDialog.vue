<template>
  <Dialog v-model:open="open">
    <DialogContent :as-child="true" class="dialog-content bg-white border-slate-200 w-[600px] max-w-[90vw]">
      <div>
        <DialogHeader class="flex flex-row items-center justify-between pb-4">
          <DialogTitle
            class="text-xl font-semibold text-slate-900 flex items-center gap-3"
          >
            <div class="p-2 bg-orange-100 rounded-lg">
              <Shield class="h-5 w-5 text-orange-600" />
            </div>
            Tuya Cloud Authorization
          </DialogTitle>
          <ShadCnButton
            variant="ghost"
            size="icon"
            class="h-8 w-8 bg-slate-100 hover:bg-slate-200 flex-shrink-0"
            @click="handleClose"
          >
            <X class="h-4 w-4 fill-black text-slate-700" />
          </ShadCnButton>
        </DialogHeader>

        <!-- Container with consistent height for smooth transitions -->
        <div class="min-h-[440px] flex flex-col justify-center">
          <Transition name="slide" mode="out-in">
            <!-- Step 1: Prompt -->
            <div v-if="currentStep === 'prompt'" key="prompt" class="space-y-4">
              <Card class="border-slate-200 bg-slate-50">
                <CardContent class="p-6 text-center space-y-4">
                  <div
                    class="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center"
                  >
                    <QrCode class="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 class="text-lg font-medium text-slate-900">
                    Connect Your Tuya Account
                  </h3>
                  <p class="text-slate-700 text-sm leading-relaxed px-4">
                    To access your smart devices, we need to authorize access to
                    your Tuya Cloud account. Click below to generate a QR code
                    for secure authentication.
                  </p>
                  <div
                    class="w-fit flex items-center gap-2 text-xs text-slate-700 bg-slate-200 p-3 rounded-lg mx-auto"
                  >
                    <Shield class="h-4 w-4 text-orange-600" />
                    <span>Your credentials are encrypted and secure</span>
                  </div>
                </CardContent>
              </Card>
              <ShadCnButton
                class="w-full bg-orange-500 hover:bg-orange-600 text-white border-0 h-11"
                @click="fetchQRCode"
              >
                Generate QR Code
              </ShadCnButton>
            </div>

            <!-- Step 2: QR Code -->
            <div
              v-else-if="currentStep === 'qr-code'"
              key="qr-code"
            >
              <Card class="border-slate-200 bg-white">
                <CardContent class="p-6 text-center space-y-6">
                  <div class="space-y-3">
                    <h3 class="text-lg font-medium text-slate-900">
                      Scan QR Code
                    </h3>
                    <ul class="flex flex-col gap-2 text-center items-center">
                      <li class="text-slate-700 text-sm leading-relaxed">
                        1. Open the Tuya Smart app on your phone
                      </li>
                      <li class="text-slate-700 text-sm leading-relaxed">
                        2. Click on <CircleUserRound class="inline" /> (profile) icon in the menu
                      </li>
                      <li class="text-slate-700 text-sm leading-relaxed">
                        3. Click on <ScanLine class="inline" /> (scan) icon in the upper right corner
                      </li>
                      <li class="text-slate-700 text-sm leading-relaxed">
                        4. Scan this QR code
                      </li>
                      <li class="text-slate-700 text-sm leading-relaxed">
                        5. Click <span class="inline-block px-2 py-0.5 bg-blue-900 rounded-md text-white">Confirm login</span> button for "antikhome"
                      </li>
                    </ul>
                  </div>

                  <div
                    class="relative mx-auto"
                  >
                    <!-- QR Code Placeholder -->
                    <div
                      class="w-fit h-fit p-6 bg-orange-500/15 rounded-2xl flex items-center justify-center relative mx-auto"
                    >
                      <img :src="qrCodeUrl" alt="QR Code" class="rounded border shadow w-48 h-48">
                      <div
                        class="absolute -translate-y-1/2 bg-orange-500/50 h-1 w-[calc(100%+2rem)] rounded-lg flex items-center justify-center animate-updown"
                      />
                    </div>
                  </div>

                  <div class="space-y-3">
                    <div
                      class="flex items-center justify-center gap-2 text-xs text-slate-600"
                    >
                      <Smartphone class="h-4 w-4" />
                      <span>
                        {{
                          isScanning ? "Waiting for scan..." : "Ready to scan"
                        }}
                      </span>
                    </div>

                    <Transition name="fade">
                      <div
                        v-if="isScanning"
                        class="flex items-center justify-center gap-2 text-sm text-slate-700"
                      >
                        <div
                          class="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"
                        />
                        Authenticating...
                      </div>
                    </Transition>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Step 3: Success -->
            <div
              v-else-if="currentStep === 'success'"
              key="success"
              class="space-y-4"
            >
              <Card class="border-green-200 bg-green-50">
                <CardContent class="p-6 text-center space-y-6">
                  <div
                    class="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle
                      class="h-12 w-12 text-green-600 animate-bounce"
                    />
                  </div>

                  <div class="space-y-2">
                    <h3 class="text-lg font-medium text-green-800">
                      Authorization Successful!
                    </h3>
                    <p class="text-green-700 text-sm leading-relaxed px-4">
                      Your Tuya account has been successfully connected. We can
                      now access and display all your smart devices from the
                      Tuya Cloud.
                    </p>
                  </div>

                  <div
                    class="bg-green-100 p-4 rounded-lg text-sm text-green-800"
                  >
                    🎉 Loading your devices...
                  </div>
                </CardContent>
              </Card>

              <ShadCnButton
                class="w-full bg-green-600 hover:bg-green-700 text-white border-0 h-11"
                @click="handleClose"
              >
                Continue to Dashboard
              </ShadCnButton>
            </div>

            <!-- Step 4: Failure -->
            <div
              v-else-if="currentStep === 'failure'"
              key="failure"
              class="space-y-4"
            >
              <Card class="border-red-200 bg-red-50">
                <CardContent class="p-6 text-center space-y-6">
                  <div
                    class="mx-auto w-20 h-20 bg-red-100 rounded-full flex items-center justify-center"
                  >
                    <XCircle class="h-12 w-12 text-red-600 animate-pulse" />
                  </div>

                  <div class="space-y-2">
                    <h3 class="text-lg font-medium text-red-800">
                      Authorization Failed
                    </h3>
                    <p v-if="error.isDuplicate" class="text-red-700 text-sm leading-relaxed px-4">
                      We couldn't complete the authorization process. This is because the Tuya account is already registered to another installation. Please use a different Tuya account.
                    </p>
                    <p v-else class="text-red-700 text-sm leading-relaxed px-4">
                      We couldn't complete the authorization process. This might
                      be due to a network issue or the QR code expired. Please
                      try generating a new QR code.
                    </p>
                  </div>

                  <div v-if="!error.isDuplicate" class="bg-red-100 p-4 rounded-lg text-sm text-red-800">
                    💡 Make sure your phone has internet connection and the Tuya
                    Smart app is up to date.
                  </div>
                </CardContent>
              </Card>

              <div class="space-y-3">
                <ShadCnButton
                  class="w-full bg-orange-500 hover:bg-orange-600 text-white border-0 h-11"
                  @click="handleRetry"
                >
                  <RotateCcw class="h-4 w-4 mr-2" />
                  Try Again
                </ShadCnButton>
                <ShadCnButton
                  variant="outline"
                  class="w-full border-slate-300 text-slate-700 hover:bg-slate-50 h-11"
                  @click="handleClose"
                >
                  Cancel
                </ShadCnButton>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, reactive } from 'vue';
import Dialog from '@/shadcn-components/ui/dialog/Dialog.vue';
import DialogContent from '@/shadcn-components/ui/dialog/DialogContent.vue';
import DialogHeader from '@/shadcn-components/ui/dialog/DialogHeader.vue';
import DialogTitle from '@/shadcn-components/ui/dialog/DialogTitle.vue';
import ShadCnButton from '@/shadcn-components/ui/button/Button.vue';
import Card from '@/shadcn-components/ui/card/Card.vue';
import CardContent from '@/shadcn-components/ui/card/CardContent.vue';
import {
  QrCode,
  CheckCircle,
  XCircle,
  RotateCcw,
  Smartphone,
  Shield,
  X,
  CircleUserRound,
  ScanLine,
} from 'lucide-vue-next';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';

type AuthStep = 'prompt' | 'qr-code' | 'success' | 'failure';

interface Props {
  installationId: string;
}

const props = defineProps<Props>();
const open = defineModel<boolean>('open', { required: true });
const emit = defineEmits(['authenticated']);

const currentStep = ref<AuthStep>('prompt');
const qrCodeUrl = ref('');
const isScanning = ref(false);
const authenticated = ref(false);
const error = reactive({
  value: false,
  isDuplicate: false,
});
const qrToken = ref('');
const pollingInterval = ref<ReturnType<typeof setInterval>>();

const fetchQRCode = async() => {
  try {
    const { data } = await customAxios.post(`/tuya/${props.installationId}/generate-qr`);
    qrToken.value = data.qr_token;
    qrCodeUrl.value = data.qr_url;

    currentStep.value = 'qr-code';
    isScanning.value = true;
    startPolling();
  } catch {
    error.value = true;
    currentStep.value = 'failure';
    deployToast(ToastType.ERROR, { text: 'Failed to generate QR code.' });
  }
};

const startPolling = () => {
  stopPolling();

  pollingInterval.value = setInterval(async() => {
    try {
      const { data } = await customAxios.post(`/tuya/${props.installationId}/check-qr-status`, {
        qr_token: qrToken.value,
      });

      if (data.authenticated) {
        authenticated.value = true;
        isScanning.value = false;
        stopPolling();
        currentStep.value = 'success';
        deployToast(ToastType.SUCCESS, { text: 'Successfully connected to Tuya.' });
        emit('authenticated');
      }
    } catch (e: any) {
      console.error(e);
      stopPolling();
      isScanning.value = false;
      error.value = true;
      if (e.response?.status === 409) {
        error.isDuplicate = true;
        deployToast(ToastType.ERROR, { text: 'This Tuya account is already registered to other installation.' });
      } else {
        error.isDuplicate = false;
      }
      currentStep.value = 'failure';
    }
  }, 3000);
};

const stopPolling = () => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
    pollingInterval.value = undefined;
  }
};

const handleRetry = () => {
  stopPolling();
  error.value = false;
  authenticated.value = false;
  qrCodeUrl.value = '';
  qrToken.value = '';
  currentStep.value = 'prompt';
};

const handleClose = () => {
  open.value = false;
  stopPolling();
  setTimeout(() => {
    currentStep.value = 'prompt';
    qrCodeUrl.value = '';
    qrToken.value = '';
    isScanning.value = false;
    authenticated.value = false;
    error.value = false;
  }, 200);
};

onBeforeUnmount(() => {
  stopPolling();
});
</script>

<style scoped>
/* Smooth slide transition using mode="out-in" */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* Fade transition for dynamic content */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Can go in scoped style or a global CSS file */
.dialog-content[data-state='open'] {
  animation: fadeIn 200ms ease-out;
}

.dialog-content[data-state='closed'] {
  animation: fadeOut 150ms ease-in forwards;
}

.animate-updown {
  animation: updown 2s ease-in-out infinite;
}

@keyframes updown {
  0% { opacity: 0;top: 5%; }
  50% { opacity: 1 }
  100% { opacity: 0;top: 95%; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.98); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: scale(1); }
  to { opacity: 0; transform: scale(0.98); }
}

</style>
