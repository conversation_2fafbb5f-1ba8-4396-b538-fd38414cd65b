<script lang="ts" setup>
import EnergyJSON from '@/assets/lottie/energy-solar.json';
import <PERSON>rrorJSON from '@/assets/lottie/error.json';
import UpdateJSON from '@/assets/lottie/update.json';
import { type InverterCurrentDataTransformed, WorkMode } from '@/util/types/api-responses';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  showCode?: boolean,
}

withDefaults(defineProps<Props>(), {
  showCode: true,
});

</script>

<template>
  <div
    class="flex items-center rounded-full px-2 py-1 bg-opacity-70"
    :class="[
      inverterCurrentData.metrics.work_mode.value === WorkMode.STANDBY ? 'bg-gray-400'
      : inverterCurrentData.metrics.work_mode.value === WorkMode['ON GRID'] ? 'bg-green-500'
        : inverterCurrentData.metrics.work_mode.value === WorkMode['OFF GRID'] ? 'bg-gray-400'
          : inverterCurrentData.metrics.work_mode.value === WorkMode.ERROR ? 'bg-red-500'
            : inverterCurrentData.metrics.work_mode.value === WorkMode.UPGRADING ? 'bg-yellow-500'
              : 'bg-gray-400'
    ]"
  >
    <div
      v-if="WorkMode.ERROR !== inverterCurrentData.metrics.work_mode.value"
      class="relative h-6 w-6 [&_svg_*]:fill-black [&_svg_*]:stroke-black dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white mr-1"
    >
      <LottieAnimation
        v-if="[WorkMode.STANDBY, WorkMode['ON GRID'], WorkMode['OFF GRID'], WorkMode['POWER ON SELF CHECK']].includes(inverterCurrentData.metrics.work_mode.value)"
        :animation-data="EnergyJSON"
        :height="`100%`"
        :width="`100%`"
        :speed="[WorkMode.STANDBY, WorkMode['OFF GRID'], WorkMode['POWER ON SELF CHECK']].includes(inverterCurrentData.metrics.work_mode.value) ? 0 : 1"
      />
      <LottieAnimation
        v-else-if="WorkMode.UPGRADING === inverterCurrentData.metrics.work_mode.value"
        :animation-data="UpdateJSON"
        :height="`100%`"
        :width="`100%`"
      />
      <LottieAnimation
        v-else-if="WorkMode.ERROR === inverterCurrentData.metrics.work_mode.value"
        :animation-data="ErrorJSON"
        :height="`100%`"
        :width="`100%`"
      />
    </div>
    <div
      v-else
      class="relative h-11 w-11 [&_svg_*]:stroke-black dark:[&_svg_*]:stroke-white mr-1"
    >
      <LottieAnimation
        :animation-data="ErrorJSON"
        :height="`100%`"
        :width="`100%`"
        :speed="1"
      />
    </div>
    <div>{{ WorkMode[inverterCurrentData.metrics.work_mode.value as keyof typeof WorkMode] }} <span v-if="showCode">({{ inverterCurrentData.metrics.work_mode.value }})</span></div>
  </div>
</template>
