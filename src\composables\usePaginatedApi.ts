import { ref, watch, type Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDebounce } from '@vueuse/core';
import { customAxios } from '@/util/axios';
import { ComponentStateType } from '@/util/types/components';
import { deployToast, ToastType } from '@/util/toast';
import { useI18n } from 'vue-i18n';

export interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

export interface PaginationLinks {
  first: string;
  last: string;
  prev: string | null;
  next: string | null;
}

export interface ApiPaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
  links: PaginationLinks;
}

export interface UsePaginatedApiOptions {
  endpoint: string;
  initialPerPage?: number;
  searchParam?: string;
  debounceMs?: number;
  useUrlPagination?: boolean;
  errorMessages?: {
    fetchError?: string;
    accessDenied?: string;
  };
  customFilters?: Ref<Record<string, any>>;
}

export function usePaginatedApi<T>(options: UsePaginatedApiOptions) {
  const {
    endpoint,
    initialPerPage = 15,
    searchParam = 'search_term',
    debounceMs = 300,
    useUrlPagination = false,
    errorMessages = {},
    customFilters
  } = options;

  const route = useRoute();
  const router = useRouter();
  const { t } = useI18n();

  // Reactive state
  const data = ref<T[]>([]) as Ref<T[]>;
  const componentState = ref(ComponentStateType.LOADING);
  const currentPage = ref(1);
  const totalPages = ref(1);
  const totalItems = ref(0);
  const perPage = ref(initialPerPage);
  const searchTerm = ref('');

  const paginationMeta = ref<PaginationMeta>({
    current_page: 1,
    from: 0,
    last_page: 1,
    per_page: initialPerPage,
    to: 0,
    total: 0,
  });

  const paginationLinks = ref<PaginationLinks>({
    first: '',
    last: '',
    prev: null,
    next: null,
  });

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, debounceMs);

  // Fetch data function
  const fetchData = async(page: number = 1, search?: string) => {
    componentState.value = ComponentStateType.LOADING;

    try {
      const params: Record<string, any> = {
        page,
        limit: perPage.value,
      };

      if (search && search.trim()) {
        params[searchParam] = search.trim();
      }

      // Add custom filters if provided
      if (customFilters?.value) {
        Object.entries(customFilters.value).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            params[key] = value;
          }
        });
      }

      const response = await customAxios.get<ApiPaginatedResponse<T>>(endpoint, {
        params,
      });

      data.value = response.data.data;
      paginationMeta.value = response.data.meta;
      paginationLinks.value = response.data.links;

      currentPage.value = response.data.meta.current_page;
      totalPages.value = response.data.meta.last_page;
      totalItems.value = response.data.meta.total;

      componentState.value = ComponentStateType.OK;
    } catch (error: any) {
      console.error(`Failed to fetch data from ${endpoint}:`, error);

      if (error?.response?.status === 403) {
        componentState.value = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
        deployToast(ToastType.ERROR, {
          text: errorMessages.accessDenied || t('misc.access-denied'),
          timeout: 6000,
        });
      } else {
        componentState.value = ComponentStateType.ERROR;
        deployToast(ToastType.ERROR, {
          text: errorMessages.fetchError || t('misc.failed-to-get-data'),
          timeout: 6000,
        });
      }
    }
  };

  // Page change handler
  const handlePageChange = (page: number) => {
    if (page !== currentPage.value && page >= 1 && page <= totalPages.value) {
      if (useUrlPagination) {
        router.push({
          name: route.name!,
          query: { ...route.query, page: page.toString() },
        });
      } else {
        fetchData(page, debouncedSearchTerm.value);
      }
    }
  };

  // Per page change handler
  const handlePerPageChange = async(newPerPage: number) => {
    perPage.value = newPerPage;
    currentPage.value = 1;
    await fetchData(1, searchTerm.value);

    if (useUrlPagination) {
      router.push({
        name: route.name!,
        query: { ...route.query, page: undefined },
      });
    }
  };

  // Search handler
  const handleSearch = async(newSearchTerm: string) => {
    searchTerm.value = newSearchTerm;
    currentPage.value = 1;
    await fetchData(1, newSearchTerm);
  };

  // Refresh handler
  const refresh = () => {
    fetchData(currentPage.value, debouncedSearchTerm.value);
  };

  // Watch for custom filters changes
  if (customFilters) {
    watch(customFilters, () => {
      currentPage.value = 1;
      fetchData(1, debouncedSearchTerm.value);
    }, { deep: true });
  }

  // Watch for debounced search term changes
  watch(debouncedSearchTerm, newValue => {
    currentPage.value = 1;
    fetchData(1, newValue);
  });

  // Watch for route changes (only if URL pagination is enabled)
  if (useUrlPagination) {
    watch(() => route.query.page, newPage => {
      const page = newPage ? parseInt(newPage as string, 10) : 1;
      if (page !== currentPage.value && page >= 1) {
        fetchData(page, searchTerm.value);
      }
    }, { immediate: false });
  }

  // Initialize function
  const initialize = () => {
    const initialPage = useUrlPagination && route.query.page
      ? parseInt(route.query.page as string, 10)
      : 1;
    fetchData(initialPage);
  };

  // Clear search function
  const clearSearch = () => {
    searchTerm.value = '';
  };

  return {
    // Reactive state
    data,
    componentState,
    currentPage,
    totalPages,
    totalItems,
    perPage,
    searchTerm,
    debouncedSearchTerm,
    paginationMeta,
    paginationLinks,

    // Methods
    fetchData,
    handlePageChange,
    handlePerPageChange,
    handleSearch,
    refresh,
    initialize,
    clearSearch,
  };
}
