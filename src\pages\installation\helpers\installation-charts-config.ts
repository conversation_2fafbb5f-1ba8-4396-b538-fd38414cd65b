import { isLightModeEnabled } from '@/composables/theme';
import type { ApiMetricKeys } from '@/util/types/api-responses';
import { computed } from 'vue';

export const yAxisCounters = computed(() => (
  {
    yAxis: [
      {
        type: 'value',
        name: 'W',
        position: 'left',
        offset: 0,
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: isLightModeEnabled ? '#6947d4' : '#bfadf9',
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            opacity: '0.2',
            color: isLightModeEnabled.value ? '#000' : '#fff',
          },
        },
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '%',
        position: 'right',
        offset: 0,
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#00e762'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            opacity: '0.2',
            color: isLightModeEnabled.value ? '#000' : '#fff',
          },
        },
        axisLabel: {
          formatter: '{value}'
        },
        min: 0,
        interval: 20,
        max: 100,
      },
    ],
    metricToYAxis: {
      atk_solar_power: 0,
      atk_battery_power: 0,
      atk_grid_power: 0,
      atk_battery_charge: 1,
    },
  }
));

export const yAxisFloats = computed(() => (
  {
    yAxis: [{
      type: 'value',
      name: 'kWh',
      position: 'left',
      offset: 0,
      alignTicks: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: isLightModeEnabled ? '#6947d4' : '#bfadf9'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2',
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
      },
      axisLabel: {
        formatter: '{value}'
      }
    }],
  }
));

export const metricColorMap: Record<ApiMetricKeys, string> = {
  atk_grid_energy_buy: '#e74c3c', // Red (Buy from grid)
  atk_grid_energy_sell: '#27ae60', // Green (Sell to grid)
  atk_solar_energy: '#f1c40f', // Yellow (Solar energy)
  atk_solar_power: '#f1c40f', // Yellow (same as solar energy)
  atk_battery_power: '#e67e22', // Orange-Red (Battery, discharge)
  atk_battery_charge: '#2ecc71', // Green (Battery, charge)
  atk_grid_power: '#b559dc',
  atk_home_power: '#ff0000',

  pv1_voltage:  '#2980b9',
  pv1_current:  '#16a085',
  pv1_power:    '#3498db',
  pv2_voltage:  '#8e44ad',
  pv2_current:  '#00b894',
  pv2_power:    '#6c5ce7',
  pv3_voltage:  '#0097e6',
  pv3_current:  '#00cec9',
  pv3_power:    '#00a8ff',
  pv4_voltage:  '#fdcb6e',
  pv4_current:  '#e17055',
  pv4_power:    '#ffeaa7',

  pv_mode:      '#636e72',

  // === Grid, phases (greens & oranges) ===
  grid_l1_voltage:     '#27ae60',
  grid_l1_current:     '#00b894',
  grid_l1_frequency:   '#00cec9',
  grid_l1_power:       '#00bfae',

  grid_l2_voltage:     '#f39c12',
  grid_l2_current:     '#e67e22',
  grid_l2_frequency:   '#fab1a0',
  grid_l2_power:       '#fdcb6e',

  grid_l3_voltage:     '#e17055',
  grid_l3_current:     '#d35400',
  grid_l3_frequency:   '#fdcb6e',
  grid_l3_power:       '#fd9644',

  grid_mode:           '#636e72',

  // === Total/Active/Reactive Power (purples & deep blue) ===
  total_inv_power:     '#6c5ce7',
  ac_active_power:     '#0984e3',
  ac_reactive_power:   '#a29bfe',
  ac_apparent_power:   '#b2bec3',

  // === Backup/Load (muted pinks & browns) ===
  backup_l1_load_voltage:     '#dfe6e9',
  backup_l1_load_current:     '#fab1a0',
  backup_l1_load_frequency:   '#e17055',
  backup_l1_load_mode:        '#636e72',
  backup_l1_load_power:       '#fd79a8',

  backup_l2_load_voltage:     '#a29bfe',
  backup_l2_load_current:     '#00b894',
  backup_l2_load_frequency:   '#81ecec',
  backup_l2_load_power:       '#00cec9',

  backup_l3_load_voltage:     '#636e72',
  backup_l3_load_current:     '#b2bec3',
  backup_l3_load_frequency:   '#fdcb6e',
  backup_l3_load_power:       '#ffeaa7',

  l1_load_power:        '#d35400',
  l2_load_power:        '#00b894',
  l3_load_power:        '#8e44ad',
  backup_total_load_power: '#b2bec3',
  total_load_power:     '#636e72',
  ups_load_percent:     '#fd79a8',

  // === Temperatures (oranges, reds, yellow) ===
  air_temperature:         '#f1c40f',
  module_temperature:      '#e67e22',
  radiator_temperature:    '#e74c3c',

  // === Battery (deep green & blue) ===
  battery_voltage:         '#00b894',
  battery_current:         '#16a085',
  battery_mode:            '#636e72',

  // === Warning/Error/Status (dark greys & reds) ===
  warning_code:            '#e17055',
  safety_country:          '#636e72',
  work_mode:               '#636e72',
  inverter_working_state:  '#636e72',
  operation_mode:          '#636e72',
  error_message:           '#e74c3c',
  cpld_warning_code:       '#fd79a8',
  w_charger_ctrl_flg:      '#636e72',
  meter_connect_status:    '#636e72',
  meter_communication_status: '#636e72',

  // === Meter (deep blue & purple) ===
  meter_active_power_l1:        '#6c5ce7',
  meter_active_power_l2:        '#0984e3',
  meter_active_power_l3:        '#00b894',
  meter_total_reactive_power:   '#a29bfe',
  meter_power_factor_l1:        '#00cec9',
  meter_power_factor_l2:        '#fdcb6e',
  meter_power_factor_l3:        '#fd79a8',
  meter_power_factor:           '#636e72',
  meter_frequency:              '#636e72',

  // === BMS (battery management system, greens) ===
  bms_status:               '#27ae60',
  bms_pack_temperature:     '#e17055',
  bms_charge_i_max:         '#00b894',
  bms_discharge_i_max:      '#d35400',
  bms_error_code_l:         '#e74c3c',
  bms_state_of_health:      '#00b894',
  bms_warning_code_l:       '#fd79a8',
  bms_max_cell_temperature_id: '#a29bfe',
  bms_min_cell_temperature_id: '#fdcb6e',
  bms_max_cell_voltage_id:     '#00b894',
  bms_min_cell_voltage_id:     '#636e72',
  bms_max_cell_temperature:    '#fdcb6e',
  bms_min_cell_temperature:    '#00b894',
  bms_max_cell_voltage:        '#0984e3',
  bms_min_cell_voltage:        '#636e72',

  // === Misc / single color for "other" ===
  all_data:             '#636e72',
  small_packet_interval:'#b2bec3',
  safety_country_text:  '#636e72'
};