@use 'sass:list'
@use 'sass:map'
@use 'sass:math'
@use 'sass:meta'

@function map-deep-set($map, $keys, $value)
  $maps: ($map,)
  $result: null

  // If the last key is a map already
  // Warn the user we will be overriding it with $value
  @if meta.type-of(list.nth($keys, -1)) == "map"
    @warn "The last key you specified is a map; it will be overrided with `#{$value}`."

  // If $keys is a single key
  // Just merge and return
  @if list.length($keys) == 1
    @return map.merge($map, ( $keys: $value ))

  // Loop from the first to the second to last key from $keys
  // Store the associated map to this key in the $maps list
  // If the key doesn't exist, throw an error
  @for $i from 1 through list.length($keys) - 1
    $current-key: list.nth($keys, $i)
    $current-map: list.nth($maps, -1)
    $current-get: map.get($current-map, $current-key)

    @if $current-get == null
      @error "Key `#{$current-key}` doesn't exist at current level in map."

    $maps: list.append($maps, $current-get)

  // Loop from the last map to the first one
  // Merge it with the previous one
  @for $i from list.length($maps) through 1
    $current-map: list.nth($maps, $i)
    $current-key: list.nth($keys, $i)
    $current-val: if($i == list.length($maps), $value, $result)
    $result: map.merge($current-map, ($current-key: $current-val))

  // Return result
  @return $result

@function map-deep-get($map, $keys...)
  @each $key in $keys
    $map: map.get($map, $key)

  @return $map

@function breakpoint-min($name, $breakpoints)
  $min: map.get($breakpoints, $name)
  @return if($min != 0, $min, null)

@function breakpoint-infix($name, $breakpoints)
  @return if(breakpoint-min($name, $breakpoints) == null, "", "-#{$name}")

// Adapted from https://gist.github.com/pentzzsolt/4949bbd7691d43d00616dc4f1451cae9#file-non-destructive-map-merge-4-scss
@function map-deep-merge($parent-map, $child-map)
  $result: $parent-map

  @each $key, $child in $child-map
    $parent-has-key: map.has-key($result, $key)
    $parent-value: map.get($result, $key)
    $parent-type: meta.type-of($parent-value)
    $child-type: meta.type-of($child)
    $parent-is-map: $parent-type == map
    $child-is-map: $child-type == map

    @if (not $parent-has-key) or ($parent-type != $child-type) or (not ($parent-is-map and $child-is-map))
      $result: map.merge($result, ( $key: $child ))

    @else
      $result: map.merge($result, ( $key: map-deep-merge($parent-value, $child) ))

  @return $result

@function theme-color($color, $opacity: 1)
  $color: rgba(var(--v-theme-#{$color}), $opacity)
  @return $color

@function roundEven($val)
  @return 2 * math.round($val * .5)
