import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { customAxios } from '@/util/axios';
import type { OAuthTokenResponse, OAuthUserResponse } from '@/util/types/api-responses';
import { AvailablePermissions } from '@/util/types/roles-and-permissions';

export enum LocalStorageTokens {
  ACCESS = 'amr-at',
  REFRESH = 'amr-rt',
}

export const TokenService = {
  getAccessToken: () => localStorage.getItem(LocalStorageTokens.ACCESS),
  getRefreshToken: () => localStorage.getItem(LocalStorageTokens.REFRESH),
  setAccessToken: (token: string) => localStorage.setItem(LocalStorageTokens.ACCESS, token),
  setRefreshToken: (token: string) => localStorage.setItem(LocalStorageTokens.REFRESH, token),
  removeTokens: () => {
    localStorage.removeItem(LocalStorageTokens.ACCESS);
    localStorage.removeItem(LocalStorageTokens.REFRESH);
  },
};

export type UserData = {
  id: number,
  name: string,
  surname: string,
  phone: string,
  email: string,
  permissions: string[],
  organization?: {
    id: number,
    name: string,
  },
  roles: {
    id: number,
    name: string,
  }[],
}

export const useAuthStore = defineStore('auth-store', () => {
  const accessToken = ref(TokenService.getAccessToken() ?? undefined);
  const refreshToken = ref(TokenService.getRefreshToken() ?? undefined);
  const user = ref<UserData>();
  const isUnverified = computed(() => Boolean(user.value?.roles.find(role => role.name === 'customer_unverified_mail')));

  const canServiceInstallation = computed(() => user.value?.permissions.includes(AvailablePermissions.SERVICE_INSTALLATION));

  const fetchUser = async(): Promise<UserData|undefined> => {
    try {
      const { data: userResponseData } = await customAxios.get<OAuthUserResponse>('/user');
      user.value = userResponseData.data;
      return userResponseData.data;
    } catch (err) {
      console.error(err);
      logout();
      return;
    }
  };

  const login = async(username: string, password: string) => {
    try {
      const { data: responseData } = await customAxios.post<OAuthTokenResponse>('/oauth/token', {
        grant_type: 'password',
        client_id: import.meta.env.VITE_APP_CLIENT_ID,
        client_secret: import.meta.env.VITE_APP_CLIENT_SECRET,
        username,
        password,
        scope: '*',
      });

      accessToken.value = responseData.access_token;
      TokenService.setAccessToken(responseData.access_token);
      refreshToken.value = responseData.refresh_token;
      TokenService.setRefreshToken(responseData.refresh_token);
      await fetchUser();
    } catch (err) {
      console.error(err);
      throw new Error('Failed to login');
    }
  };

  const promptRefreshToken = async() => {
    try {
      const { data: responseData } = await customAxios.post<OAuthTokenResponse>('/oauth/token', {
        grant_type: 'refresh_token',
        refresh_token: refreshToken.value,
        client_id: import.meta.env.VITE_APP_CLIENT_ID,
        client_secret: import.meta.env.VITE_APP_CLIENT_SECRET,
        scope: '*',
      });

      accessToken.value = responseData.access_token;
      TokenService.setAccessToken(responseData.access_token);
      refreshToken.value = responseData.refresh_token;
      TokenService.setRefreshToken(responseData.refresh_token);

      return {
        accessToken: responseData.access_token,
        refreshToken: responseData.refresh_token,
      };
    } catch (err) {
      console.error(err);
      throw new Error('Failed to get token');
    }
  };

  const logout = async() => {
    try {
      await customAxios.get('/user/logout');
    } catch {
      //
    }
    TokenService.removeTokens();
    accessToken.value = undefined;
    refreshToken.value = undefined;
    user.value = undefined;
  };

  return {
    accessToken,
    refreshToken,
    user,
    login,
    promptRefreshToken,
    logout,
    fetchUser,
    isUnverified,
    userHelpers: {
      canServiceInstallation,
    }
  };
});
