<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { PlusIcon, RefreshCw } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
import UsersSearchInput from '@/components/user-management/UsersSearchInput.vue';
import UsersRoleFilter from '@/components/user-management/UsersRoleFilter.vue';
import UsersPagination from '@/components/user-management/UsersPagination.vue';
import CreateOrEditUserDialog from '@/components/user-management/CreateOrEditUserDialog.vue';
import UsersTable from '@/components/user-management/UsersTable.vue';
import { ComponentStateType } from '@/util/types/components';
import { routeMap } from '@/router/routes';
import { customAxios } from '@/util/axios';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import { usePaginatedApi } from '@/composables/usePaginatedApi';
import type { ApiRolesResponse } from '@/util/types/api-responses';
import type { RoleData } from '@/util/types/roles-and-permissions';
import type { UserManagementData } from '@/util/types/user-management';

const route = useRoute();
initializeBasicBreadcrumbBehaviour(routeMap.management.children.users.meta.i18nTitle, routeMap.management.children.users.name, true, route);

// Role filtering state
const selectedRoleId = ref<number | null>(null);

// Custom filters for API requests
const customFilters = ref<Record<string, any>>({});

// Pagination and data management
const {
  data: users,
  componentState,
  currentPage,
  totalPages,
  totalItems,
  perPage,
  searchTerm,

  paginationLinks,
  handlePageChange,
  handlePerPageChange,
  handleSearch,
  refresh,
  initialize,
} = usePaginatedApi<UserManagementData[0]>({
  endpoint: '/users',
  initialPerPage: 15,
  searchParam: 'search_term',
  customFilters,
  errorMessages: {
    fetchError: 'Failed to fetch users',
    accessDenied: 'Access denied to user management',
  },
});

// Roles and filtering
const allRoles = ref<RoleData[]>([]);
const rolesLoading = ref(false);

// Modals
const modals = reactive({
  createUser: {
    isOpened: false,
  },
  editUser: {
    isOpened: false,
    userId: null as string | null,
  },
});

const fetchRoles = async() => {
  rolesLoading.value = true;
  try {
    const { data: rolesResponse } = await customAxios.get<ApiRolesResponse>('/roles');
    allRoles.value = rolesResponse.data;
  } catch (error) {
    console.error('Failed to fetch roles:', error);
  } finally {
    rolesLoading.value = false;
  }
};

// Event handlers
const handleRoleFilter = (roleId: number | null) => {
  selectedRoleId.value = roleId;

  // Update custom filters for API request
  if (roleId !== null) {
    customFilters.value = {
      'filter[roles][]': [roleId]
    };
  } else {
    customFilters.value = {};
  }
};

const handleRefresh = () => {
  refresh();
};

const handleCreateUser = () => {
  modals.createUser.isOpened = true;
};

const handleEditUser = (userId: string) => {
  modals.editUser.userId = userId;
  modals.editUser.isOpened = true;
};

const handleUserCreated = () => {
  refresh();
};

const handleUserUpdated = () => {
  refresh();
};

const handleUserDeleted = () => {
  refresh();
};

// Initialize data
onMounted(async() => {
  await Promise.all([
    initialize(),
    fetchRoles(),
  ]);
});

</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-col gap-4 md:gap-0 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          {{ $t('user-management.title') }}
        </h1>
        <p class="text-muted-foreground">
          {{ $t('user-management.description') }}
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="default"
          class="gap-2"
          @click="handleCreateUser"
        >
          <PlusIcon class="h-4 w-4" />
          {{ $t('user-management.create-user') }}
        </Button>
        <Button
          variant="outline"
          size="default"
          class="gap-2"
          @click="handleRefresh"
        >
          <RefreshCw class="h-4 w-4" />
          {{ $t('misc.refresh') }}
        </Button>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="flex flex-col sm:flex-row gap-4 sm:items-center sm:justify-between">
      <div class="w-full sm:max-w-sm">
        <UsersSearchInput
          v-model="searchTerm"
          :placeholder="$t('user-management.search-placeholder')"
          @search="handleSearch"
        />
      </div>
      <div class="flex-shrink-0">
        <UsersRoleFilter
          v-model="selectedRoleId"
          :roles="allRoles"
          :loading="rolesLoading"
          @filter="handleRoleFilter"
        />
      </div>
    </div>

    <!-- Users Table -->
    <section class="dark:bg-card bg-white rounded-xl">
      <UsersTable
        :users="users"
        :all-roles="allRoles"
        :loading="componentState === ComponentStateType.LOADING"
        @edit-user="handleEditUser"
        @delete-user="handleUserDeleted"
      />
    </section>

    <!-- Pagination -->
    <UsersPagination
      :current-page="currentPage"
      :total-pages="totalPages"
      :has-prev="!!paginationLinks.prev"
      :has-next="!!paginationLinks.next"
      :per-page="perPage"
      :total-items="totalItems"
      @page-change="handlePageChange"
      @per-page-change="handlePerPageChange"
    />

    <!-- Modals -->
    <CreateOrEditUserDialog
      v-model="modals.createUser.isOpened"
      :all-roles="allRoles"
      :is-edit-dialog="false"
      @user-created="handleUserCreated"
    />

    <CreateOrEditUserDialog
      v-model="modals.editUser.isOpened"
      :all-roles="allRoles"
      :user-id="modals.editUser.userId"
      :is-edit-dialog="true"
      @user-updated="handleUserUpdated"
    />
  </div>
</template>

