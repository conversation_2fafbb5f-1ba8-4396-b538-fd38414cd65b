<script setup lang="ts">

import { Label } from '@/shadcn-components/ui/label';
import { Input } from '@/shadcn-components/ui/input';
import type { BackendFormField, FormErrors } from '@/pages/device-detail/types/device-configuration-types';
import ButtonVariant1 from '../ButtonVariant1.vue';
import Tooltip from '@/shadcn-components/ui/tooltip/Tooltip.vue';
import TooltipTrigger from '@/shadcn-components/ui/tooltip/TooltipTrigger.vue';
import TooltipContent from '@/shadcn-components/ui/tooltip/TooltipContent.vue';
import { TooltipProvider } from '@/shadcn-components/ui/tooltip';

interface Props {
  fields: BackendFormField[];
  errors?: FormErrors;
  labelClass?: string;
  inputClass?: string;
  fieldClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  labelClass: '',
  inputClass: '',
  fieldClass: '',
});
const emit = defineEmits(['send-instruction']);

const modelValue = defineModel<Record<string, any>>({ required: true });

const getFieldError = (fieldKey: string): string[] => {
  return props.errors?.[fieldKey] || [];
};

const hasFieldError = (fieldKey: string): boolean => {
  return getFieldError(fieldKey).length > 0;
};

const getInputClasses = (fieldKey: string): string => {
  const baseClasses = hasFieldError(fieldKey)
    ? 'w-full rounded-md border border-red-500 bg-background px-3 py-2 text-sm outline-hidden focus-visible:border-red-600'
    : 'w-full rounded-md border border-input bg-background px-3 py-2 text-sm outline-hidden';
  return `${baseClasses} ${props.inputClass}`;
};
</script>

<template>
  <div
    v-for="field in fields"
    :key="field.key"
    :class="fieldClass"
  >
    <Label
      :for="field.key"
      :class="labelClass"
    >
      <div>
        <span v-if="field.type !== 'button'">{{ field.label }}</span>
        <span v-else>{{ $t('misc.instruction') }}</span>
        <span v-if="field.required" class="text-red-500">*</span>
      </div>
      <div v-if="field.help" class="text-[10px] font-extralight mt-0.5 text-gray-500">
        {{ field.help }}
      </div>
    </Label>

    <div class="w-full">
      <select
        v-if="field.type === 'select'"
        :id="field.key"
        v-model="modelValue[field.key]"
        :class="getInputClasses(field.key)"
      >
        <option disabled value="undefined">
          {{ $t('misc.select-option') }}
        </option>
        <option
          v-for="(opt, idx) in field.options"
          :key="idx"
          :value="opt.value"
        >
          {{ opt.label }}
        </option>
      </select>

      <TooltipProvider v-else-if="field.type === 'button'" :delay-duration="0">
        <Tooltip>
          <TooltipTrigger
            as-child
            as="button"
          >
            <ButtonVariant1
              class="h-9 rounded-md px-2 w-full"
              :active="false"
              type="button"
              @click="emit('send-instruction', field)"
            >
              {{ field.label }}
            </ButtonVariant1>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            {{ $t('device-detail.clicking-means-send-instruction') }}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Input
        v-else-if="field.type === 'text' || field.type === 'number'"
        :id="field.key"
        v-model="modelValue[field.key]"
        :type="field.type"
        :class="[hasFieldError(field.key) ? 'border-red-500 focus-visible:border-red-600' : '', inputClass]"
      />

      <input
        v-else-if="field.type === 'switch' || field.type === 'checkbox'"
        :id="field.key"
        v-model="modelValue[field.key]"
        type="checkbox"
        :class="[hasFieldError(field.key) ? 'border-red-500' : '', inputClass]"
      >

      <div v-else class="text-xs text-gray-400">
        Unknown field type: {{ field.type }}
      </div>

      <!-- Error messages -->
      <div
        v-if="hasFieldError(field.key)"
        class="mt-1 text-sm text-red-600"
      >
        <div
          v-for="error in getFieldError(field.key)"
          :key="error"
          class="text-xs"
        >
          {{ $t(error) }}
        </div>
      </div>
    </div>
  </div>
</template>
