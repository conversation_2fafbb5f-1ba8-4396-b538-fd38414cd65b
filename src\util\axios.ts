import createAuthRefreshInterceptor from '@antik-web/axios-auth-refresh-response-data';
import axios from 'axios';
import { Model } from 'vue-api-query';
import router from '@/router';
import { TokenService, useAuthStore } from '@/stores/auth-store';
import type { AxiosError } from 'axios';

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_API_URL as string,
  timeout: 10000, // Request timeout
});

axiosInstance.interceptors.request.use(
  config => {
    const token = TokenService.getAccessToken();

    if (token) {
      config.headers.Authorization = 'Bearer ' + token; // Set JWT token
    }

    return config;
  },
  error => {
    // Do something with request error
    console.error(error); // For debug
    void Promise.reject(error);
  },
);

const refreshAuthLogic = async(failedRequest: AxiosError): Promise<void> => {
  await handleTokenAuth(failedRequest);
};

const refreshTokens = async() => {
  const { promptRefreshToken } = useAuthStore();
  const { accessToken, refreshToken } = await promptRefreshToken();
  return { accessToken, refreshToken };
};

export const handleTokenAuth = async(res: AxiosError) => {
  try {
    if (router.currentRoute.value.name === 'login') {
      return;
    }

    const { accessToken } = await refreshTokens();
    res.response!.config.headers.Authorization = 'Bearer ' + accessToken;
    await Promise.resolve();
  } catch (err: any) {
    console.error(err);
  }
};

createAuthRefreshInterceptor(axiosInstance, refreshAuthLogic, { pauseInstanceWhileRefreshing: true });

axiosInstance.interceptors.response.use(
  res => res,
  async(error: AxiosError) => {
    if (error?.response?.status === 429 && (error.request as XMLHttpRequest).responseURL.search(/ping|token/) === -1) {
      // TODO
    }

    return Promise.reject(error);
  },
);

export const customAxios = axiosInstance;

Model.$http = axios;
