<script setup lang="ts">
import { MetricChartViewType, type MetricChartInputs, type MetricChartInputSetters } from '@/util/types/installation';
import { DateTime } from 'luxon';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-vue-next';

interface Props {
  metricChartInputData: MetricChartInputs,
  metricChartInputSetters: MetricChartInputSetters,
}

defineProps<Props>();
</script>

<template>
  <div class="px-4 py-2 flex items-center gap-2">
    <button
      v-if="[MetricChartViewType.PICK_DAY, MetricChartViewType.DAY].includes(metricChartInputData.viewType)"
      class="bg-prim-col-1 hover:bg-prim-col-1/80 rounded-md h-6 w-6 flex items-center justify-center cursor-pointer"
      @click="metricChartInputSetters.subtractDay"
    >
      <ChevronLeftIcon class="h-5 w-5" />
    </button>
    <h3 class="font-bold text-lg">
      <span v-if="metricChartInputData.viewType === MetricChartViewType.DAY">{{ $t('misc.last-24h') }}</span>
      <span v-else>{{ DateTime.fromISO(metricChartInputData.dateStart).toFormat('dd.MM.yyyy') }}</span>
      <span v-if="![MetricChartViewType.DAY, MetricChartViewType.PICK_DAY].includes(metricChartInputData.viewType)"> - {{ DateTime.fromISO(metricChartInputData.dateEnd).toFormat('dd.MM.yyyy') }}</span>
    </h3>
    <button
      v-if="[MetricChartViewType.PICK_DAY, MetricChartViewType.DAY].includes(metricChartInputData.viewType)"
      class="bg-prim-col-1 hover:bg-prim-col-1/80 rounded-md h-6 w-6 flex items-center justify-center cursor-pointer"
      @click="metricChartInputSetters.addDay"
    >
      <ChevronRightIcon class="h-5 w-5" />
    </button>
  </div>
</template>