@use '../tools/functions' as *;

$shadow-key-umbra-opacity: var(--v-shadow-key-umbra-opacity, rgba(0, 0, 0, 0.2)) !default;
$shadow-key-penumbra-opacity: var(--v-shadow-key-penumbra-opacity, rgba(0, 0, 0, 0.14)) !default;
$shadow-key-ambient-opacity: var(--v-shadow-key-penumbra-opacity, rgba(0, 0, 0, 0.12)) !default;

$shadow-key-umbra: () !default;
$shadow-key-umbra: map-deep-merge(
  (
    0: (0px 0px 0px 0px $shadow-key-umbra-opacity),
    1: (0px 2px 1px -1px $shadow-key-umbra-opacity),
    2: (0px 3px 1px -2px $shadow-key-umbra-opacity),
    3: (0px 3px 3px -2px $shadow-key-umbra-opacity),
    4: (0px 2px 4px -1px $shadow-key-umbra-opacity),
    5: (0px 3px 5px -1px $shadow-key-umbra-opacity),
    6: (0px 3px 5px -1px $shadow-key-umbra-opacity),
    7: (0px 4px 5px -2px $shadow-key-umbra-opacity),
    8: (0px 5px 5px -3px $shadow-key-umbra-opacity),
    9: (0px 5px 6px -3px $shadow-key-umbra-opacity),
    10: (0px 6px 6px -3px $shadow-key-umbra-opacity),
    11: (0px 6px 7px -4px $shadow-key-umbra-opacity),
    12: (0px 7px 8px -4px $shadow-key-umbra-opacity),
    13: (0px 7px 8px -4px $shadow-key-umbra-opacity),
    14: (0px 7px 9px -4px $shadow-key-umbra-opacity),
    15: (0px 8px 9px -5px $shadow-key-umbra-opacity),
    16: (0px 8px 10px -5px $shadow-key-umbra-opacity),
    17: (0px 8px 11px -5px $shadow-key-umbra-opacity),
    18: (0px 9px 11px -5px $shadow-key-umbra-opacity),
    19: (0px 9px 12px -6px $shadow-key-umbra-opacity),
    20: (0px 10px 13px -6px $shadow-key-umbra-opacity),
    21: (0px 10px 13px -6px $shadow-key-umbra-opacity),
    22: (0px 10px 14px -6px $shadow-key-umbra-opacity),
    23: (0px 11px 14px -7px $shadow-key-umbra-opacity),
    24: (0px 11px 15px -7px $shadow-key-umbra-opacity)
  ),
  $shadow-key-umbra
);

$shadow-key-penumbra: () !default;
$shadow-key-penumbra: map-deep-merge(
  (
    0: (0px 0px 0px 0px $shadow-key-penumbra-opacity),
    1: (0px 1px 1px 0px $shadow-key-penumbra-opacity),
    2: (0px 2px 2px 0px $shadow-key-penumbra-opacity),
    3: (0px 3px 4px 0px $shadow-key-penumbra-opacity),
    4: (0px 4px 5px 0px $shadow-key-penumbra-opacity),
    5: (0px 5px 8px 0px $shadow-key-penumbra-opacity),
    6: (0px 6px 10px 0px $shadow-key-penumbra-opacity),
    7: (0px 7px 10px 1px $shadow-key-penumbra-opacity),
    8: (0px 8px 10px 1px $shadow-key-penumbra-opacity),
    9: (0px 9px 12px 1px $shadow-key-penumbra-opacity),
    10: (0px 10px 14px 1px $shadow-key-penumbra-opacity),
    11: (0px 11px 15px 1px $shadow-key-penumbra-opacity),
    12: (0px 12px 17px 2px $shadow-key-penumbra-opacity),
    13: (0px 13px 19px 2px $shadow-key-penumbra-opacity),
    14: (0px 14px 21px 2px $shadow-key-penumbra-opacity),
    15: (0px 15px 22px 2px $shadow-key-penumbra-opacity),
    16: (0px 16px 24px 2px $shadow-key-penumbra-opacity),
    17: (0px 17px 26px 2px $shadow-key-penumbra-opacity),
    18: (0px 18px 28px 2px $shadow-key-penumbra-opacity),
    19: (0px 19px 29px 2px $shadow-key-penumbra-opacity),
    20: (0px 20px 31px 3px $shadow-key-penumbra-opacity),
    21: (0px 21px 33px 3px $shadow-key-penumbra-opacity),
    22: (0px 22px 35px 3px $shadow-key-penumbra-opacity),
    23: (0px 23px 36px 3px $shadow-key-penumbra-opacity),
    24: (0px 24px 38px 3px $shadow-key-penumbra-opacity)
  ),
  $shadow-key-penumbra
);

$shadow-key-ambient: () !default;
$shadow-key-ambient: map-deep-merge(
  (
    0: (0px 0px 0px 0px $shadow-key-ambient-opacity),
    1: (0px 1px 3px 0px $shadow-key-ambient-opacity),
    2: (0px 1px 5px 0px $shadow-key-ambient-opacity),
    3: (0px 1px 8px 0px $shadow-key-ambient-opacity),
    4: (0px 1px 10px 0px $shadow-key-ambient-opacity),
    5: (0px 1px 14px 0px $shadow-key-ambient-opacity),
    6: (0px 1px 18px 0px $shadow-key-ambient-opacity),
    7: (0px 2px 16px 1px $shadow-key-ambient-opacity),
    8: (0px 3px 14px 2px $shadow-key-ambient-opacity),
    9: (0px 3px 16px 2px $shadow-key-ambient-opacity),
    10: (0px 4px 18px 3px $shadow-key-ambient-opacity),
    11: (0px 4px 20px 3px $shadow-key-ambient-opacity),
    12: (0px 5px 22px 4px $shadow-key-ambient-opacity),
    13: (0px 5px 24px 4px $shadow-key-ambient-opacity),
    14: (0px 5px 26px 4px $shadow-key-ambient-opacity),
    15: (0px 6px 28px 5px $shadow-key-ambient-opacity),
    16: (0px 6px 30px 5px $shadow-key-ambient-opacity),
    17: (0px 6px 32px 5px $shadow-key-ambient-opacity),
    18: (0px 7px 34px 6px $shadow-key-ambient-opacity),
    19: (0px 7px 36px 6px $shadow-key-ambient-opacity),
    20: (0px 8px 38px 7px $shadow-key-ambient-opacity),
    21: (0px 8px 40px 7px $shadow-key-ambient-opacity),
    22: (0px 8px 42px 7px $shadow-key-ambient-opacity),
    23: (0px 9px 44px 8px $shadow-key-ambient-opacity),
    24: (0px 9px 46px 8px $shadow-key-ambient-opacity)
  ),
  $shadow-key-ambient
);
