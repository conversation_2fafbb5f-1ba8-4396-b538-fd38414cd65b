<script setup lang="ts">
import { defineExpose, ref } from 'vue';
import { isLightModeEnabled } from '@/composables/theme.ts';
// TODO: In Illustrator, create path on each chart, make it opposite direction using  Object -> Path -> Reverse Path Direction
// TODO VUE: Make refs on path (chart) and circle (text) elements
// to auto-assign css in illustrator, 1. Create a new style in the Graphic Styles panel
// 2. Double click the new style and give it a custom name, like "my-style"
// 3. Apply that style to one or more elements
// 4. Export the SVG

const photovoltaicsGroup = ref<SVGElement>();
const batteryGroup = ref<SVGElement>();
const gridGroup = ref<SVGElement>();
const heatingGroup = ref<SVGElement>();
const tuvGroup = ref<SVGElement>();
const householdGroup = ref<SVGElement>();
const wallBoxGroup = ref<SVGElement>();

const photovoltaicsCurrentPowerCenter = ref<SVGCircleElement>();
const photovoltaicsSystemCenter = ref<SVGCircleElement>();
const photovoltaicsMainCenter = ref<SVGCircleElement>();
const photovoltaicsTodayEnergyCenter = ref<SVGCircleElement>();
const photovoltaicsTodayEnergyPercentCenter = ref<SVGCircleElement>();
const photovoltaicsTodayEnergyAverageCenter = ref<SVGCircleElement>();
const photovoltaicsArrowsOut = ref<SVGElement>();
const photovoltaicsCurrentPowerChart = ref<SVGPathElement>();
const photovoltaicsTodaysPowerChart = ref<SVGPathElement>();

const batteryStateCenter = ref<SVGCircleElement>();
const batteryMainCenter = ref<SVGCircleElement>();
const batteryArrowsIn = ref<SVGElement>();
const batteryArrowsOut = ref<SVGElement>();
const batteryStateChart = ref<SVGPathElement>();

const gridCurrentPowerCenter = ref<SVGCircleElement>();
const gridCurrentPowerPercentCenter = ref<SVGCircleElement>();
const gridCurrentPowerTotalCenter = ref<SVGCircleElement>();
const gridMainCenter = ref<SVGCircleElement>();
const gridConsumptionCenter = ref<SVGCircleElement>();
const gridConsumptionPercentCenter = ref<SVGCircleElement>();
const gridConsumptionTotalCenter = ref<SVGCircleElement>();
const gridArrowsIn = ref<SVGElement>();
const gridArrowsOut = ref<SVGElement>();
const gridConsumptionChart = ref<SVGPathElement>();
const gridPowerChart = ref<SVGPathElement>();

const heatingTodayCenter = ref<SVGCircleElement>();
const heatingTodayPercentCenter = ref<SVGCircleElement>();
const heatingTodayTotalCenter = ref<SVGCircleElement>();
const heatingMainCenter = ref<SVGCircleElement>();
const heatingArrowsIn = ref<SVGElement>();
const heatingTodayConsumptionChart = ref<SVGPathElement>();

const tuvStateCurrentTemperatureCenter = ref<SVGCircleElement>();
const tuvMainCenter = ref<SVGCircleElement>();
const tuvArrowsIn = ref<SVGElement>();
const tuvStateChart = ref<SVGPathElement>();

const householdTodayCenter = ref<SVGCircleElement>();
const householdTodayPercentCenter = ref<SVGCircleElement>();
const householdTodayTotalCenter = ref<SVGCircleElement>();
const householdMainCenter = ref<SVGCircleElement>();
const householdArrowsIn = ref<SVGElement>();
const householdConsumptionChart = ref<SVGPathElement>();

const wallBoxTodayCenter = ref<SVGCircleElement>();
const wallBoxTodayPercentCenter = ref<SVGCircleElement>();
const wallBoxTodayTotalCenter = ref<SVGCircleElement>();
const wallBoxMainCenter = ref<SVGCircleElement>();
const wallboxArrowsIn = ref<SVGElement>();
const wallboxConsumptionChart = ref<SVGPathElement>();

defineExpose({
  wholeGroups: {
    photovoltaicsGroup,
    batteryGroup,
    gridGroup,
    heatingGroup,
    tuvGroup,
    householdGroup,
    wallBoxGroup,
  },
  dynamicTextCenterPoints: {
    photovoltaics: {
      currentPower: photovoltaicsCurrentPowerCenter,
      system: photovoltaicsSystemCenter,
      main: photovoltaicsMainCenter,
      todayEnergy: photovoltaicsTodayEnergyCenter,
      todayEnergyPercent: photovoltaicsTodayEnergyPercentCenter,
      todayEnergyAverage: photovoltaicsTodayEnergyAverageCenter,
    },
    battery: {
      batteryState: batteryStateCenter,
      batteryMain: batteryMainCenter,
    },
    grid: {
      currentPower: gridCurrentPowerCenter,
      currentPowerPercent: gridCurrentPowerPercentCenter,
      currentPowerTotal: gridCurrentPowerTotalCenter,
      main: gridMainCenter,
      consumption: gridConsumptionCenter,
      consumptionPercent: gridConsumptionPercentCenter,
      consumptionTotal: gridConsumptionTotalCenter,
    },
    heating: {
      today: heatingTodayCenter,
      todayPercent: heatingTodayPercentCenter,
      todayTotal: heatingTodayTotalCenter,
      main: heatingMainCenter,
    },
    tuv: {
      tuvStateCurrentTemperatureCenter: tuvStateCurrentTemperatureCenter,
      main: tuvMainCenter,
    },
    household: {
      today: householdTodayCenter,
      todayPercent: householdTodayPercentCenter,
      todayTotal: householdTodayTotalCenter,
      main: householdMainCenter,
    },
    wallBox: {
      today: wallBoxTodayCenter,
      todayPercent: wallBoxTodayPercentCenter,
      todayTotal: wallBoxTodayTotalCenter,
      main: wallBoxMainCenter,
    },
  },
  arrows: {
    photovoltaics: {
      out: photovoltaicsArrowsOut,
    },
    battery: {
      in: batteryArrowsIn,
      out: batteryArrowsOut,
    },
    grid: {
      in: gridArrowsIn,
      out: gridArrowsOut,
    },
    heating: {
      in: heatingArrowsIn,
    },
    tuv: {
      in: tuvArrowsIn,
    },
    household: {
      in: householdArrowsIn,
    },
    wallBox: {
      in: wallboxArrowsIn,
    },
  },
  charts: {
    photovoltaics: {
      photovoltaicsCurrentPowerChart,
      photovoltaicsTodaysPowerChart,
    },
    battery: {
      batteryStateChart,
    },
    grid: {
      gridPowerChart,
      gridConsumptionChart,
    },
    heating: {
      heatingTodayConsumptionChart,
    },
    tuv: {
      tuvStateChart,
    },
    household: {
      householdConsumptionChart,
    },
    wallBox: {
      wallboxConsumptionChart,
    },
  }
});
</script>

<template>
  <svg
    id="Layer_1"
    :class="isLightModeEnabled ? '' : 'dark'"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 274.980896 82.7912445"
    style="enable-background:new 0 0 274.980896 82.7912445;"
    xml:space="preserve"
  >
    <line
      class="st0"
      x1="42.6015854"
      y1="40.831852"
      x2="230.8357544"
      y2="40.831852"
    />
    <g id="photovoltaics-group" ref="photovoltaicsGroup">
      <g>
        <g>
          <path
            class="st1"
            d="M42.1786499,9.5896854H37.431221l-0.8211174-0.8208342h4.7477112L42.1786499,9.5896854z
				 M37.4914551,9.4442139h4.3360176l-0.529892-0.529891h-4.3362999L37.4914551,9.4442139z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M43.1699562,10.5809946H38.422245l-0.8208351-0.8211184h4.747715L43.1699562,10.5809946z
				 M38.4824791,10.435523h4.3362999l-0.529892-0.5301752h-4.3362999L38.4824791,10.435523z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M44.1456375,11.5563917h-4.7477112l-0.8211174-0.8208342h4.7477112L44.1456375,11.5563917z
				 M39.4581604,11.4109201h4.3362999l-0.5301743-0.529891h-4.3362999L39.4581604,11.4109201z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M42.937542,9.3564196c-0.0170441,0-0.0343781-0.0059662-0.0480156-0.0181837
				c-0.030117-0.0264235-0.0332413-0.0724516-0.0065346-0.1025686c0.284977-0.3233328,0.4418144-0.739006,0.4418144-1.1697388
				c0-0.9753976-0.79356-1.7689557-1.768959-1.7689557c-0.890728,0-1.645359,0.6651344-1.7553177,1.5473394
				c-0.0048294,0.0397773-0.0392075,0.0690422-0.0812569,0.0630755c-0.0397797-0.0048304-0.0681915-0.0411983-0.063076-0.0812597
				c0.1190491-0.9546566,0.9359055-1.6746268,1.8996506-1.6746268c1.0555229,0,1.9144287,0.8589067,1.9144287,1.9144273
				c0,0.4662476-0.1699066,0.9157314-0.4781799,1.2657728C42.9776039,9.3481808,42.9577179,9.3564196,42.937542,9.3564196z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M39.285984,8.1386642h-0.79356c-0.040062,0-0.0727348-0.0326738-0.0727348-0.0727358
				c0-0.040061,0.0326729-0.0727353,0.0727348-0.0727353h0.79356c0.0400581,0,0.0727348,0.0326743,0.0727348,0.0727353
				C39.3587189,8.1059904,39.3260422,8.1386642,39.285984,8.1386642z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M44.5229568,8.6713972c-0.0051155,0-0.0105133-0.0005684-0.0159111-0.0017052l-0.774807-0.173315
				c-0.0392075-0.0088081-0.0639267-0.0477333-0.0551186-0.0869427c0.0088043-0.0392084,0.0448914-0.0639277,0.0869408-0.0551195
				l0.774807,0.173315c0.0392075,0.0088081,0.0639267,0.0477333,0.0551186,0.0869427
				C44.5863152,8.6483831,44.5561981,8.6713972,44.5229568,8.6713972z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M43.638195,7.213841c-0.0306854,0-0.0591011-0.0196047-0.0690422-0.0502901
				c-0.0125046-0.0380726,0.0082397-0.0792704,0.0465965-0.0914879l0.754631-0.2460513
				c0.0380745-0.0130696,0.0789871,0.0085239,0.0914879,0.0465965s-0.0082397,0.0792704-0.0465965,0.0914879l-0.754631,0.2460513
				C43.6532516,7.2127042,43.6455803,7.213841,43.638195,7.213841z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M42.888958,6.2969728c-0.015625,0-0.0312538-0.0048299-0.0446053-0.0150585
				c-0.0315399-0.0247188-0.0375061-0.0704627-0.013073-0.1022844l0.485569-0.6279144
				c0.0247192-0.0321059,0.0704651-0.0377884,0.1022835-0.0130696c0.0315399,0.0247188,0.0375061,0.0704627,0.013073,0.1022849
				l-0.485569,0.627914C42.9321442,6.2873125,42.910553,6.2969728,42.888958,6.2969728z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M41.6879654,5.8682299c-0.0014191,0-0.0028419,0-0.0045433,0
				c-0.040062-0.0025573-0.0704651-0.0369363-0.0681915-0.077282l0.0483017-0.7924218
				c0.0025558-0.0383568,0.0343781-0.0681896,0.0727348-0.0681896c0.0014229,0,0.0028419,0,0.0045471,0
				c0.040062,0.0025573,0.0704613,0.0369363,0.0681877,0.0772815L41.760704,5.8000398
				C41.7581444,5.8383965,41.7263222,5.8682299,41.6879654,5.8682299z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M40.4207726,6.1730947c-0.0250015,0-0.0494385-0.0130696-0.063076-0.0363679l-0.396637-0.6875801
				c-0.019886-0.0349474-0.0079536-0.0792704,0.0267105-0.0994434c0.0335236-0.0196042,0.078701-0.0085235,0.0994415,0.0267076
				l0.396637,0.6875801c0.0198898,0.0349474,0.0079575,0.0792708-0.0267067,0.0994434
				C40.4457779,6.1699696,40.4332733,6.1730947,40.4207726,6.1730947z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M39.5899963,7.0038733c-0.0125046,0-0.0250053-0.0031257-0.0363693-0.0096602l-0.687294-0.3969212
				c-0.0346642-0.0201731-0.0465965-0.0647802-0.0267105-0.0994434c0.0201721-0.0349474,0.0650673-0.0468807,0.0994453-0.0267076
				l0.687294,0.3969212c0.0346642,0.0201726,0.0465965,0.0647802,0.0267105,0.0994434
				C39.639431,6.9908037,39.6149979,7.0038733,39.5899963,7.0038733z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M43.7936096,10.1477051c-0.0187531,0-0.03722-0.007103-0.0514259-0.0213089l-0.544384-0.5443821
				c-0.0284119-0.0284119-0.0284119-0.07444,0-0.1028528c0.0284157-0.0284119,0.0744438-0.0284119,0.1028557,0l0.5443802,0.5443821
				c0.0284119,0.0284119,0.0284119,0.07444,0,0.1028528C43.8308296,10.1406021,43.8123627,10.1477051,43.7936096,10.1477051z"
          />
        </g>
        <g>
          <path
            class="st1"
            d="M39.7147255,7.9372206l-0.3798752-0.5440974c-0.0230141-0.0329585-0.0147743-0.0784183,0.0179024-0.1014323
				c0.0326729-0.0224457,0.0778503-0.0150585,0.1014328,0.0179l0.2963409,0.4247651l0.4247627-0.2966251
				c0.0332451-0.0230141,0.0781364-0.0150585,0.1014328,0.0178995c0.0230141,0.0329585,0.0150604,0.0784183-0.0178986,0.1014323
				L39.7147255,7.9372206z"
          />
        </g>
      </g>
      <text transform="matrix(1 0 0 1 30.9320297 16.5173416)" class="st2 st3 st4">Fotovoltika</text>
      <path
        class="actextrectangle"
        d="M49.1539879,27.1833248H32.3310127c-1.0999985,0-2-0.8999996-2-2v-3.4209538
		c0-1.1000004,0.9000015-2,2-2h16.8229752c1.0999985,0,2,0.8999996,2,2v3.4209538
		C51.1539879,26.2833252,50.2539864,27.1833248,49.1539879,27.1833248z"
      />
      <g>
        <text transform="matrix(1 0 0 1 17.9389877 35.6073532)" class="st5 st6 st7">systém</text>
        <text transform="matrix(1 0 0 1 10.0098963 33.5731735)"><tspan x="0" y="0" class="st6 st7">AKT</tspan><tspan x="3.1416016" y="0" class="st6 st7">U</tspan><tspan x="4.2392578" y="0" class="st6 st7">ÁL</tspan><tspan x="6.1933594" y="0" class="st6 st7">N</tspan><tspan x="7.375" y="0" class="st6 st7">Y VÝ</tspan><tspan x="10.8652344" y="0" class="st6 st7">K</tspan><tspan x="11.9130859" y="0" class="st6 st7">ON</tspan></text>
      </g>
      <text transform="matrix(1 0 0 1 63.4941902 35.3556099)" class="st5 st6 st8">priemerne</text>
      <text transform="matrix(1 0 0 1 57.8574905 33.6058884)" class="st6 st9">DENNÁ ENERGIA</text>
      <g class="st5" />
      <linearGradient
        id="SVGID_1_"
        gradientUnits="userSpaceOnUse"
        x1="7.7409587"
        y1="23.4344692"
        x2="26.3149567"
        y2="23.4344692"
      >
        <stop offset="0" style="stop-color:#31B04A" />
        <stop offset="1" style="stop-color:#009548" />
      </linearGradient>
      <path
        class="st10"
        d="M9.2356777,29.8604164c-0.6354036-1.2155571-0.9947195-2.5982647-0.9947195-4.0648937
		c0-4.8529263,3.9340734-8.7869987,8.7869997-8.7869987s8.7869987,3.9340725,8.7869987,8.7869987
		c0,1.4649868-0.358511,2.8462391-0.9925842,4.0608082"
      />
      <linearGradient
        id="SVGID_2_"
        gradientUnits="userSpaceOnUse"
        x1="54.9804649"
        y1="23.4344692"
        x2="73.5544662"
        y2="23.4344692"
      >
        <stop offset="0" style="stop-color:#27AAE1" />
        <stop offset="0.0751259" style="stop-color:#2995D0" />
        <stop offset="0.1881396" style="stop-color:#2B7BBC" />
        <stop offset="0.3060003" style="stop-color:#2A67AD" />
        <stop offset="0.4272921" style="stop-color:#2957A2" />
        <stop offset="0.553003" style="stop-color:#294A9A" />
        <stop offset="0.6850783" style="stop-color:#294094" />
        <stop offset="0.8277422" style="stop-color:#2A3A91" />
        <stop offset="1" style="stop-color:#2B3990" />
      </linearGradient>
      <path
        style="fill:none;stroke:url(#SVGID_00000065047545491194239340000014848017770607336365_);stroke-linecap:round;stroke-miterlimit:10;"
        d="
		M56.4751854,29.8604164c-0.6354065-1.2155571-0.9947205-2.5982647-0.9947205-4.0648937
		c0-4.8529263,3.9340744-8.7869987,8.7869987-8.7869987s8.7870026,3.9340725,8.7870026,8.7869987
		c0,1.4649868-0.3585129,2.8462391-0.9925842,4.0608082"
      />
      <path class="st0" d="M42.6015854,40.8260994c-1.6304665,0-2.9644852-1.3500023-2.9644852-3v-10.675705" />
      <g id="static-text">
        <text transform="matrix(1 0 0 1 59.5658951 28.329155)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 64.2672195 28.1379929)" class="st14 st6 st13">|</text>
      </g>
      <g id="dynamic-charts">
        <path
          id="current-power-chart"
          ref="photovoltaicsCurrentPowerChart"
          class="acchartcustom"
          d="M24.8223724,29.8563309
			c0.6340733-1.2145691,0.9925842-2.5958214,0.9925842-4.0608082c0-4.8529263-3.9340725-8.7869987-8.7869987-8.7869987
			s-8.7869997,3.9340725-8.7869997,8.7869987c0,1.466629,0.3593159,2.8493366,0.9947195,4.0648937"
        />
        <path
          id="daily-energy-chart"
          ref="photovoltaicsTodaysPowerChart"
          class="acchartcustom"
          d="M56.483181,29.8604164
			c-0.6354027-1.2155571-0.9947205-2.5982647-0.9947205-4.0648937c0-4.8529263,3.9340744-8.7869987,8.7869987-8.7869987
			s8.7870026,3.9340725,8.7870026,8.7869987c0,1.4649868-0.3585129,2.8462391-0.9925842,4.0608082"
        />
      </g>
      <g id="dynamic-text">
        <circle
          id="photovoltaics-center"
          ref="photovoltaicsMainCenter"
          class="st15"
          cx="40.6622543"
          cy="23.472847"
          r="0.0802455"
        />
        <circle
          id="daily-energy-percent"
          ref="photovoltaicsTodayEnergyPercentCenter"
          class="st15"
          cx="67.1781082"
          cy="27.4488297"
          r="0.0802455"
        />
        <circle
          id="daily-energy-big"
          ref="photovoltaicsTodayEnergyCenter"
          class="st15"
          cx="64.2754593"
          cy="23.6890163"
          r="0.0802455"
        />
        <circle
          id="daily-energy-percent_1_"
          ref="photovoltaicsTodayEnergyAverageCenter"
          class="st15"
          cx="60.4327087"
          cy="34.9985771"
          r="0.0802455"
        />
        <circle
          id="current-power-big"
          ref="photovoltaicsCurrentPowerCenter"
          class="st15"
          cx="16.9477367"
          cy="24.7808075"
          r="0.0802455"
        />
        <circle
          id="current-power-system"
          ref="photovoltaicsSystemCenter"
          class="st15"
          cx="13.2548008"
          cy="35.1521912"
          r="0.0802455"
        />
      </g>
      <g id="arrows-out" ref="photovoltaicsArrowsOut">
        <polygon class="st29" points="39.6244621,30.8228226 41.0410194,27.7660313 38.2079048,27.7660313 		" />
        <polygon class="st29" points="39.6244621,34.411377 41.0410156,31.3545856 38.2079048,31.3545856 		" />
        <polygon class="st29" points="39.6371002,37.9999352 41.0536575,34.9431419 38.2205429,34.9431419 		" />
      </g>

    </g>
    <g id="battery-group" ref="batteryGroup">
      <g class="st5" />
      <linearGradient
        id="SVGID_3_"
        gradientUnits="userSpaceOnUse"
        x1="104.0064468"
        y1="23.2880211"
        x2="122.5804443"
        y2="23.2880211"
      >
        <stop offset="0" style="stop-color:#27AAE1" />
        <stop offset="0.0751259" style="stop-color:#2995D0" />
        <stop offset="0.1881396" style="stop-color:#2B7BBC" />
        <stop offset="0.3060003" style="stop-color:#2A67AD" />
        <stop offset="0.4272921" style="stop-color:#2957A2" />
        <stop offset="0.553003" style="stop-color:#294A9A" />
        <stop offset="0.6850783" style="stop-color:#294094" />
        <stop offset="0.8277422" style="stop-color:#2A3A91" />
        <stop offset="1" style="stop-color:#2B3990" />
      </linearGradient>
      <path
        class="st16"
        d="M105.5011673,29.7139683c-0.6354065-1.2155571-0.9947205-2.5982628-0.9947205-4.0648937
		c0-4.8529243,3.9340744-8.7869987,8.7869949-8.7869987c4.8529282,0,8.7870026,3.9340744,8.7870026,8.7869987
		c0,1.4649868-0.3585129,2.8462391-0.9925842,4.0608082"
      />
      <path
        class="actextrectangle"
        d="M145.7070313,26.8613453h-16.8229675c-1.0999985,0-2-0.8999996-2-2v-3.4209538
		c0-1.1000004,0.9000015-2,2-2h16.8229675c1.1000061,0,2,0.8999996,2,2v3.4209538
		C147.7070313,25.9613457,146.8070374,26.8613453,145.7070313,26.8613453z"
      />
      <g>
        <text transform="matrix(1 0 0 1 131.2125092 16.5656967)" class="st2 st3 st4">Batéria</text>
        <text transform="matrix(1 0 0 1 108.062294 33.4622726)" class="st6 st9">STAV NABITIA</text>

        <linearGradient
          id="SVGID_4_"
          gradientUnits="userSpaceOnUse"
          x1="104.0104446"
          y1="23.3233337"
          x2="122.5844421"
          y2="23.3233337"
        >
          <stop offset="0" style="stop-color:#D7DF23" />
          <stop offset="1" style="stop-color:#8DC63F" />
        </linearGradient>
        <path
          class="st17"
          d="M105.5051651,29.749279c-0.6354065-1.2155552-0.9947205-2.5982628-0.9947205-4.0648918
			c0-4.8529263,3.9340744-8.7870007,8.7869949-8.7870007c4.8529282,0,8.7870026,3.9340744,8.7870026,8.7870007
			c0,1.4649868-0.3585129,2.8462372-0.9925842,4.0608063"
        />
        <g class="st5" />
        <g>
          <path
            class="st1"
            d="M138.0875092,5.9100738h-0.4624023c0.0084229-0.021595,0.0140686-0.0446754,0.0140686-0.0691757
				c0-0.2254233-0.1844482-0.4098368-0.4098358-0.4098368h-0.1666107c-0.2254028,0-0.4098511,0.1844463-0.4098511,0.4098368
				c0,0.0245004,0.005661,0.0475807,0.0140686,0.0691757h-0.4624023c-0.3707733,0-0.6723633,0.3016319-0.6723633,0.6724067
				v4.3790655c0,0.3707743,0.30159,0.6724072,0.6723633,0.6724072h1.8829651c0.3707733,0,0.6724091-0.3016329,0.6724091-0.6724072
				V6.5824804C138.7599182,6.2117057,138.4582825,5.9100738,138.0875092,5.9100738z M138.5877533,10.9615459
				c0,0.2758436-0.2243958,0.5002432-0.5002441,0.5002432h-1.8829651c-0.2758331,0-0.5002441-0.2243996-0.5002441-0.5002432
				V6.5824804c0-0.2758441,0.224411-0.5002437,0.5002441-0.5002437h1.8829651c0.2758484,0,0.5002441,0.2243996,0.5002441,0.5002437
				V10.9615459z"
          />
          <polygon
            class="st1"
            points="137.2847443,7.181221 136.5100708,8.9457121 137.227356,8.7592182 136.8974304,10.1076975
				137.7581482,8.2571249 137.026535,8.4579821 			"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="136.9586487"
        y1="26.9086399"
        x2="136.9586487"
        y2="40.6738014"
      />
      <g id="static-text_1_" />
      <g id="dynamic-text_1_">
        <circle
          id="battery-center"
          ref="batteryMainCenter"
          class="st15"
          cx="137.2153015"
          cy="23.1508675"
          r="0.0802455"
        />
        <circle
          id="daily-energy-big_1_"
          ref="batteryStateCenter"
          class="st15"
          cx="113.3816833"
          cy="24.8559952"
          r="0.0802455"
        />
      </g>
      <g id="dynamic-charts_1_">
        <path
          id="daily-energy-chart_1_"
          ref="batteryStateChart"
          class="acchartcustom"
          d="M121.0958557,29.7098827
			c0.6340714-1.2145691,0.9925842-2.5958214,0.9925842-4.0608082c0-4.8529243-3.9340744-8.7869987-8.7870026-8.7869987
			c-4.8529205,0-8.7869949,3.9340744-8.7869949,8.7869987c0,1.4666309,0.359314,2.8493366,0.9947205,4.0648937"
        />
      </g>
      <g id="arrows-out_1_" ref="batteryArrowsOut">
        <polygon class="st29" points="136.9523315,30.6170826 138.3688965,27.5602913 135.5357819,27.5602913 		" />
        <polygon class="st29" points="136.9523315,34.2056389 138.3688965,31.1488457 135.5357819,31.1488457 		" />
        <polygon class="st29" points="136.9649658,37.7941933 138.3815308,34.7374001 135.5484161,34.7374001 		" />
      </g>
      <g id="arrows-in_1_" ref="batteryArrowsIn">
        <polygon class="st29" points="136.9586487,29.2785034 135.542099,32.3352966 138.3752136,32.3352966 		" />
        <polygon class="st29" points="136.9712982,32.8670578 135.5547333,35.923851 138.3878479,35.923851 		" />
        <polygon class="st29" points="136.9712982,36.455616 135.5547333,39.5124054 138.3878479,39.5124054 		" />
      </g>
    </g>
    <g id="grid-group" ref="gridGroup">
      <g>
        <g>
          <g>
            <rect
              x="235.9629822"
              y="7.7434645"
              class="st1"
              width="0.0660872"
              height="0.7844477"
            />
            <path
              class="st1"
              d="M236.9848328,10.1267166V9.5436363l-2.1702423-0.6112928l-0.0082703-0.0614281
					c-0.0455627-0.3400412-0.0899658-0.6805429-0.1318359-1.0224247l-0.0128784-0.1058316h1.3649902v-0.490046l-1.4710541-0.410903
					l-0.0069122-0.0632687c-0.0515289-0.4700303-0.0996094-0.9419012-0.1440125-1.4151521l-0.00737-0.0807543h-0.7953491
					l-0.0075836,0.0807543c-0.0444031,0.4746313-0.0927124,0.948113-0.1444855,1.4195232l-0.006897,0.063499l-1.4705963,0.4065313
					v0.4898162v0.7844481h0.0660858V7.7426591h1.2991333l-0.0128784,0.1058316
					c-0.0421143,0.3418818-0.0865173,0.6823835-0.1320648,1.0224247l-0.0082855,0.0614281l-2.1699982,0.6112928v0.5986385h1.9983673
					l-0.0163269,0.1083622c-0.062149,0.4126568-0.1290131,0.8219204-0.1966248,1.2304649h0.1794739
					c0.0551147-0.3336,0.1114502-0.6664801,0.1627808-1.0022364l0.0294495-0.1918774l0.7898254,0.8443518l-0.3239899,0.349762
					h0.0761719l0.2860107-0.3088102l0.286026,0.3088102h0.0761566l-0.3239899-0.349762l0.7898254-0.8443518l0.0294495,0.1918774
					c0.0514679,0.3357563,0.1077271,0.6686363,0.1627655,1.0022364h0.179718
					c-0.0675812-0.4084587-0.1344452-0.8176641-0.1966248-1.2304649l-0.0163422-0.1083622h1.9333038v0.7688894h0.0661163v-0.7844477
					H236.9848328z M235.9709167,7.2951756v0.3915768h-0.2631989l-0.4221649-0.5827637L235.9709167,7.2951756z
					 M235.6382446,7.6867523h-0.9382172l0.513504-0.5864449L235.6382446,7.6867523z M235.1652222,7.0701685l-0.5132904,0.586215
					l-0.0881042-0.7539353L235.1652222,7.0701685z M234.5380554,8.2078581
					c0.0529175,0.4194145,0.1095123,0.8369894,0.1681824,1.2531834l0.0609589,0.43437l-0.7348328-1.1641474l0.4732513-0.8001499
					L234.5380554,8.2078581z M233.635498,6.7144823c0.0423279-0.388586,0.0832825-0.7776318,0.1205597-1.1682887
					l0.0082703-0.0853553h0.4702606l0.0082855,0.0853553c0.0375061,0.3913469,0.0784607,0.7813134,0.1210175,1.1708193
					l0.0115051,0.1049113l-0.7511749-0.0036807L233.635498,6.7144823z M233.5597992,7.3807611
					c0.0170288-0.1408019,0.0340576-0.2816043,0.0499268-0.4228663l0.009201-0.0839748h0.7610626l0.0092163,0.0839748
					c0.0147247,0.1320596,0.0308228,0.2634287,0.0466919,0.3950281l0.0400391,0.3338294h-0.9529419L233.5597992,7.3807611z
					 M233.5153961,7.7426591h0.9681244l0.0112762,0.0962548l-0.4953308,0.8380537l-0.4952545-0.845531
					C233.5111084,7.7768531,233.5153961,7.7426591,233.5153961,7.7426591z M232.0282288,7.6867523V7.2951756l0.685379-0.1911869
					l-0.4221802,0.5827637H232.0282288z M232.3606873,7.6867523l0.424469-0.5864449l0.5142059,0.5864449H232.3606873z
					 M232.8337097,7.0701685l0.6013947-0.1677203l-0.0881195,0.7539353L232.8337097,7.0701685z M233.2926941,9.4610415
					c0.0454407-0.3211184,0.1530457-1.1675119,0.1999817-1.5384402l0.4738922,0.8088927l-0.7353058,1.1641474L233.2926941,9.4610415
					z M234.0010681,8.7818794l0.0814514,0.1338997l0.7389832,1.1705894h-1.6440735L234.0010681,8.7818794z M231.0702362,10.0863686
					V9.5861988l0.9708862-0.2737818l-0.5933533,0.7739515H231.0702362z M231.5184021,10.0863686l0.5995636-0.7820034
					l0.8385925,0.7820034H231.5184021z M233.0239716,10.0725641l-0.8537903-0.7962675l1.0067902-0.2836752l-0.0195618,0.142643
					c-0.0349731,0.2537661-0.0706329,0.5068417-0.1074371,0.7592268L233.0239716,10.0725641z M234.0022278,11.0871668
					l-0.0027618-0.0027609l-0.0027618,0.0027609l-0.8836975-0.9448919h1.7731476L234.0022278,11.0871668z M234.9751892,10.0723343
					l-0.026001-0.1776133c-0.0370483-0.252615-0.0727081-0.5056906-0.1074371-0.7594566l-0.0195618-0.142643l1.0067749,0.2836752
					L234.9751892,10.0723343z M235.0423584,10.0863686l0.8383789-0.7820034l0.6002502,0.7820034H235.0423584z M235.9578094,9.312417
					l0.9711151,0.2737818v0.5001698h-0.377533L235.9578094,9.312417z"
            />
            <rect
              x="231.0138092"
              y="10.1441154"
              class="st1"
              width="0.0660872"
              height="0.7844477"
            />
          </g>
          <g>
            <path
              class="st18"
              d="M232.2124634,8.1181831h-0.4243622c-0.0506287,0-0.0916901-0.0410633-0.0916901-0.0916872
					c0-0.0506115,0.0410614-0.0916872,0.0916901-0.0916872h0.4243622c0.0505981,0,0.0916748,0.0410757,0.0916748,0.0916872
					C232.3041382,8.0771198,232.2630615,8.1181831,232.2124634,8.1181831z"
            />
            <path
              class="st18"
              d="M236.2109528,8.1181831h-0.4244232c-0.0506287,0-0.0916901-0.0410633-0.0916901-0.0916872
					c0-0.0506115,0.0410614-0.0916872,0.0916901-0.0916872h0.4244232c0.0506134,0,0.0916748,0.0410757,0.0916748,0.0916872
					C236.3026276,8.0771198,236.2615662,8.1181831,236.2109528,8.1181831z"
            />
            <path
              class="st18"
              d="M232.2124634,8.3903236h-0.4243622c-0.0506287,0-0.0916901-0.0410757-0.0916901-0.0916872
					c0-0.0506239,0.0410614-0.0916872,0.0916901-0.0916872h0.4243622c0.0505981,0,0.0916748,0.0410633,0.0916748,0.0916872
					C232.3041382,8.3492479,232.2630615,8.3903236,232.2124634,8.3903236z"
            />
            <path
              class="st18"
              d="M236.2109528,8.3903236h-0.4244232c-0.0506287,0-0.0916901-0.0410757-0.0916901-0.0916872
					c0-0.0506239,0.0410614-0.0916872,0.0916901-0.0916872h0.4244232c0.0506134,0,0.0916748,0.0410633,0.0916748,0.0916872
					C236.3026276,8.3492479,236.2615662,8.3903236,236.2109528,8.3903236z"
            />
            <path
              class="st18"
              d="M231.2543945,10.5035486h-0.4243469c-0.0506287,0-0.0916901-0.0410643-0.0916901-0.0916882
					c0-0.0506115,0.0410614-0.0916872,0.0916901-0.0916872h0.4243469c0.0506134,0,0.0916901,0.0410757,0.0916901,0.0916872
					C231.3460846,10.4624844,231.3050079,10.5035486,231.2543945,10.5035486z"
            />
            <path
              class="st18"
              d="M237.1689453,10.5035486h-0.4243622c-0.0505981,0-0.0916748-0.0410643-0.0916748-0.0916882
					c0-0.0506115,0.0410767-0.0916872,0.0916748-0.0916872h0.4243622c0.0506287,0,0.0916901,0.0410757,0.0916901,0.0916872
					C237.2606354,10.4624844,237.219574,10.5035486,237.1689453,10.5035486z"
            />
            <path
              class="st18"
              d="M231.2543945,10.7756891h-0.4243469c-0.0506287,0-0.0916901-0.0410643-0.0916901-0.0916882
					c0-0.0506229,0.0410614-0.0916872,0.0916901-0.0916872h0.4243469c0.0506134,0,0.0916901,0.0410643,0.0916901,0.0916872
					C231.3460846,10.7346249,231.3050079,10.7756891,231.2543945,10.7756891z"
            />
            <path
              class="st18"
              d="M237.1689453,10.7756891h-0.4243622c-0.0505981,0-0.0916748-0.0410643-0.0916748-0.0916882
					c0-0.0506229,0.0410767-0.0916872,0.0916748-0.0916872h0.4243622c0.0506287,0,0.0916901,0.0410643,0.0916901,0.0916872
					C237.2606354,10.7346249,237.219574,10.7756891,237.1689453,10.7756891z"
            />
          </g>
        </g>
        <text transform="matrix(1 0 0 1 230.5752411 16.1485672)" class="st2 st3 st4">Sieť</text>
        <g>
          <text transform="matrix(1 0 0 1 210.100174 35.4120407)"><tspan x="0" y="0" class="st19 st6 st7">celk</tspan><tspan x="2.9589844" y="0" class="st19 st6 st7">ov</tspan><tspan x="4.6992188" y="0" class="st19 st6 st7">o</tspan></text>
          <text transform="matrix(1 0 0 1 201.166153 33.377861)"><tspan x="0" y="0" class="st6 st7">SP</tspan><tspan x="2.0478516" y="0" class="st6 st7">O</tspan><tspan x="3.1728516" y="0" class="st6 st7">TREBA </tspan><tspan x="8.734375" y="0" class="st6 st7">Z</tspan><tspan x="9.7265625" y="0" class="st6 st7">O SI</tspan><tspan x="12.7402344" y="0" class="st6 st7">E</tspan><tspan x="13.7197266" y="0" class="st6 st7">TE</tspan></text>
        </g>

        <linearGradient
          id="SVGID_5_"
          gradientUnits="userSpaceOnUse"
          x1="199.5598297"
          y1="23.2391052"
          x2="218.1338348"
          y2="23.2391052"
        >
          <stop offset="0" style="stop-color:#EF4136" />
          <stop offset="1" style="stop-color:#BE1E2D" />
        </linearGradient>
        <path
          class="st20"
          d="M201.0545502,29.6650524c-0.6354065-1.2155571-0.9947205-2.5982647-0.9947205-4.0648937
			c0-4.8529263,3.934082-8.7869987,8.7870026-8.7869987s8.7870026,3.9340725,8.7870026,8.7869987
			c0,1.4649868-0.3585205,2.8462391-0.9925842,4.0608082"
        />
        <g class="st5" />
        <g>
          <text transform="matrix(1 0 0 1 259.7466736 35.518486)"><tspan x="0" y="0" class="st19 st6 st7">celk</tspan><tspan x="2.9589844" y="0" class="st19 st6 st7">ov</tspan><tspan x="4.6992188" y="0" class="st19 st6 st7">o</tspan></text>
          <text transform="matrix(1 0 0 1 251.0118561 33.4843063)"><tspan x="0" y="0" class="st6 st7">DO</tspan><tspan x="2.2568359" y="0" class="st6 st7">D</tspan><tspan x="3.3496094" y="0" class="st6 st7">Á</tspan><tspan x="4.3369141" y="0" class="st6 st7">VKA </tspan><tspan x="7.9228516" y="0" class="st6 st7">D</tspan><tspan x="8.9160156" y="0" class="st6 st7">O SI</tspan><tspan x="11.9306641" y="0" class="st6 st7">E</tspan><tspan x="12.9101563" y="0" class="st6 st7">TE</tspan></text>
        </g>
        <g class="st5" />

        <linearGradient
          id="SVGID_6_"
          gradientUnits="userSpaceOnUse"
          x1="249.1066895"
          y1="23.3061008"
          x2="267.6806946"
          y2="23.3061008"
        >
          <stop offset="0" style="stop-color:#F7941D" />
          <stop offset="0.1320122" style="stop-color:#F68522" />
          <stop offset="0.3216028" style="stop-color:#F37425" />
          <stop offset="0.5219853" style="stop-color:#F26627" />
          <stop offset="0.738451" style="stop-color:#F15D29" />
          <stop offset="1" style="stop-color:#F15A29" />
        </linearGradient>
        <path
          class="st21"
          d="M250.6014099,29.7320461c-0.6354065-1.2155552-0.9947205-2.5982628-0.9947205-4.0648918
			c0-4.8529263,3.934082-8.7870007,8.7869873-8.7870007c4.8529358,0,8.7870178,3.9340744,8.7870178,8.7870007
			c0,1.4649868-0.3585205,2.8462391-0.9925842,4.0608063"
        />
      </g>
      <path class="st0" d="M233.8002472,26.8923645v10.9337349c0,1.6499977-1.3340149,3-2.9644928,3" />
      <path
        class="actextrectangle"
        d="M242.2328186,26.7404976h-16.8229675c-1.1000061,0-2-0.8999996-2-2v-3.4209518
		c0-1.1000004,0.8999939-2,2-2h16.8229675c1.1000061,0,2,0.8999996,2,2v3.4209518
		C244.2328186,25.840498,243.3328247,26.7404976,242.2328186,26.7404976z"
      />
      <g id="static-text_2_">
        <text transform="matrix(1 0 0 1 253.6850739 28.2007828)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 258.3858643 28.0096207)" class="st14 st6 st13">|</text>
        <text transform="matrix(1 0 0 1 203.9887848 28.2004623)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 208.689621 28.0093002)" class="st14 st6 st13">|</text>
      </g>
      <g id="dynamic-charts_2_">
        <path
          id="grid-consumption-chart"
          ref="gridConsumptionChart"
          class="acchartcustom"
          d="M216.5610199,29.6609669
			c0.634079-1.2145691,0.9925842-2.5958214,0.9925842-4.0608082c0-4.8529263-3.9340668-8.7869987-8.7870026-8.7869987
			c-4.8529205,0-8.7869873,3.9340725-8.7869873,8.7869987c0,1.466629,0.359314,2.8493366,0.9947205,4.0648937"
        />
        <path
          id="grid-power-chart"
          ref="gridPowerChart"
          class="acchartcustom"
          d="M266.1881104,29.7279606
			c0.6340637-1.2145672,0.9925842-2.5958195,0.9925842-4.0608063c0-4.8529263-3.934082-8.7870007-8.7870178-8.7870007
			c-4.8529053,0-8.7869873,3.9340744-8.7869873,8.7870007c0,1.466629,0.359314,2.8493366,0.9947205,4.0648918"
        />
      </g>
      <g id="dynamic-text_2_">
        <circle
          id="grid-center"
          ref="gridMainCenter"
          class="st15"
          cx="233.7410889"
          cy="23.0300217"
          r="0.0802455"
        />
        <circle
          id="grid-power-percent"
          ref="gridCurrentPowerPercentCenter"
          class="st15"
          cx="261.2963257"
          cy="27.3204594"
          r="0.0802455"
        />
        <circle
          id="current-grid-power"
          ref="gridCurrentPowerCenter"
          class="st15"
          cx="258.3936768"
          cy="23.560648"
          r="0.0802455"
        />
        <circle
          id="grid-power-total"
          ref="gridCurrentPowerTotalCenter"
          class="st15"
          cx="254.374527"
          cy="35.1414871"
          r="0.0802455"
        />
        <circle
          id="consumption-percent"
          ref="gridConsumptionPercentCenter"
          class="st15"
          cx="211.589035"
          cy="27.3452568"
          r="0.0802455"
        />
        <circle
          id="total-consumption"
          ref="gridConsumptionTotalCenter"
          class="st15"
          cx="205.1649323"
          cy="34.9786949"
          r="0.0802455"
        />
        <circle
          id="current-consumption"
          ref="gridConsumptionCenter"
          class="st15"
          cx="208.6863861"
          cy="23.5854435"
          r="0.0802455"
        />
      </g>
      <g id="arrows-in_2_" ref="gridArrowsIn">
        <polygon class="st29" points="233.7939301,27.4053993 232.3773651,30.4621925 235.2104797,30.4621925 		" />
        <polygon class="st29" points="233.8065643,30.9939537 232.3900146,34.0507469 235.2231293,34.0507469 		" />
        <polygon class="st29" points="233.8065643,34.5825081 232.3899994,37.6393013 235.223114,37.6393013 		" />
      </g>
      <g id="arrows-out_2_" ref="gridArrowsOut">
        <polygon class="st29" points="233.7939301,30.5159454 235.2104797,27.4591522 232.3773651,27.4591522 		" />
        <polygon class="st29" points="233.7939301,34.1044998 235.2104797,31.0477085 232.3773651,31.0477085 		" />
        <polygon class="st29" points="233.8065643,37.6930542 235.2231293,34.6362648 232.3900146,34.6362648 		" />
      </g>
    </g>
    <g id="heating-group" ref="heatingGroup">
      <g>
        <text transform="matrix(1 0 0 1 34.4004288 67.7241135)" class="st2 st3 st4">Kúrenie</text>
        <g>
          <text transform="matrix(1 0 0 1 17.4868355 69.2304001)"><tspan x="0" y="0" class="st19 st6 st7">celk</tspan><tspan x="2.9589844" y="0" class="st19 st6 st7">ov</tspan><tspan x="4.6992188" y="0" class="st19 st6 st7">o</tspan></text>
          <text transform="matrix(1 0 0 1 10.0509119 67.1962204)"><tspan x="0" y="0" class="st6 st7">DEN</tspan><tspan x="3.2773438" y="0" class="st6 st7">N</tspan><tspan x="4.4941406" y="0" class="st6 st7">Á SP</tspan><tspan x="8.0126953" y="0" class="st6 st7">O</tspan><tspan x="9.1376953" y="0" class="st6 st7">TREBA</tspan></text>
        </g>
        <linearGradient
          id="SVGID_7_"
          gradientUnits="userSpaceOnUse"
          x1="7.7409587"
          y1="57.0579758"
          x2="26.3149567"
          y2="57.0579758"
        >
          <stop offset="0" style="stop-color:#EF4136" />
          <stop offset="1" style="stop-color:#BE1E2D" />
        </linearGradient>
        <path
          class="st22"
          d="M9.2356777,63.4839249c-0.6354036-1.2155571-0.9947195-2.5982666-0.9947195-4.0648956
			c0-4.8529243,3.9340734-8.7869987,8.7869997-8.7869987s8.7869987,3.9340744,8.7869987,8.7869987
			c0,1.4649887-0.358511,2.846241-0.9925842,4.0608063"
        />
        <g class="st5" />
        <path
          class="st1"
          d="M40.3720894,71.9571152c0.3462563-0.014946,0.4841652-0.5293045,0.8609924-0.3091812
			c0.3260803,0.1904755,0.0926323,0.8430786-0.062851,1.0990829c-0.4235153,0.6973267-0.8499489,0.2350769-1.1607552-0.2569962
			l-0.2093658-0.0279007c-0.1807365,0.2629623,0.1445961,0.5998688-0.0837135,0.8390732
			c-0.3398209,0.3560333-0.8873711-0.2827454-1.0285492-0.5738678c-0.3426895-0.7066498,0.2384377-0.8116302,0.8064957-0.8329163
			l0.131588-0.1714783c0.0143013-0.0540009-0.1085777-0.1369324-0.1521111-0.1631012
			c-0.1895981-0.1139755-0.4915352-0.0968018-0.5267029-0.3768463c-0.0632744-0.5038834,0.8035431-0.5868149,1.1506805-0.5483322
			c0.6533356,0.0724106,0.3852654,0.7292328,0.1925926,1.1141739L40.3720894,71.9571152z M39.6538086,72.2048416
			c0.0915909-0.0759964,0.1812744,0.0418396,0.2702484,0.0501022c0.0756989,0.0070267,0.1275711-0.070694,0.2139626-0.0139313
			c0.0350113,0.0230026,0.1952858,0.3226166,0.2628517,0.3997726c0.0507774,0.0579834,0.2470779,0.2430115,0.31847,0.2364426
			c0.1059875-0.009758,0.296917-0.3251953,0.3379936-0.4238281c0.0521355-0.1251831,0.2244987-0.6952972-0.0496025-0.612114
			c-0.0386467,0.011734-0.1295967,0.1291656-0.1766396,0.1649933c-0.1283417,0.0977478-0.4895477,0.2544403-0.6408615,0.1825104
			c-0.0785789-0.0373535-0.048954-0.1666565-0.0690575-0.2321472c-0.0235176-0.0766068-0.1173172-0.0825348-0.1132622-0.1890335
			c0.0024529-0.0643616,0.1810608-0.3284302,0.2168121-0.4480209c0.0337372-0.1128387,0.1045494-0.3620987-0.0298424-0.420578
			c-0.2220039-0.096611-0.5938187-0.028923-0.8130951,0.0595627c-0.1073875,0.043335-0.2407608,0.0816574-0.1921654,0.2229462
			c0.0152168,0.0442429,0.2222366,0.0892487,0.2849388,0.1165237c0.1348572,0.0586777,0.37183,0.2358017,0.4312973,0.3717194
			c0.0773468,0.1767883-0.0525208,0.1492767-0.1262436,0.2287979c-0.0642815,0.0693359-0.029892,0.1968689-0.132431,0.2288284
			c-0.0656395,0.020462-0.1814461-0.0124893-0.2699852-0.0085754c-0.1231155,0.0054474-0.4436531,0.0833511-0.5380821,0.1591721
			c-0.0725555,0.0582657-0.0000267,0.1993484,0.0287132,0.2693558c0.0855141,0.2082825,0.271534,0.4431076,0.464592,0.5595016
			c0.3533401,0.2130356,0.1910286-0.1574173,0.1777573-0.3182526
			C39.4993935,72.6579666,39.5522499,72.2891083,39.6538086,72.2048416z M42.5751572,73.8580017v-0.1077652h-3.7487144
			c-0.1481056-0.0000305-0.2823067-0.0603714-0.3804626-0.1583786c-0.0980034-0.0981522-0.1583481-0.2323532-0.1583748-0.3804626
			v-2.564621c0.0000267-0.1481094,0.0603714-0.2823105,0.1583748-0.3804626
			c0.098156-0.0980072,0.2323608-0.1583481,0.3804626-0.1583786h3.7487144
			c0.1481018,0.0000305,0.2823067,0.0603714,0.3804626,0.1583786c0.0980034,0.0981522,0.1583481,0.2323532,0.1583748,0.3804626
			v2.564621c-0.0000267,0.1481094-0.0603714,0.2823105-0.1583748,0.3804626
			c-0.098156,0.0980072-0.2323608,0.1583481-0.3804626,0.1583786V73.8580017v0.1077652
			c0.207531,0.0000305,0.3966293-0.0851135,0.5328674-0.2215042c0.1363907-0.1362381,0.2215309-0.3253326,0.2215042-0.5328674
			v-2.564621c0.0000267-0.2075348-0.0851135-0.3966293-0.2215042-0.5328674
			c-0.1362381-0.1363907-0.3253403-0.2215347-0.5328674-0.2215042h-3.7487144
			c-0.207531-0.0000305-0.3966293,0.0851135-0.5328674,0.2215042c-0.1363907,0.1362381-0.2215347,0.3253326-0.2215042,0.5328674
			v2.564621c-0.0000305,0.2075348,0.0851135,0.3966293,0.2215042,0.5328674
			c0.1362381,0.1363907,0.3253365,0.2215347,0.5328674,0.2215042h3.7487144V73.8580017z M41.5156555,70.8796844h1.1840897
			c0.0595169,0,0.1077652-0.0482559,0.1077652-0.1077728s-0.0482483-0.1077652-0.1077652-0.1077652h-1.1840897
			c-0.0595169,0-0.107769,0.0482483-0.107769,0.1077652S41.4561386,70.8796844,41.5156555,70.8796844 M41.9593887,73.2845993
			h0.7467194c0.0595207,0,0.107769-0.0482483,0.107769-0.1077652s-0.0482483-0.1077652-0.107769-0.1077652h-0.7467194
			c-0.0595169,0-0.1077652,0.0482483-0.1077652,0.1077652S41.8998718,73.2845993,41.9593887,73.2845993 M41.9836121,72.4855118
			h0.7467194c0.0595169,0,0.107769-0.0482483,0.107769-0.1077652c0-0.0595245-0.0482521-0.1077728-0.107769-0.1077728h-0.7467194
			c-0.0595169,0-0.107769,0.0482483-0.107769,0.1077728C41.875843,72.4372635,41.9240952,72.4855118,41.9836121,72.4855118
			 M42.0076332,71.7194901h0.7467194c0.0595169,0,0.107769-0.0482483,0.107769-0.1077652s-0.0482521-0.1077652-0.107769-0.1077652
			h-0.7467194c-0.0595169,0-0.107769,0.0482483-0.107769,0.1077652S41.9481163,71.7194901,42.0076332,71.7194901"
        />
      </g>
      <path class="st0" d="M39.6371002,53.657753v-9.825901c0-1.6499977,1.3340187-3,2.9644852-3" />
      <g class="st5" />
      <linearGradient
        id="SVGID_8_"
        gradientUnits="userSpaceOnUse"
        x1="7.7409821"
        y1="57.0579758"
        x2="26.3149815"
        y2="57.0579758"
      >
        <stop offset="0" style="stop-color:#31B04A" />
        <stop offset="1" style="stop-color:#009548" />
      </linearGradient>
      <path
        class="st23"
        d="M9.2357016,63.4839249c-0.6354036-1.2155571-0.9947195-2.5982666-0.9947195-4.0648956
		c0-4.8529243,3.9340734-8.7869987,8.7869987-8.7869987s8.7870007,3.9340744,8.7870007,8.7869987
		c0,1.4649887-0.358511,2.846241-0.9925861,4.0608063"
      />
      <path
        class="actextrectangle"
        d="M49.1539879,61.0345459H32.3310127c-1.0999985,0-2-0.9000015-2-2v-3.4209518
		c0-1.1000023,0.9000015-2,2-2h16.8229752c1.0999985,0,2,0.8999977,2,2v3.4209518
		C51.1539879,60.1345444,50.2539864,61.0345459,49.1539879,61.0345459z"
      />
      <g id="static-texts">
        <text transform="matrix(1 0 0 1 12.2501612 62.0193405)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 16.9509964 61.8281784)" class="st14 st6 st13">|</text>
      </g>
      <g id="dynamic-charts_3_">
        <path
          id="daily-consumption-chart"
          ref="heatingTodayConsumptionChart"
          class="acchartcustom"
          d="M24.8223953,63.4798355
			c0.6340752-1.2145653,0.9925861-2.5958176,0.9925861-4.0608063c0-4.8529243-3.9340744-8.7869987-8.7870007-8.7869987
			s-8.7869987,3.9340744-8.7869987,8.7869987c0,1.466629,0.3593159,2.8493385,0.9947195,4.0648956"
        />
      </g>
      <g id="dynamic-text_3_">
        <circle
          id="todays-consumption-percent"
          ref="heatingTodayPercentCenter"
          class="st15"
          cx="19.8504086"
          cy="61.1641273"
          r="0.0802455"
        />
        <circle
          id="todays-consumption-big"
          ref="heatingTodayCenter"
          class="st15"
          cx="16.9477596"
          cy="57.4043159"
          r="0.0802455"
        />
        <circle
          id="heating-center"
          ref="heatingMainCenter"
          class="st15"
          cx="40.6622543"
          cy="57.32407"
          r="0.0802455"
        />
        <circle
          id="todays-consumption-percent_1_"
          ref="heatingTodayTotalCenter"
          class="st15"
          cx="13.0387688"
          cy="68.8043671"
          r="0.0802455"
        />
      </g>
      <g id="arrows-in_3_" ref="heatingArrowsIn">
        <polygon class="st29" points="39.6244621,46.0906715 41.0410194,43.0338821 38.2079048,43.0338821 		" />
        <polygon class="st29" points="39.6244583,49.6792297 41.0410156,46.6224365 38.207901,46.6224365 		" />
        <polygon class="st29" points="39.6371002,53.2677841 41.0536575,50.2109909 38.2205429,50.2109909 		" />
      </g>
    </g>
    <g id="tuv-group" ref="tuvGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 100.3231277 67.5239334)" class="st2 st3 st4">TÚV</text>
          <text transform="matrix(1 0 0 1 76.4947891 67.2021713)" class="st6 st9">TEPLOTA</text>
          <linearGradient
            id="SVGID_9_"
            gradientUnits="userSpaceOnUse"
            x1="70.5708084"
            y1="57.0632324"
            x2="89.1448059"
            y2="57.0632324"
          >
            <stop offset="0" style="stop-color:#EF4136" />
            <stop offset="1" style="stop-color:#BE1E2D" />
          </linearGradient>
          <path
            class="st24"
            d="M72.0655289,63.4891777c-0.6354065-1.2155571-0.9947205-2.5982628-0.9947205-4.0648918
				c0-4.8529282,3.9340744-8.7869987,8.7870026-8.7869987c4.8529205,0,8.7869949,3.9340706,8.7869949,8.7869987
				c0,1.4649849-0.3585129,2.8462372-0.9925842,4.0608063"
          />
          <g class="st5" />
        </g>
        <g>
          <path
            class="st1"
            d="M105.7238617,71.0061111c-0.0321045-0.3947144-0.3531494-0.7174072-0.75-0.7446289l-0.001709-0.0001221
				l-2.0322266-0.000061l-0.0015259,0.0001221c-0.3988037,0.0241699-0.7229614,0.3485718-0.7550049,0.744751l-0.0001831,0.0019531
				l0.000061,3.9544067l0.0001221,0.0021362c0.031189,0.3648071,0.3092651,0.6723633,0.671936,0.7329712l0.0030518,0.0005493
				l0.2354126,0.0097046l0.0863647,0.1326904l0.0148315,0.0227051h0.0270996h0.0426025l-0.0037842,0.1243896l0.0255737,0.1503296
				l0.1362915,0.0942383l0.0026855,0.0003052l0.211792-0.0010986l0.0220947-0.000061l0.1210938-0.1345825l0.0128174-0.0142212
				v-0.2192993h0.3206787v0.2000732v0.0175781l0.102417,0.1279297l0.0145874,0.0182495l0.2473755,0.0053711l0.0220947,0.0004883
				l0.1292114-0.1356812l0.0137329-0.0144043l0.0005493-0.2196045h0.0412598h0.0097656l0.0389404-0.0157471l0.0175781-0.0071411
				l0.0650024-0.1310425l0.0459595,0.0004883l0.2553101-0.026001c0.3318481-0.0838623,0.5769043-0.3795166,0.6061401-0.7191772
				l0.0001831-0.0020752v-3.9544678L105.7238617,71.0061111z M104.335495,76.0157547v-0.1524658h0.0916748v0.1524658H104.335495z
				 M103.4800873,76.0157547v-0.1524658h0.0916748v0.1524658H103.4800873z M102.3993988,71.0655594
				c0.0016479-0.302124,0.2450562-0.5665894,0.5496826-0.5874023l2.0133667,0.000061
				c0.3034058,0.024292,0.5436401,0.2863159,0.5454102,0.5873413H102.3993988z M102.4003143,73.7384109v-2.4436035h3.1066284
				v2.4436035H102.4003143z M104.9446259,75.4924393l-1.9821167-0.000061
				c-0.2952271-0.0151978-0.5400391-0.2548828-0.5623779-0.5490723l0.0001831-0.9731445h3.1066284l0.0001831,0.9729004
				C105.4842987,75.2386551,105.2408905,75.4759598,104.9446259,75.4924393z"
          />
          <path
            class="st1"
            d="M105.2098846,71.6260452l-0.043396,0.0362549v1.6877441l0.1102295-0.0021973
				c0.0073242-0.5598755,0.0074463-1.1234741-0.000061-1.6832886L105.2098846,71.6260452z"
          />
          <path
            class="st1"
            d="M103.9198456,72.9024734c-0.3154297,0.0354614-0.2818604,0.5444336,0.072998,0.5029907
				C104.2953949,73.3701859,104.2651825,72.8635941,103.9198456,72.9024734z M104.0281219,73.2678299
				c-0.0828247,0.0578003-0.1993408,0.0006104-0.2095337-0.0983887l0.1151733-0.1502075
				C104.0765839,73.0007401,104.1421967,73.1880569,104.0281219,73.2678299z"
          />
          <path
            class="st1"
            d="M103.9012299,71.7105789c-0.3601074,0.0346069-0.541687,0.4559937-0.3394165,0.7526855
				c0.2000122,0.2932739,0.6381836,0.2700195,0.8085938-0.0404053
				C104.5581512,72.0810013,104.2881317,71.6733475,103.9012299,71.7105789z M104.2292938,72.4218826
				c-0.1245117,0.1502686-0.3522339,0.1774292-0.5025024,0.0493774c-0.2386475-0.2034912-0.1387939-0.5826416,0.1660767-0.640564
				v0.2313843l0.0184326,0.032959l0.0766602,0.0061035l0.0217285-0.0296631v-0.2407837
				C104.2844086,71.8752518,104.4089203,72.2049637,104.2292938,72.4218826z"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="103.8781891"
        y1="53.4722481"
        x2="103.8781891"
        y2="40.969162"
      />
      <path
        class="actextrectangle"
        d="M112.3730392,60.999073H95.5500641c-1.0999985,0-2-0.9000015-2-2v-3.4209557
		c0-1.0999985,0.9000015-2,2-2h16.8229752c1.0999985,0,2,0.9000015,2,2v3.4209557
		C114.3730392,60.0990715,113.4730377,60.999073,112.3730392,60.999073z"
      />
      <g id="dynamic-charts_4_">
        <path
          id="daily-consumption-chart_1_"
          ref="tuvStateChart"
          class="acchartcustom"
          d="M87.6522217,63.4850922
			c0.6340714-1.2145691,0.9925842-2.5958214,0.9925842-4.0608063c0-4.8529282-3.9340744-8.7869987-8.7869949-8.7869987
			c-4.8529282,0-8.7870026,3.9340706-8.7870026,8.7869987c0,1.466629,0.359314,2.8493347,0.9947205,4.0648918"
        />
      </g>
      <g id="dynamic-texts">
        <circle
          id="current-temperature"
          ref="tuvStateCurrentTemperatureCenter"
          class="st15"
          cx="79.857811"
          cy="58.5293961"
          r="0.0802455"
        />
        <circle
          id="heating-center_1_"
          ref="tuvMainCenter"
          class="st15"
          cx="103.8813019"
          cy="57.2885933"
          r="0.0802455"
        />
      </g>
      <g id="arrows-in_4_" ref="tuvArrowsIn">
        <polygon class="st29" points="103.8718643,45.144825 105.2884216,42.0880356 102.455307,42.0880356 		" />
        <polygon class="st29" points="103.8718643,48.7333832 105.2884216,45.67659 102.455307,45.67659 		" />
        <polygon class="st29" points="103.8845062,52.3219376 105.3010635,49.2651443 102.4679489,49.2651443 		" />
      </g>
    </g>
    <g id="household-group" ref="householdGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 158.4012909 67.6608658)" class="st2 st3 st4">Domácnosť</text>
          <g>
            <text transform="matrix(1 0 0 1 144.7348785 69.2177048)"><tspan x="0" y="0" class="st19 st6 st7">celk</tspan><tspan x="2.9589844" y="0" class="st19 st6 st7">ov</tspan><tspan x="4.6992188" y="0" class="st19 st6 st7">o</tspan></text>
            <text transform="matrix(1 0 0 1 137.299942 67.1835251)"><tspan x="0" y="0" class="st6 st7">DEN</tspan><tspan x="3.2773438" y="0" class="st6 st7">N</tspan><tspan x="4.4941406" y="0" class="st6 st7">Á SP</tspan><tspan x="8.0126953" y="0" class="st6 st7">O</tspan><tspan x="9.1376953" y="0" class="st6 st7">TREBA</tspan></text>
          </g>

          <linearGradient
            id="SVGID_10_"
            gradientUnits="userSpaceOnUse"
            x1="134.9894409"
            y1="57.0447731"
            x2="153.563446"
            y2="57.0447731"
          >
            <stop offset="0" style="stop-color:#EF4136" />
            <stop offset="1" style="stop-color:#BE1E2D" />
          </linearGradient>
          <path
            class="st25"
            d="M136.4841614,63.4707184c-0.6354065-1.2155571-0.9947205-2.5982628-0.9947205-4.0648956
				c0-4.8529243,3.9340668-8.7869987,8.7870026-8.7869987c4.8529205,0,8.7870026,3.9340744,8.7870026,8.7869987
				c0,1.4649887-0.3585205,2.846241-0.9925842,4.0608101"
          />
          <g class="st5" />
        </g>
        <g>
          <path
            class="st26"
            d="M172.7639465,74.8966064l-1.4628906-1.015625v-1.6015625
				c0-0.0595703-0.0478516-0.1074219-0.1074219-0.1074219s-0.1074219,0.0478516-0.1074219,0.1074219v1.6582031
				c0,0.0351563,0.0166016,0.0683594,0.0458984,0.0888672l1.2260742,0.8505859h-0.5390625
				c-0.0595703,0-0.1074219,0.0478516-0.1074219,0.1074219v1.1015625c0,0.0595703,0.0478516,0.1074219,0.1074219,0.1074219
				s0.1074219-0.0478516,0.1074219-0.1074219v-0.9941406h0.7758789c0.0473633,0,0.0888672-0.0302734,0.1035156-0.0751953
				C172.8196106,74.9708252,172.8020325,74.9210205,172.7639465,74.8966064z"
          />
          <path
            class="st26"
            d="M170.4392395,72.2012939c-0.0209961,0.0205078-0.0327148,0.0478516-0.0327148,0.0761719
				l-0.0229492,1.0175781l-0.9780273-0.7099609c-0.0361328-0.0253906-0.0839844-0.0283203-0.1274414-0.0019531l-3.2861328,2.3544922
				c-0.0380859,0.0273438-0.0541992,0.0771484-0.0400391,0.1210938c0.0146484,0.0439453,0.0556641,0.0742188,0.1030273,0.0742188
				h0.925293v0.953125c0,0.0595703,0.0483398,0.1074219,0.1079102,0.1074219s0.1074219-0.0478516,0.1074219-0.1074219v-1.0605469
				c0-0.0595703-0.0478516-0.1074219-0.1074219-0.1074219h-0.6977539l2.9506836-2.1142578l1.0825195,0.7871094
				c0.0307617,0.0214844,0.0717773,0.0253906,0.1118164,0.0087891c0.0351563-0.0185547,0.0581055-0.0546875,0.0585938-0.0947266
				l0.0273438-1.2226563c0.0009766-0.0292969-0.0087891-0.0556641-0.0307617-0.0791016
				C170.5495911,72.1641846,170.4851379,72.1622314,170.4392395,72.2012939z M170.4743347,72.237793l-0.000061,0.000061
				l-0.0003662-0.0004272L170.4743347,72.237793z"
          />
          <path
            class="st26"
            d="M171.2481384,71.704895l0.0216675,0.005188v-0.0441895l0.0141602-0.0749512l-0.0323486,0.012085
				c-0.017395-0.0358887-0.0488892-0.0664063-0.1097412-0.0853271c-0.0483398-0.0146484-0.112793-0.0253906-0.1694336-0.0351563
				l-0.0083008,0.0497437l0,0l-0.0078125-0.0516968c-0.0581055-0.0107422-0.1240234-0.0214844-0.175293-0.0361328
				c-0.0302734-0.0097656-0.0522461-0.0175781-0.065918-0.0253906l-0.0048828-0.0029297l0.003418-0.0019531
				c0.015625-0.0048828,0.0400391-0.0117188,0.0791016-0.0195313c0.0556641-0.0097656,0.1220703-0.0166016,0.1928711-0.0234375
				l0.0273438-0.0029297c0.1040039-0.0097656,0.2119141-0.0195313,0.2973633-0.0410156
				c0.0527344-0.0136719,0.09375-0.03125,0.125-0.0537109c0.0255737-0.0183716,0.0280762-0.0472412,0.0397339-0.0722656
				l0.0325317,0.0117188l-0.0026245-0.0661011c0-0.0001221,0.0001831-0.0001831,0.0001831-0.0003052l0.0014648-0.0820313
				l-0.0231323,0.0115967c-0.0123901-0.0205688-0.0303345-0.0418701-0.0628052-0.0594482
				c-0.0341797-0.0185547-0.0786133-0.03125-0.1381836-0.0419922c-0.0917969-0.0146484-0.2104492-0.0185547-0.3251953-0.0214844
				l-0.027832-0.0009766c-0.1254883-0.0029297-0.2202148-0.0058594-0.3007813-0.015625
				c-0.0483398-0.0048828-0.0854492-0.0126953-0.1108398-0.0214844l-0.0053101-0.00177l0.0057983-0.0021362
				c0.0253906-0.0097656,0.0644531-0.0195313,0.1186523-0.0273438c0.090332-0.0146484,0.1982422-0.0234375,0.3198242-0.0322266
				c0.152832-0.0107422,0.3120117-0.0234375,0.4355469-0.0498047c0.0629883-0.0136719,0.2519531-0.0527344,0.2597656-0.1982422
				l0.0415039-0.0302734l-0.046875-0.0175781c-0.0068359-0.0244141-0.0200195-0.0449219-0.0395508-0.0634766
				c-0.097168-0.0917969-0.3310547-0.0830078-0.703125-0.0595703c-0.1367188,0.0097656-0.2919922,0.0166016-0.4174805,0.0146484
				c-0.1157227-0.0039063-0.1669922-0.0175781-0.1894531-0.0283203l-0.0039063-0.0019531l0.0039063-0.0019531
				c0.0224609-0.015625,0.0766602-0.0371094,0.2055664-0.0537109c0.1254883-0.0166016,0.2905273-0.0224609,0.4360352-0.0283203
				l0.0258789-0.0009766c0.1850586-0.0078125,0.3764648-0.015625,0.5175781-0.0371094
				c0.0830078-0.0126953,0.1455078-0.0302734,0.1914063-0.0527344c0.0883789-0.0449219,0.1069336-0.1054688,0.1069336-0.1503906
				c-0.0004883-0.0400391-0.0336914-0.0722656-0.0771484-0.0732422c-0.0004883,0-0.0014648,0-0.0024414,0
				c-0.0419922,0-0.0756836,0.0292969-0.0786133,0.0683594v0.0019531c-0.0097656,0.0087891-0.0532227,0.0361328-0.2114258,0.0556641
				c-0.1142578,0.0146484-0.2602539,0.0214844-0.4008789,0.0273438l-0.0424805,0.0019531
				c-0.2075195,0.0068359-0.3911133,0.0146484-0.5356445,0.0371094c-0.0834961,0.0136719-0.1464844,0.03125-0.1933594,0.0546875
				c-0.081543,0.0400391-0.1044922,0.0947266-0.1083984,0.1376953l-0.0390625,0.0253906l0.0415039,0.0205078
				c0.0063477,0.0292969,0.0214844,0.0556641,0.0449219,0.0771484c0.1000977,0.0957031,0.3457031,0.0878906,0.7412109,0.0644531
				c0.1264648-0.0078125,0.2460938-0.015625,0.3481445-0.015625l0.0400391,0.0009766
				c0.1035156,0.0029297,0.1533203,0.0136719,0.1762695,0.0224609l0.0043945,0.0019531l-0.0039063,0.0029297
				c-0.0185547,0.0126953-0.0610352,0.0322266-0.1601563,0.0507813c-0.0976563,0.0175781-0.2211914,0.0273438-0.3408203,0.0371094
				l-0.0483398,0.0039063c-0.1386719,0.0107422-0.2695313,0.0205078-0.3720703,0.0410156
				c-0.0576172,0.0107422-0.1069336,0.0263672-0.1445313,0.0458984c-0.041748,0.0236206-0.060791,0.0505371-0.0720215,0.0758667
				l-0.0227051-0.0075073l0.0079956,0.0672607l-0.0040894,0.0635986l0.0213623-0.0078735
				c0.0119019,0.0245361,0.0302734,0.0496216,0.0679932,0.0703735c0.0327148,0.0185547,0.0766602,0.0322266,0.1425781,0.0439453
				c0.1000977,0.015625,0.2290039,0.0195313,0.362793,0.0224609c0.1020508,0.0029297,0.2075195,0.0058594,0.2905273,0.015625
				c0.0625,0.0068359,0.0981445,0.015625,0.1166992,0.0224609l0.0056763,0.0020752l-0.0056763,0.0028076
				c-0.019043,0.0097656-0.050293,0.0195313-0.0922852,0.0283203c-0.0610352,0.0117188-0.1367188,0.0195313-0.2099609,0.0273438
				l-0.034668,0.0029297c-0.097168,0.0087891-0.1982422,0.0175781-0.2749023,0.0361328
				c-0.0483398,0.0117188-0.0839844,0.0263672-0.1098633,0.0439453c-0.0366211,0.0244141-0.0537109,0.0527344-0.0615234,0.0771484
				l-0.0698242,0.0253906l0.065918,0.0380859c0.0058594,0.0371094,0.0249023,0.0693359,0.0556641,0.0966797
				c0.0263672,0.0224609,0.0615234,0.0410156,0.1040039,0.0556641c0.0678711,0.0234375,0.1479492,0.0380859,0.2382813,0.0537109
				c0.0429688,0.0058594,0.0976563,0.015625,0.137207,0.0253906l0.0064087,0.0015259l-0.0064087,0.0023804
				c-0.0166016,0.0068359-0.0356445,0.0126953-0.050293,0.0166016l0.0145874,0.0477295c-0.000061,0,0.000061,0,0,0
				l-0.0267944-0.0438232c-0.0400391,0.0117188-0.0742188,0.0214844-0.1035156,0.0419922
				c-0.0952148,0.0634766-0.1347656,0.1630859-0.1206055,0.3037109c0.0039063,0.0400391,0.0385742,0.0712891,0.0825195,0.0712891
				h0.0004883c0.0239258-0.0019531,0.0429688-0.0117188,0.0571289-0.0283203
				c0.0136719-0.0166016,0.0200195-0.0380859,0.0175781-0.0585938c-0.0117188-0.1152344,0.027832-0.1416016,0.0507813-0.1572266
				c0.0087891-0.0058594,0.03125-0.0126953,0.0620117-0.0214844c0.0419922-0.0126953,0.0854492-0.0253906,0.1230469-0.0478516
				C171.219574,71.7703247,171.2344055,71.7376099,171.2481384,71.704895z"
          />
        </g>
      </g>
      <line
        class="st0"
        x1="168.5623169"
        y1="53.6199303"
        x2="168.5623169"
        y2="40.969162"
      />
      <path
        class="actextrectangle"
        d="M176.7403107,61.0481758h-16.8229675c-1.1000061,0-2-0.9000015-2-2V55.627224
		c0-1.1000023,0.8999939-2,2-2h16.8229675c1.1000061,0,2,0.8999977,2,2v3.4209518
		C178.7403107,60.1481743,177.8403168,61.0481758,176.7403107,61.0481758z"
      />
      <g id="static-texts_2_">
        <text transform="matrix(1 0 0 1 139.8827057 61.9654312)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 144.5835419 61.7742691)" class="st14 st6 st13">|</text>
      </g>
      <g id="dynamic-charts_5_">
        <path
          id="daily-consumption-chart_2_"
          ref="householdConsumptionChart"
          class="acchartcustom"
          d="M152.0708618,63.4666328
			c0.6340637-1.2145691,0.9925842-2.5958214,0.9925842-4.0608101c0-4.8529243-3.934082-8.7869987-8.7870026-8.7869987
			c-4.8529358,0-8.7870026,3.9340744-8.7870026,8.7869987c0,1.4666328,0.359314,2.8493385,0.9947205,4.0648956"
        />
      </g>
      <g id="dynamic-texts_1_">
        <circle
          id="todays-consumption-percent_3_"
          ref="householdTodayPercentCenter"
          class="st15"
          cx="147.4829102"
          cy="61.1101913"
          r="0.0802455"
        />
        <circle
          id="todays-consumption-big_2_"
          ref="householdTodayCenter"
          class="st15"
          cx="144.5802612"
          cy="57.3503799"
          r="0.0802455"
        />
        <circle
          id="household-center"
          ref="householdMainCenter"
          class="st15"
          cx="168.2485809"
          cy="57.3376999"
          r="0.0802455"
        />
        <circle
          id="todays-consumption-percent_4_"
          ref="householdTodayTotalCenter"
          class="st15"
          cx="140.6977386"
          cy="68.7799072"
          r="0.0802455"
        />
      </g>
      <g id="arrows-in_5_" ref="householdArrowsIn">
        <polygon class="st29" points="168.5496826,45.1605453 169.9662323,42.103756 167.1331177,42.103756 		" />
        <polygon class="st29" points="168.5496826,48.7491035 169.9662323,45.6923103 167.1331177,45.6923103 		" />
        <polygon class="st29" points="168.5623169,52.3376579 169.9788666,49.2808647 167.145752,49.2808647 		" />
      </g>
    </g>
    <g id="wall-box-group" ref="wallBoxGroup">
      <g>
        <g>
          <text transform="matrix(1 0 0 1 226.6579742 67.732811)" class="st2 st3 st4">Wall Box</text>
          <g>
            <text transform="matrix(1 0 0 1 210.3723907 69.2177048)"><tspan x="0" y="0" class="st19 st6 st7">celk</tspan><tspan x="2.9589844" y="0" class="st19 st6 st7">ov</tspan><tspan x="4.6992188" y="0" class="st19 st6 st7">o</tspan></text>
            <text transform="matrix(1 0 0 1 203.0518951 67.1835251)"><tspan x="0" y="0" class="st6 st7">DEN</tspan><tspan x="3.2773438" y="0" class="st6 st7">N</tspan><tspan x="4.4941406" y="0" class="st6 st7">Á SP</tspan><tspan x="8.0126953" y="0" class="st6 st7">O</tspan><tspan x="9.1376953" y="0" class="st6 st7">TREBA</tspan></text>
          </g>
          <path
            class="st27"
            d="M202.2359924,63.4706345c-0.6354065-1.2155571-0.9947205-2.5982628-0.9947205-4.0648918
				c0-4.8529282,3.934082-8.7869987,8.7870026-8.7869987s8.7870026,3.9340706,8.7870026,8.7869987
				c0,1.4649849-0.3585205,2.8462372-0.9925842,4.0608063"
          />
          <g class="st5" />
        </g>
        <g>
          <path
            class="st1"
            d="M236.1999359,73.2408905L236.1999359,73.2408905l-0.0002899-0.3386154
				c0-0.0001297-0.0000763-0.0002213-0.0000763-0.000351c0-0.0001221,0.000061-0.0002289,0.000061-0.000351v-0.0010071
				c0-0.0000534-0.0000458-0.0001068-0.0000458-0.0001602c0-0.0000458,0.0000305-0.0000992,0.0000305-0.000145
				c-0.0028076-0.1400833-0.0056152-0.2803955-0.0102539-0.4208527c-0.0046539-0.1405869-0.0111389-0.2810211-0.0189819-0.4212646
				c-0.0078583-0.1406326-0.0171051-0.2811203-0.0276947-0.4214401c-0.0106659-0.1408234-0.0226898-0.2815323-0.0365448-0.4219742
				c-0.0138855-0.1408005-0.0295868-0.2810516-0.0452576-0.4210587c-0.0000153-0.0002518-0.0001831-0.0004425-0.0002136-0.0006943
				s0.0000763-0.0004654,0.0000458-0.0007172l-0.0045319-0.0326233l-0.0001373-0.0008926
				c-0.0023346-0.014946-0.005127-0.029686-0.008255-0.0442123c-0.0037994-0.0176315-0.0081329-0.0351944-0.013031-0.0526276
				c-0.0049744-0.0176773-0.0105133-0.0351563-0.0166016-0.0524445c-0.0049591-0.0140686-0.0102844-0.0280151-0.015976-0.0418167
				l-0.0004272-0.0009918l-0.0129395-0.0296783l-0.0009155-0.0019989l-0.0148315-0.0304947l-0.0010071-0.0019608
				l-0.0152283-0.0282745l-0.0010223-0.0018158l-0.0153961-0.026123l-0.0010223-0.0016785l-0.0153961-0.024025l-0.0011292-0.0017014
				l-0.0188904-0.0271301l-0.0012665-0.0017548l-0.0193176-0.0254898l-0.001297-0.0016479l-0.0196228-0.02388l-0.0013123-0.0015488
				l-0.0198212-0.0223236l-0.0006561-0.0007401c-0.0154266-0.0167313-0.0315094-0.0328522-0.0482025-0.048317
				c-0.015976-0.0147705-0.032486-0.0289307-0.0495453-0.0424423c-0.017807-0.0141296-0.0361786-0.0275345-0.055069-0.0401993
				c-0.01828-0.0122528-0.0370483-0.023819-0.0562592-0.0346451c-0.0261536-0.0147171-0.053009-0.0279922-0.0803833-0.0399017
				c-0.0271301-0.0117874-0.05513-0.0224075-0.0840149-0.0312119c-0.0288696-0.0087967-0.0571747-0.0153732-0.0843048-0.0217285
				c-0.0000153-0.0000076-0.0000305,0.0000076-0.0000458,0s-0.0000305-0.0000153-0.000061-0.0000229
				c-0.000351-0.0000916-0.0007172,0.0000458-0.0010834-0.0000305c-0.0003662-0.0000839-0.0006256-0.0003357-0.0009918-0.0004044
				c-0.0832672-0.0158234-0.1669769-0.0317459-0.2515259-0.045578c-0.0832214-0.0136032-0.166687-0.0250702-0.2501984-0.0349731
				c-0.0830078-0.0098495-0.1662292-0.0181732-0.2496185-0.0248032c-0.0835571-0.0066452-0.1672668-0.0115891-0.2510071-0.0148697
				c-0.0824127-0.0032272-0.1649017-0.0048523-0.2473755-0.0048523h-0.0027466
				c-0.0834045,0.0000381-0.1667786,0.0017319-0.2500763,0.0050659c-0.0833435,0.0033417-0.1666107,0.0083313-0.2497559,0.0149918
				c-0.082962,0.0066528-0.165741,0.0149689-0.2483063,0.0248032c-0.0830536,0.009903-0.1660614,0.0213394-0.2488251,0.0348816
				c-0.0840302,0.0137634-0.1672516,0.0295944-0.2500153,0.0453339c-0.0003662,0.0000687-0.0006256,0.0003204-0.0009918,0.0004044
				c-0.0003662,0.0000687-0.0007019-0.0000534-0.0010681,0.0000305c0,0-0.0000305,0.0000153-0.0000305,0.0000153
				c-0.0000153,0-0.0000305,0-0.000061,0c-0.026886,0.006279-0.0549011,0.0127716-0.0834961,0.0213928
				c-0.0292664,0.0088348-0.0576477,0.0194931-0.0851746,0.0313644c-0.0281525,0.0121384-0.0557556,0.0256882-0.0826263,0.040741
				c-0.0192108,0.0107651-0.0379944,0.0222702-0.0563049,0.0344467c-0.0188904,0.0125732-0.0372772,0.0258713-0.05513,0.0398712
				c-0.0173035,0.0135803-0.0340881,0.0278168-0.0503082,0.0426559c-0.0169678,0.0155334-0.0333099,0.0317383-0.048996,0.0485535
				c-0.0147095,0.0157928-0.0288391,0.0321274-0.0423431,0.0489655c-0.0144196,0.0179901-0.0281219,0.0365448-0.0410614,0.0556183
				c-0.0119019,0.0175323-0.0231476,0.0355072-0.0337372,0.0538559c-0.0118408,0.0205154-0.0228271,0.0415192-0.0329437,0.0629272
				l-0.0003815,0.0008469l-0.0115356,0.0255585l-0.0008087,0.0018692l-0.0129242,0.0318298l-0.0004272,0.0010529
				c-0.0051422,0.0134811-0.009964,0.027092-0.0144348,0.0408249c-0.0050964,0.0156326-0.0097504,0.0314331-0.0139618,0.0473709
				c-0.0037842,0.0143051-0.0071716,0.0286865-0.0102234,0.0431137l-0.0002289,0.0011444l-0.0063171,0.0335312l-0.0003357,0.0019608
				l-0.003952,0.0262985c-0.0000305,0.0002289,0.0000763,0.0004349,0.0000305,0.0006714
				c-0.0000305,0.0002289-0.0001678,0.0004044-0.0001984,0.0006332c-0.0175171,0.1400833-0.0350952,0.2804871-0.0501404,0.4216385
				c-0.0149689,0.1405563-0.02742,0.2814407-0.0379181,0.4224472c-0.0104828,0.1404495-0.019043,0.281105-0.0256958,0.4219131
				c-0.0066528,0.1405258-0.0113831,0.2812805-0.0135803,0.4221878c-0.0013733,0.0882645-0.0017395,0.1764374-0.0017395,0.2644958
				c0,0.0523148,0.0001373,0.1045837,0.0002594,0.1567993l0,0l0.0002136,0.3384552
				c-0.0001221,0.0514679-0.0002441,0.1029739-0.0002441,0.1545181c0,0.0888062,0.0003662,0.1777344,0.00177,0.2667618
				c0.0021973,0.1408997,0.006958,0.2816467,0.0135956,0.4221573c0.0066681,0.140831,0.0152435,0.2815018,0.0257263,0.4219666
				c0.0105286,0.1410141,0.0229645,0.2819138,0.0379639,0.4224777c0.0150604,0.1411209,0.0326233,0.2814941,0.0501556,0.4215546
				c0.0000305,0.0002594,0.0001984,0.0004578,0.0002289,0.0007172c0.0000458,0.000267-0.000061,0.0005035-0.0000305,0.0007706
				l0.0048828,0.031723l0.0001373,0.0008545c0.0025635,0.0150528,0.0055847,0.0298996,0.0089417,0.0445404
				c0.0041656,0.0181961,0.0088959,0.036293,0.0142365,0.0542603c0.0051727,0.0174332,0.0108948,0.0346909,0.0171509,0.0517502
				c0.005188,0.0141449,0.0107574,0.0281601,0.0166626,0.0420227l0.0004578,0.0010071l0.0138702,0.0307541l0.0009613,0.0019989
				l0.0153656,0.0305481l0.0010223,0.0019302l0.0157318,0.0283661l0.0010376,0.0017929l0.0159302,0.0262451l0.0010376,0.0016479
				l0.0159149,0.0241928l0.0011597,0.0016861l0.0193939,0.0271683l0.001297,0.0017242l0.0198364,0.0255432l0.0013123,0.0016174
				l0.0201263,0.023941l0.0013275,0.0015182l0.0203247,0.0223846l0.0006714,0.0007248
				c0.0157471,0.016716,0.0321655,0.032814,0.0492096,0.0482407c0.0162811,0.0147552,0.0331268,0.0288849,0.0504913,0.042366
				c0.0181427,0.0140762,0.0368347,0.0274353,0.0560455,0.0400467c0.0186005,0.012207,0.037674,0.0237198,0.0571899,0.034462
				c0.0265503,0.0146255,0.0537872,0.0277863,0.0815582,0.0395813c0.0274963,0.0116806,0.0558777,0.022171,0.0850983,0.0308228
				c0.0292511,0.0086517,0.0578918,0.0150909,0.0853882,0.0213165c0.0000153,0.0000076,0.0000305-0.0000076,0.000061,0
				c0.0000153,0.0000076,0.0000458,0.0000229,0.0000763,0.0000305c0.000351,0.0000839,0.0007019-0.0000458,0.0010681,0.0000229
				c0.0003662,0.0000763,0.0006256,0.0003357,0.0010071,0.0003967c0.0909271,0.0165405,0.182312,0.0331802,0.2746124,0.0473785
				c0.0908051,0.0139771,0.1818695,0.0254974,0.272995,0.0352173c0.0908966,0.009697,0.1820221,0.0176239,0.2733002,0.0235825
				c0.091156,0.0059509,0.1824341,0.0099487,0.2737427,0.0120316c0.0496063,0.0011215,0.0992126,0.0016937,0.148819,0.0016937
				c0.0429993,0,0.0860138-0.0004272,0.1290131-0.0012741c0.0929413-0.0018387,0.1858673-0.005661,0.278656-0.0115051
				c0.0929871-0.0058594,0.185791-0.0137558,0.2783813-0.0234833c0.0927734-0.0097504,0.1854706-0.0213776,0.2779083-0.0355301
				c0.0939484-0.0143967,0.1869812-0.031311,0.279541-0.0481262c0.0003815-0.000061,0.0006409-0.0003204,0.0010071-0.0003967
				s0.0007019,0.0000534,0.0010681-0.0000229c0.0000153-0.0000076,0.0000458-0.0000229,0.000061-0.0000305
				c0.0000305-0.0000153,0.0000458,0.0000076,0.0000763,0c0.0270996-0.0061264,0.0553284-0.0124664,0.0841522-0.0209427
				c0.0294952-0.0086899,0.0581207-0.0192337,0.0858612-0.0310059c0.0283813-0.0120316,0.0562134-0.0254974,0.083313-0.0404816
				c0.0193939-0.0107269,0.0383453-0.0221939,0.0568085-0.0343552c0.0190582-0.0125504,0.0376282-0.0258408,0.0556488-0.0398407
				c0.0174713-0.0135803,0.0344086-0.0278244,0.0507813-0.0426865c0.0171204-0.0155487,0.0336304-0.0317688,0.0494537-0.0486298
				c0.0148621-0.015831,0.029129-0.0322189,0.0427704-0.0491104c0.0145569-0.0180511,0.0283966-0.0366745,0.0414581-0.0558319
				c0.0120239-0.0175934,0.0233917-0.0356369,0.0340881-0.0540848c0.0119324-0.0205994,0.0230255-0.0416946,0.0332336-0.0631943
				l0.0003967-0.0008392l0.0117035-0.0258026l0.0008087-0.0018997l0.0130615-0.0320053l0.000412-0.0010529
				c0.005188-0.0135345,0.0100403-0.0272064,0.0145416-0.0409775c0.0051575-0.0157471,0.0098572-0.0316467,0.0140991-0.0476761
				c0.00383-0.0144424,0.0072632-0.0289459,0.0103302-0.0434875l0.0002289-0.0011444l0.0063629-0.0336838l0.0003204-0.0019684
				l0.003952-0.026268c0.0000305-0.0002289-0.000061-0.0004349-0.0000305-0.0006638s0.0001831-0.0004044,0.0002136-0.0006332
				c0.0175323-0.1400757,0.0350952-0.2804718,0.0501556-0.421608c0.0149841-0.1405563,0.0274353-0.2814407,0.0379486-0.4224548
				c0.0104828-0.1404495,0.0190582-0.2811127,0.0257263-0.4219208c0.0066528-0.1405029,0.0113983-0.28125,0.0136108-0.4221497
				c0.0013733-0.0889664,0.0017548-0.1778259,0.0017548-0.266571
				C236.2001953,73.3440475,236.2000732,73.29245,236.1999359,73.2408905z M235.6930389,70.3216095
				c-0.0101318-0.0087814-0.0208435-0.0168381-0.0314484-0.0250549c0.0049133,0.0037003,0.0100861,0.0070648,0.0149078,0.0108795
				C235.6821899,70.3119431,235.6874847,70.3169327,235.6930389,70.3216095z M235.8186493,70.8230743
				c0.0098419,0.0862198,0.0196838,0.1723099,0.0287933,0.2583771c0.0091705,0.0865097,0.0176239,0.1731873,0.0255737,0.2599869
				c0.0079498,0.0867004,0.0153656,0.1734695,0.0222168,0.2602997c0.0068512,0.0868378,0.0131226,0.1737518,0.0188446,0.2607346
				c0.0057068,0.0866776,0.0108643,0.1734085,0.0154419,0.2601852c0.0045776,0.0867844,0.0085907,0.1736221,0.0120697,0.2605133
				c0.0034943,0.0867844,0.0064545,0.1735611,0.0087128,0.2603302c0.0022583,0.086647,0.0037994,0.1734695,0.0053406,0.2604294
				l0.0003052,0.337944c0,0.0000381,0.0000153,0.0000839,0.0000153,0.0001221c0,0.0000458-0.0000153,0.0000839-0.0000153,0.0001297
				c0.0003357,0.0610733,0.0006714,0.1220474,0.0006714,0.1829147c0,0.0264587-0.000061,0.0528946-0.0002136,0.0793152
				c-0.0005188,0.0870895-0.0020294,0.1742325-0.0042725,0.2613831c-0.002243,0.0871582-0.0052032,0.1742706-0.0089722,0.2613068
				c-0.0037537,0.0867233-0.0083008,0.1734009-0.0136261,0.2600098c-0.0053101,0.0866089-0.0113983,0.1731339-0.01828,0.2595596
				c-0.0068512,0.0861206-0.0144806,0.1721649-0.0228271,0.2581177s-0.017395,0.1717529-0.0274048,0.2573318
				c-0.0099487,0.0850525-0.0208588,0.1701279-0.0318146,0.2554016c-0.004425,0.0261536-0.008667,0.0510559-0.0144043,0.074234
				c-0.0057831,0.0234222-0.0134125,0.0467834-0.0224609,0.0699234c-0.0085449,0.0218658-0.0182037,0.0432129-0.0290375,0.0638809
				c-0.0108337,0.020752-0.0228882,0.0409164-0.036026,0.060379c-0.0131073,0.0193939-0.0272522,0.0380402-0.0423889,0.0557861
				c-0.0150909,0.0176544-0.0312042,0.0344925-0.0482178,0.0504074c-0.0170746,0.0159683-0.0350494,0.0309906-0.0538025,0.0449905
				c-0.018692,0.0139542-0.0381317,0.0268631-0.0581818,0.038559c-0.02005,0.0117035-0.0409088,0.0223007-0.0624084,0.0318451
				c-0.0215454,0.0095901-0.0433197,0.0179367-0.0651855,0.0246048c-0.0215759,0.0065994-0.0448456,0.0119858-0.0692902,0.0175858
				c-0.0505524,0.0094604-0.1008606,0.0188751-0.1510315,0.0274887c-0.0756836,0.0129929-0.1517487,0.0243073-0.2280884,0.0343018
				c-0.0763245,0.0100021-0.1528015,0.0186691-0.2294006,0.0259094c-0.1008759,0.0095291-0.2020111,0.0165787-0.3032532,0.0211792
				c-0.0950012,0.0043259-0.190094,0.0064774-0.2851868,0.0064774l-0.0187836-0.0000229
				c-0.0994568-0.0002975-0.1988831-0.0029602-0.2981873-0.0080261c-0.0992889-0.0050659-0.1985016-0.0125427-0.2975311-0.0222092
				c-0.0977783-0.009552-0.1952057-0.021225-0.2920532-0.035759c-0.096344-0.0144577-0.192749-0.0318756-0.2896881-0.0493927
				c-0.0248566-0.0056915-0.0485382-0.0111847-0.0704803-0.0179291c-0.0222626-0.0068359-0.0443726-0.0153656-0.0661926-0.0252151
				c-0.0215759-0.0097275-0.0425568-0.0205841-0.0627899-0.032547c-0.0203094-0.0120087-0.0399475-0.0251999-0.0588074-0.0394363
				c-0.0187988-0.0141983-0.0367737-0.0294037-0.053772-0.0455627c-0.0170288-0.0161743-0.0331421-0.0333786-0.0482788-0.051445
				c-0.0151672-0.0181274-0.0293274-0.0370941-0.0423889-0.0568008c-0.0130157-0.0196457-0.0249176-0.0399551-0.0355377-0.0608292
				c-0.0106354-0.0208664-0.0200806-0.0424805-0.0284424-0.0646515c-0.0083923-0.0222397-0.0155029-0.0446243-0.0209198-0.067009
				c-0.0053558-0.0220642-0.0093689-0.0457458-0.0135345-0.0706177c-0.0110474-0.0861206-0.0220795-0.1720428-0.0321045-0.2579193
				c-0.0100708-0.0861969-0.019165-0.1726303-0.0275421-0.2592087c-0.0083923-0.086586-0.0160522-0.1732483-0.0229034-0.2600021
				c-0.0068359-0.086441-0.0128937-0.1729813-0.0181732-0.2595978s-0.0097961-0.1733017-0.0135193-0.2600327
				c-0.0036926-0.0864182-0.0066071-0.1729126-0.0088043-0.2594452c-0.0022125-0.0865479-0.0036774-0.1730652-0.0041809-0.2595444
				c-0.0001373-0.0256119-0.0002136-0.0512466-0.0002136-0.0769043c0-0.0608521,0.000351-0.1218109,0.0007019-0.182869
				c0-0.000061-0.0000458-0.0001068-0.0000458-0.0001678c0-0.0000534,0.0000458-0.0001144,0.0000458-0.0001678l-0.0002289-0.3401489
				c0-0.0000534-0.0000305-0.0000839-0.0000305-0.0001297c0-0.0000534,0.0000305-0.0000839,0.0000305-0.0001297
				c-0.000351-0.0614471-0.0006866-0.1227875-0.0006866-0.1840286c0-0.0260849,0.000061-0.0521545,0.0001984-0.0782089
				c0.0005188-0.0870972,0.0020142-0.1742325,0.0042572-0.2613907c0.0022278-0.0871582,0.005188-0.174263,0.0089569-0.2612991
				c0.0037537-0.0867233,0.0082855-0.1734085,0.0136108-0.2600098c0.0053101-0.0866089,0.0113831-0.1731339,0.0182648-0.2595749
				c0.0068359-0.0861206,0.0144653-0.1721573,0.0228119-0.2581177c0.0083313-0.0859528,0.0173798-0.1717529,0.0273743-0.2573318
				c0.0099335-0.0850372,0.0208588-0.1700897,0.0317993-0.2553482c0.0042572-0.0253983,0.0083466-0.0495605,0.0138397-0.0720825
				c0.0055237-0.0227203,0.0127869-0.0454025,0.0213928-0.0678864c0.0083771-0.0219421,0.0178986-0.0433578,0.0285645-0.0641174
				c0.0107117-0.0208206,0.0226135-0.0410843,0.035614-0.0606384c0.0129547-0.0195007,0.0269775-0.0382462,0.0419769-0.0561066
				c0.0149536-0.0177612,0.0309448-0.0347214,0.0478516-0.0507507c0.0169678-0.0160904,0.0348206-0.03125,0.0534821-0.0453949
				c0.0185852-0.0140915,0.0379333-0.0271454,0.0579071-0.0389862s0.0407562-0.0225906,0.0621796-0.0323029
				c0.0214844-0.0097351,0.0431976-0.0182419,0.0650177-0.0250702c0.0214691-0.0067444,0.0446625-0.0122757,0.0690155-0.0180435
				c0.0639038-0.0123138,0.1274719-0.0245667,0.1909027-0.0354462c0.0638885-0.0109558,0.1281128-0.0206299,0.1925507-0.0293655
				c0.0427399-0.0057983,0.0855255-0.0111618,0.1283417-0.0160751c0.0855408-0.0098114,0.1713104-0.0177612,0.2572174-0.0238953
				c0.0855865-0.0061188,0.1712952-0.0104218,0.2570648-0.0128937c0.0583038-0.0016861,0.1166229-0.0025253,0.1749573-0.0025253
				l0.0161591,0.0000229c0.0637207,0.0001678,0.1274261,0.0013351,0.1911011,0.0035172
				c0.0013275,0.0000458,0.002655,0.0001678,0.0039978,0.0002213c0.0828247,0.0028763,0.1655884,0.0074005,0.2481995,0.0136871
				c0.078064,0.0059357,0.1559601,0.0136642,0.2337341,0.0226288c0.0054474,0.0006256,0.01091,0.000946,0.0163574,0.0015869
				c0.0744324,0.0086975,0.1485138,0.0191956,0.2223206,0.0308456c0.0218048,0.0033722,0.0435333,0.0071564,0.0652771,0.0107803
				c0.0678558,0.0116272,0.1357269,0.0239487,0.2039948,0.0369186c0.025177,0.0059814,0.0491028,0.0117493,0.071228,0.0188141
				c0.0224152,0.0071564,0.0446625,0.016098,0.0665894,0.0263901c0.0209351,0.0098343,0.0412598,0.0207443,0.0608521,0.0327377
				c0.0196686,0.0120316,0.0386505,0.0252075,0.0568695,0.0393982c0.018158,0.0141602,0.0354767,0.0292816,0.0518494,0.0453186
				c0.0163879,0.0160675,0.0318756,0.0331116,0.0463715,0.0509872c0.0145569,0.0179291,0.0281067,0.0366821,0.0405579,0.0561218
				c0.0124359,0.0193787,0.0237427,0.0394058,0.0338135,0.0599518c0.0100708,0.0205383,0.0189819,0.0417938,0.0268097,0.0635834
				c0.0078583,0.0218582,0.0144806,0.0438232,0.0194244,0.0657578
				C235.8113556,70.7753448,235.8149261,70.7986298,235.8186493,70.8230743z M232.5824127,70.3078918
				c0.0044708-0.0035095,0.0092621-0.0065918,0.0138092-0.0099945c-0.0098419,0.0075836-0.0197754,0.0150146-0.0292358,0.0230713
				C232.5721741,70.3166656,232.5771027,70.3120499,232.5824127,70.3078918z M232.4985962,75.7585907l-0.0190277-0.0209732
				l-0.0188446-0.0223846l-0.0185547-0.0238953l-0.0181732-0.0254517l-0.0148315-0.0225296l-0.0148621-0.0245056
				l-0.0147095-0.0264969l-0.0143585-0.0285797l-0.0130768-0.0289383c-0.005127-0.012085-0.0100098-0.0243149-0.0145416-0.0366516
				c-0.0054779-0.0149612-0.010498-0.0300827-0.0150299-0.0453415c-0.0046692-0.0157242-0.0088196-0.0316467-0.0125122-0.0477219
				c-0.0029449-0.0128708-0.0055542-0.0257034-0.0077209-0.0384674l-0.0046539-0.0302582
				c-0.0175018-0.1398163-0.0349426-0.2793121-0.0498352-0.4188919c-0.0148621-0.1392746-0.0272064-0.2790375-0.0376587-0.4190903
				c-0.0104065-0.1395035-0.0189209-0.2792053-0.0255432-0.4190674c-0.0066071-0.1395416-0.011322-0.2791595-0.013504-0.4187698
				c-0.0013733-0.0882111-0.0017548-0.1765823-0.0017548-0.2650909c0-0.0513916,0.0001221-0.1028214,0.0002594-0.1543045
				c0-0.0000381-0.0000153-0.0000534-0.0000153-0.0000839s0.0000153-0.0000458,0.0000153-0.0000839l-0.0002289-0.3386688
				c0-0.0000153-0.0000153-0.0000305-0.0000153-0.0000458c0-0.0000229,0.0000153-0.0000381,0.0000153-0.0000534
				c-0.0001373-0.0522156-0.0002594-0.1044006-0.0002594-0.1565323c0-0.0877533,0.0003662-0.1753693,0.0017395-0.2628326
				c0.0021667-0.1396179,0.0068665-0.2792511,0.0134583-0.4188004c0.0066071-0.1398392,0.0151215-0.2795258,0.0255127-0.4190063
				c0.010437-0.1400452,0.0227814-0.2798004,0.0376282-0.419075c0.0148926-0.1396637,0.0323486-0.2792358,0.0498505-0.419136
				l0.0036774-0.0245514l0.0059662-0.031662c0.0026703-0.0126648,0.0056458-0.025238,0.0089264-0.0376892
				c0.0036774-0.0139389,0.0077667-0.0277863,0.0122375-0.0415192c0.003891-0.01194,0.0080872-0.0237808,0.0125732-0.0355377
				l0.0122223-0.0301056l0.0107727-0.0238419c0.0088654-0.0187531,0.0184784-0.0371475,0.0288544-0.05513
				c0.0092773-0.016098,0.0191498-0.0318604,0.0295868-0.0472336c0.0113525-0.0167236,0.0233612-0.0330048,0.0360107-0.0487747
				c0.0118408-0.0147629,0.024231-0.0290985,0.0371399-0.0429382c0.0137482-0.0147552,0.0280762-0.0289612,0.0429535-0.0425797
				c0.0054016-0.0049438,0.0112152-0.0094147,0.0167542-0.0141983c-0.0180969,0.0160904-0.0355835,0.0328598-0.0520325,0.0505981
				c-0.0175476,0.0189209-0.0341187,0.0387344-0.0496216,0.0593414c-0.015564,0.0206909-0.0300751,0.0421829-0.0434265,0.0643539
				c-0.0134125,0.0222244-0.0256653,0.0451508-0.0366974,0.0686646c-0.0110016,0.0234222-0.0207214,0.0473251-0.0292816,0.071579
				c-0.0085754,0.0243073-0.016098,0.0493011-0.0220184,0.0749817c-0.0059204,0.0257187-0.0099487,0.0506592-0.0138397,0.07444
				c-0.000061,0.0003586,0.0000916,0.0006866,0.0000458,0.0010376s-0.0002899,0.000618-0.0003357,0.0009689
				c-0.0173492,0.1394806-0.0347595,0.2792816-0.0496674,0.4198303c-0.0148773,0.1402435-0.0272217,0.2808228-0.0376587,0.421524
				c-0.0104065,0.1402054-0.0189056,0.2806244-0.0255127,0.4211884c-0.0065765,0.1401062-0.0112915,0.2804413-0.0134583,0.4209366
				c-0.001358,0.0876236-0.0017242,0.1751556-0.0017242,0.2625732c0,0.0526581,0.0001373,0.105278,0.0002594,0.1578522l0,0
				l0.0002289,0.3417587c-0.0001221,0.0515594-0.0002441,0.1031723-0.0002441,0.1548233
				c0,0.0883942,0.0003662,0.1769104,0.0017395,0.2655334c0.002182,0.1404648,0.006897,0.2807922,0.013504,0.4208832
				c0.0066071,0.1405563,0.0151215,0.2809601,0.0255432,0.4211731c0.0104523,0.140686,0.0228119,0.2812576,0.0376892,0.4215012
				c0.0149231,0.1405029,0.0323334,0.2802734,0.0496979,0.4197235c0.0000458,0.0003586,0.0002899,0.0006332,0.0003357,0.0009918
				c0.0000458,0.000351-0.0001068,0.0006714-0.0000458,0.00103c0.0039368,0.0240021,0.0080109,0.0491714,0.0140228,0.0751266
				c0.006012,0.0259628,0.0136566,0.051239,0.0223846,0.0757904c0.0087128,0.0245514,0.018631,0.0487289,0.0298615,0.0724106
				c0.0111084,0.023468,0.0234375,0.0463333,0.036911,0.0685043c0.013504,0.0222549,0.028183,0.0438232,0.0439453,0.0645828
				c0.0157776,0.0207977,0.0326538,0.0407791,0.0505219,0.059845c0.0163879,0.0174866,0.0338135,0.0339966,0.0518188,0.0498581
				c-0.0054016-0.0046234-0.0110626-0.008934-0.0163422-0.0137024
				C232.5267487,75.7873077,232.5123749,75.7732162,232.4985962,75.7585907z M232.5712891,75.8256683
				c0.0090027,0.0075684,0.0184631,0.0145416,0.0278168,0.0216904c-0.0043335-0.0032196-0.0089111-0.0061035-0.0131683-0.0094147
				C232.5809021,75.8340378,232.5762177,75.8297043,232.5712891,75.8256683z M235.6771698,75.8375931
				c-0.0041656,0.0032349-0.0086365,0.0060501-0.0128632,0.0092087c0.0091705-0.0070114,0.0184326-0.0138855,0.0272522-0.0213165
				C235.6867065,75.8294601,235.6821136,75.8337402,235.6771698,75.8375931z M236.0914917,73.6605911
				c-0.002182,0.139595-0.0068817,0.279213-0.0134888,0.4187546c-0.0066223,0.1398468-0.0151367,0.2795334-0.0255432,0.419014
				c-0.0104523,0.1400452-0.0227966,0.2798157-0.0376587,0.4190826c-0.0148926,0.1396561-0.0323639,0.2792206-0.0498657,0.4190903
				l-0.0036774,0.0244904l-0.0060425,0.0320129c-0.0026855,0.0127029-0.0056763,0.0253372-0.0089874,0.0378418
				c-0.0037079,0.0140305-0.0078278,0.0279694-0.0123444,0.0417862c-0.003952,0.0120544-0.0081787,0.0240021-0.0127411,0.0358353
				l-0.0122681,0.0300903l-0.0109863,0.024231c-0.0089264,0.0187759-0.018631,0.0372238-0.0290527,0.0552216
				c-0.0093842,0.0161819-0.0193481,0.0320053-0.029892,0.0474396c-0.0114594,0.0167923-0.0235901,0.0331268-0.0363617,0.0489578
				c-0.0119629,0.0148087-0.0244751,0.0291824-0.0375061,0.0430679c-0.0138855,0.0147781-0.0283508,0.0290146-0.0433655,0.0426483
				c-0.0052338,0.0047531-0.0108643,0.0090485-0.0162354,0.013649c0.0180511-0.0159454,0.0355225-0.0325394,0.0519409-0.0501251
				c0.0177307-0.018959,0.0344543-0.0388412,0.0501099-0.0595169c0.0157318-0.0207672,0.0303802-0.0423431,0.043869-0.0646057
				c0.0135193-0.022316,0.0258942-0.0453491,0.0370483-0.0689697c0.0110931-0.0235443,0.0209198-0.0475769,0.0295563-0.0719604
				c0.0086517-0.0244217,0.0162354-0.0495682,0.0222168-0.0753937c0.0059814-0.0258636,0.010025-0.0509338,0.0139465-0.074852
				c0.0000458-0.0003586-0.0000916-0.000679-0.0000458-0.00103c0.0000458-0.0003586,0.0002899-0.0006332,0.0003357-0.0009918
				c0.0173645-0.1394806,0.0347748-0.2792892,0.0496979-0.4198227c0.0148773-0.1402435,0.0272522-0.2808151,0.0376892-0.4215164
				c0.0104065-0.1402054,0.0189209-0.2806168,0.0255432-0.4211884c0.0065918-0.1400986,0.0113068-0.2804413,0.0134888-0.420929
				c0.0013733-0.0883255,0.0017395-0.1765366,0.0017395-0.2646332c0-0.0519791-0.0001373-0.1039047-0.0002594-0.1557999l0,0
				l-0.0003052-0.3404083c0-0.0001831-0.0001068-0.0003281-0.0001068-0.0005035
				c0-0.0001907,0.0000916-0.0003357,0.0000916-0.0005188c-0.0027924-0.1402817-0.0056-0.2807999-0.0102386-0.4214554
				c-0.0046387-0.1407547-0.0110931-0.2813568-0.0189209-0.4217682c-0.0078278-0.1405563-0.0170288-0.2809753-0.0275879-0.4212341
				c-0.0105743-0.1404648-0.022522-0.2808151-0.0362854-0.4209061c-0.0137939-0.1403656-0.0293579-0.2801819-0.0449066-0.4197617
				c-0.0000458-0.0003357-0.0002594-0.0005951-0.0002899-0.0009232c-0.0000458-0.0003433,0.0000916-0.0006409,0.0000458-0.0009842
				c-0.0035248-0.0237122-0.0071564-0.0485916-0.0127106-0.0743103c-0.0055542-0.0257263-0.0127563-0.0507813-0.0210266-0.0751419
				c-0.0082703-0.0243454-0.0177307-0.0483551-0.0285034-0.0718765c-0.0106659-0.0233154-0.0225525-0.046051-0.0355682-0.0681229
				c-0.0130615-0.0221252-0.0272827-0.043602-0.0425873-0.0642776c-0.0153351-0.0207214-0.0317688-0.0406418-0.0491943-0.0596542
				c-0.0163574-0.0178528-0.0337372-0.0347595-0.0517883-0.0509338c0.0057526,0.0050049,0.0117798,0.009697,0.0173645,0.0148773
				c0.0145874,0.0134888,0.0286407,0.0275726,0.0420837,0.0421829l0.0185699,0.0209198l0.01828,0.0222702l0.0180664,0.0238342
				l0.0176239,0.0253296l0.0143433,0.0223846l0.0143585,0.0243835l0.0142059,0.0263672l0.0138397,0.0284576l0.0121765,0.0279083
				c0.0049591,0.0120163,0.0095825,0.0241394,0.013916,0.0363998c0.0053253,0.0151291,0.0101624,0.030426,0.0144958,0.045845
				c0.0042877,0.0152283,0.0080872,0.0306396,0.0114441,0.0461807c0.0027466,0.0127716,0.0051575,0.0254822,0.0071259,0.0381241
				l0.0042877,0.0309677c0.0156403,0.1398315,0.0312805,0.2794266,0.0450592,0.4191055
				c0.0137634,0.1395187,0.0257263,0.2794189,0.0363159,0.4195328c0.0105591,0.1396255,0.0197601,0.2794189,0.0275726,0.4193573
				c0.0077972,0.1395416,0.0142517,0.2791672,0.0188599,0.4188232c0.0046234,0.1394272,0.0074158,0.2791824,0.0102234,0.4191513l0,0
				v0.0005112l0.0003204,0.3384705c0,0.0000153,0,0.0000229,0,0.0000381s0,0.0000305,0,0.0000458
				c0.0001068,0.0515518,0.0002441,0.1030655,0.0002441,0.1545258
				C236.0932465,73.4841309,236.0928802,73.5724335,236.0914917,73.6605911z"
          />
          <path
            class="st1"
            d="M234.1312103,72.2855301c-0.4331207,0.0000458-0.7841339,0.351059-0.7841949,0.7841797
				c0.000061,0.4331207,0.3510742,0.7841415,0.7841949,0.7841873c0.4331207-0.0000458,0.7841339-0.3510666,0.7841797-0.7841873
				C234.9153442,72.6365891,234.5643311,72.2855759,234.1312103,72.2855301z M234.5789948,73.5174942
				c-0.1148376,0.1147232-0.2726746,0.1854477-0.4477844,0.1854782c-0.1751099-0.0000305-0.3329468-0.070755-0.4477844-0.1854782
				c-0.1147156-0.11483-0.1854401-0.2726669-0.1854858-0.4477844c0.0000458-0.1751099,0.0707703-0.3329468,0.1854858-0.4477844
				c0.1148376-0.1147156,0.2726746-0.1854401,0.4477844-0.1854782c0.1751099,0.0000381,0.3329468,0.0707626,0.4477844,0.1854782
				c0.1147156,0.1148376,0.1854401,0.2726746,0.1854858,0.4477844
				C234.7644348,73.2448273,234.6937103,73.4026642,234.5789948,73.5174942z"
          />
        </g>
      </g>
      <path class="st0" d="M230.8357544,40.831852c1.6304779,0,2.9644928,1.3500023,2.9644928,3v9.825901" />
      <linearGradient
        id="SVGID_11_"
        gradientUnits="userSpaceOnUse"
        x1="200.711792"
        y1="57.0447731"
        x2="219.2857819"
        y2="57.0447731"
      >
        <stop offset="0" style="stop-color:#EF4136" />
        <stop offset="1" style="stop-color:#BE1E2D" />
      </linearGradient>
      <path
        class="st28"
        d="M202.2065125,63.4707184c-0.6354065-1.2155571-0.9947205-2.5982628-0.9947205-4.0648956
		c0-4.8529243,3.9340668-8.7869987,8.7870026-8.7869987c4.8529205,0,8.7869873,3.9340744,8.7869873,8.7869987
		c0,1.4649887-0.3585052,2.846241-0.9925842,4.0608101"
      />
      <path
        class="actextrectangle"
        d="M242.4626617,61.0480919H225.639679c-1.0999908,0-2-0.8999977-2-2V55.62714
		c0-1.0999985,0.9000092-2,2-2h16.8229828c1.0999908,0,2,0.9000015,2,2v3.4209518
		C244.4626617,60.1480942,243.5626526,61.0480919,242.4626617,61.0480919z"
      />
      <g id="static-texts_3_">
        <text transform="matrix(1 0 0 1 205.6050568 61.9653549)" class="st2 st6 st13">kWh</text>
        <text transform="matrix(1 0 0 1 210.3058929 61.7741928)" class="st14 st6 st13">|</text>
      </g>
      <g id="dynamic-charts_6_">
        <path
          id="daily-consumption-chart_3_"
          ref="wallboxConsumptionChart"
          class="acchartcustom"
          d="M217.7931976,63.4665489
			c0.634079-1.2145691,0.9925842-2.5958214,0.9925842-4.0608063c0-4.8529282-3.9340668-8.7869987-8.7869873-8.7869987
			c-4.8529358,0-8.7870026,3.9340706-8.7870026,8.7869987c0,1.466629,0.359314,2.8493347,0.9947205,4.0648918"
        />
      </g>
      <g id="dynamic-texts_2_">
        <circle
          id="todays-consumption-percent_6_"
          ref="wallBoxTodayPercentCenter"
          class="st15"
          cx="213.2052612"
          cy="61.1101112"
          r="0.0802455"
        />
        <circle
          id="todays-consumption-big_3_"
          ref="wallBoxTodayCenter"
          class="st15"
          cx="210.3026123"
          cy="57.350296"
          r="0.0802455"
        />
        <circle
          id="household-center_1_"
          ref="wallBoxMainCenter"
          class="st15"
          cx="233.970932"
          cy="57.337616"
          r="0.0802455"
        />
        <circle
          id="total-consumption_1_"
          ref="wallBoxTodayTotalCenter"
          class="st15"
          cx="206.1284332"
          cy="68.7798233"
          r="0.0802455"
        />
      </g>
      <g id="arrows-in_6_" ref="wallboxArrowsIn">
        <polygon class="st29" points="233.8002472,45.8445549 235.2168121,42.7877617 232.3836975,42.7877617 		" />
        <polygon class="st29" points="233.8002472,49.4331093 235.2167969,46.3763161 232.3836823,46.3763161 		" />
        <polygon class="st29" points="233.8128815,53.0216637 235.2294464,49.9648743 232.3963318,49.9648743 		" />
      </g>
    </g>
  </svg>
</template>

<style lang="css" scoped>
svg {
  &.dark {
    text {
      fill: #ffffff;
    }
    .acchartcustom {
      stroke: oklch(0 0 0 / 0.75);
    }
    .actextrectangle {
      fill: oklch(0 0 0 / 0.25);
      stroke: oklch(0 0 0 / 0.35);
    }
  }
}
</style>

<style lang="css">
svg {
  .main-chart-text {
    fill: #000!important;
  }
  &.dark {
    .main-chart-text {
      fill: #fff!important;
    }
  }
  g:has(>.st29) {
    display: none;
    &.arrow-animate {
      display: unset;
    }
  }
}
</style>

<style lang="css" scoped>
.acchartcustom{fill:none;stroke:#D2D3D4;stroke-width:1.15;stroke-linecap:round;stroke-miterlimit:10;}
.actextrectangle{fill:#FFFFFF;stroke:#C6C6C5;stroke-width:0.25;stroke-miterlimit:10;}
.st0{fill:none;stroke:#C6C6C5;stroke-width:0.25;stroke-miterlimit:10;}
.st1{fill:#4580C2;}
.st2{fill:#384851;}
.st3{font-family:'Roboto';}
.st4{font-size:4px;}
.st5{opacity:0.5;}
.st6{font-family:'Roboto';font-weight:300;}
.st7{font-size:1.6950399px;}
.st8{font-size:1.6950001px;}
.st9{font-size:1.69504px;}
.st10{fill:none;stroke:url(#SVGID_1_);stroke-linecap:round;stroke-miterlimit:10;}
.st11{fill:none;stroke:url(#SVGID_2_);stroke-linecap:round;stroke-miterlimit:10;}
.st13{font-size:2.2114px;}
.st14{opacity:0.5;fill:#384851;}
.st15{fill:none;}
.st16{fill:none;stroke:url(#SVGID_3_);stroke-linecap:round;stroke-miterlimit:10;}
.st17{fill:none;stroke:url(#SVGID_4_);stroke-linecap:round;stroke-miterlimit:10;}
.st18{fill:#3D3D3C;}
.st19{fill:#939598;}
.st20{fill:none;stroke:url(#SVGID_5_);stroke-linecap:round;stroke-miterlimit:10;}
.st21{fill:none;stroke:url(#SVGID_6_);stroke-linecap:round;stroke-miterlimit:10;}
.st22{fill:none;stroke:url(#SVGID_7_);stroke-linecap:round;stroke-miterlimit:10;}
.st23{fill:none;stroke:url(#SVGID_8_);stroke-linecap:round;stroke-miterlimit:10;}
.st24{fill:none;stroke:url(#SVGID_9_);stroke-linecap:round;stroke-miterlimit:10;}
.st25{fill:none;stroke:url(#SVGID_10_);stroke-linecap:round;stroke-miterlimit:10;}
.st26{fill:#4280C2;}
.st27{fill:none;stroke:#D1D3D4;stroke-linecap:round;stroke-miterlimit:10;}
.st28{fill:none;stroke:url(#SVGID_11_);stroke-linecap:round;stroke-miterlimit:10;}
.st29{fill:#C6C6C5;}
</style>
