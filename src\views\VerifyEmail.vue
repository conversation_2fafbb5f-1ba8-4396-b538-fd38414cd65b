<script lang="ts" setup>
import { useRouter } from 'vue-router';
import Mail<PERSON>SON from '@/assets/lottie/waiting-mail.json';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo-w-text.svg?skipsvgo';
import { routeMap } from '@/router/routes';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { useAuthStore } from '@/stores/auth-store';

const router = useRouter();
const authStore = useAuthStore();

const onLogout = () => {
  authStore.logout();
  router.replace({ name: routeMap.home.children.dashboardInstallations.name });
};

const interval = setInterval(async() => {
  await authStore.fetchUser();
  if (!authStore.isUnverified) {
    clearInterval(interval);
    await router.replace({ name: routeMap.home.children.dashboardInstallations.name });
  }
}, 3000);

</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2 bg-prim-col-1 lg:bg-transparent">
      <div class="hidden bg-prim-col-2 lg:flex items-center justify-center">
        <SolarCloudLogo class="h-52 [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div class="flex flex-col items-center justify-center py-12 bg-prim-col-1">
        <div class="relative h-44 w-44">
          <LottieAnimation
            :animation-data="MailJSON"
            :height="`100%`"
            :width="`100%`"
          />
        </div>
        <div class="px-4 text-center text-muted-foreground">
          Pred vstupom do webovej aplikácie musíte potvrdiť e-mailovú adresu.
        </div>
        <ShadCnButton
          type="submit"
          variant="default"
          size="icon"
          class="mt-4 rounded-lg w-fit h-fit px-3 py-2 dark:text-white text-black"
          @click="onLogout"
        >
          {{ $t('login.log-out' ) }}
        </ShadCnButton>
      </div>
    </div>
  </div>
</template>
