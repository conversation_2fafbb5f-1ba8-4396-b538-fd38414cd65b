<script lang="ts" setup>
import { PopoverClose } from 'radix-vue';
import { reactive } from 'vue';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';

interface Props {
  max?: string;
  min?: string;
  initialInterval?: {
    dateStart: string,
    dateEnd: string,
  }
}

const props = defineProps<Props>();
const emit = defineEmits<{
  intervalChanged: [dates: Record<'dateStart' | 'dateEnd', string>] // named tuple syntax
}>();

const localDateInputs = reactive({
  start: props.initialInterval?.dateStart ?? '',
  end: props.initialInterval?.dateEnd ?? '',
});

const onSubmit = () => {
  emit('intervalChanged', { dateStart: localDateInputs.start, dateEnd: localDateInputs.end });
};

</script>

<template>
  <div class="w-fit">
    <h3 class="font-bold mb-1.5 text-black dark:text-white">
      {{ $t('calendar.pick-interval') }}
    </h3>
    <form
      class="dark:text-white text-black"
      @submit.prevent="onSubmit"
    >
      <div class="flex items-center gap-2">
        <div>
          <div class="text-xs">
            Od
          </div>
          <input
            v-model="localDateInputs.start"
            type="date"
            :min="min"
            :max="max"
            class="w-full h-9 chart-date-input bg-prim-col-1/60 hover:bg-prim-col-1/80 px-2 rounded-md cursor-pointer outline-hidden"
            required
          >
        </div>
        <div class="relative top-2">
          -
        </div>
        <div>
          <div class="text-xs">
            Do
          </div>
          <input
            v-model="localDateInputs.end"
            type="date"
            :min="min"
            :max="max"
            class="w-full h-9 chart-date-input bg-prim-col-1/60 hover:bg-prim-col-1/80 px-2 rounded-md cursor-pointer outline-hidden"
            required
          >
        </div>
      </div>
      <PopoverClose>
        <ShadCnButton
          type="submit"
          variant="default"
          size="icon"
          class="rounded-lg w-fit h-fit px-2 py-1 mt-2 dark:text-white text-black"
        >
          {{ $t('misc.save' ) }}
        </ShadCnButton>
      </PopoverClose>
    </form>
  </div>
</template>
