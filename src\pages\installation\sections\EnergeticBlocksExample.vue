<script setup lang="ts">
import MiniChart from '@/components/charts/MiniChart.vue';
import { routeMap } from '@/router/routes.ts';
import { type AnyDevice, type DeviceMetric, DeviceTypes } from '@/util/types/devices/device-types.ts';
import { deviceTypeTitleMap, deviceValueMap, installationBlocks } from '@/util/types/devices/device-detail.ts';
import type { InstallationDetailData } from '../types/installation-types';

interface Props {
  installation: InstallationDetailData,
  installationDetail: InstallationDetailData,
}

defineProps<Props>();

const apiData: AnyDevice[] = [
  { id: '1', model: 'GW10K-ET', type: DeviceTypes.INVERTER, vendor: 'Goodwe', currentPower: { value: 5140, unit: 'W' }, dailyPower: { value: 23, unit: 'kW' }, totalPower: { value: 5420, unit: 'kWh' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '2', model: 'Longi Hi-MO6 LR5-54HTH-425M', type: DeviceTypes.PANEL, vendor: 'Goodwe', peakOutput: { value: 10.2, unit: 'kWp' }, count: { value: 24, unit: 'ks' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '3', model: 'Tigo CCA kit', type: DeviceTypes.DATA_LOGGER, vendor: 'Tigo', currentPower: { value: 5135, unit: 'W' }, tpa: { value: 1, unit: 'ks' }, ts04: { value: 24, unit: 'ks' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '4', model: 'Wall Plug', type: DeviceTypes.WALL_PLUG, vendor: 'Antik', currentConsumption: { value: 120, unit: 'W' }, todayConsumption: { value: 1.4, unit: 'kWh' }, totalConsumption: { value: 56, unit: 'kWh' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '5', model: 'Temp Sensor', type: DeviceTypes.THERMOMETER, vendor: 'Antik', temperature: { value: 24, unit: '°C' }, humidity: { value: 50, unit: '%' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '6', model: 'Temp Sensor', type: DeviceTypes.THERMOMETER, vendor: 'Antik', temperature: { value: 22, unit: '°C' }, humidity: { value: 53, unit: '%' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
  { id: '7', model: '-', type: DeviceTypes.AC, vendor: 'Daikin', currentConsumption: { value: 0, unit: 'W' }, todayConsumption: { value: 0, unit: 'kWh' }, totalConsumption: { value: 143, unit: 'kWh' }, lastUpdated: '23.1.2024 10:01' } as AnyDevice,
];

const groupedDevices = installationBlocks.map(block => ({
  title: block.title,
  subtitle: block.subtitle,
  devices: apiData.filter(device => block.deviceTypes.includes(device.type)),
}));
</script>

<template>
  <div class="w-full mt-4">
    <h2 class="font-bold text-2xl">
      Bloky inštalácie
    </h2>
    <div class="w-full grid gap-4 mt-4">
      <div v-for="block in groupedDevices" :key="block.title" class="rounded-xl bg-prim-col-foreground-1 dark:bg-prim-col-foreground-1/70">
        <div class="flex flex-row items-center justify-between p-6">
          <div>
            <h3 class="text-2xl font-semibold leading-none tracking-tight">
              {{ block.title }}
            </h3>
            <p class="text-sm text-muted-foreground">
              {{ block.subtitle }}
            </p>
          </div>
        </div>

        <div class="px-4 pb-4 grid gap-2">
          <router-link
            v-for="device in block.devices"
            :key="device.id"
            :to="{ name: routeMap.home.children.deviceDetail.name, params: { installationId: installation.id, deviceId: device.id } }"
            class="flex items-center gap-4 p-4 dark:bg-prim-col-foreground-1/90 dark:hover:bg-prim-col-foreground-2/70 bg-prim-col-foreground-2/30 hover:bg-prim-col-foreground-2/50 rounded-lg transition-colors duration-150"
          >
            <div class="flex items-center gap-2 w-full flex-wrap">
              <div class="flex flex-col gap-1 xl:w-52 xl:max-w-52 flex-[1_1_100%] xl:flex-none">
                <div>
                  <div class="text-xs bg-prim-col-foreground-contrast dark:text-prim-col-1 text-black w-fit px-1 py-0.5 rounded-md mb-1.5">
                    {{ deviceTypeTitleMap[device.type] }}
                  </div>
                  <div class="text-xs">
                    {{ device.vendor }}
                  </div>
                  <div class="font-medium line-clamp-1 break-all">
                    {{ device.model }}
                  </div>
                </div>
                <div class="flex items-center gap-1">
                  <div class="w-2 h-2 rounded-full bg-green-500" />
                  <div class="text-xs">
                    Zariadenie je online
                  </div>
                </div>
              </div>

              <div v-for="deviceValue in deviceValueMap[device.type]" :key="deviceValue.key" class="flex flex-col flex-[1_1_100%] sm:flex-none sm:w-fit sm:min-w-40">
                <div class="dark:bg-white/15 bg-black/15 rounded-t-md px-3 py-1.5">
                  <h3 class="font-light text-xs">
                    {{ deviceValue.title }}
                  </h3>
                </div>
                <div class="dark:bg-white/10 bg-black/10 px-2 py-3 rounded-b-md">
                  <div class="font-medium dark:text-white text-black">
                    {{ (device[deviceValue.key as keyof AnyDevice] as unknown as DeviceMetric)?.value }} {{ (device[deviceValue.key as keyof AnyDevice] as unknown as DeviceMetric)?.unit ?? '' }}
                  </div>
                </div>
              </div>

              <div class="xl:ml-auto flex-[1_1_100%] sm:flex-none sm:w-40 h-[76px] block">
                <MiniChart />
              </div>

              <div class="flex flex-col flex-[1_1_100%] sm:flex-none">
                <div class="dark:bg-white/15 bg-black/15 rounded-t-md px-3 py-1.5">
                  <h3 class="font-light text-xs whitespace-nowrap">
                    Posledná aktualizácia
                  </h3>
                </div>
                <div class="dark:bg-white/10 bg-black/5 px-2 py-3 rounded-b-md">
                  <div class="font-medium dark:text-white text-black">
                    {{ device.lastUpdated ?? '-' }}
                  </div>
                </div>
              </div>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-muted-foreground {
  color: #6b7280;
}

.bg-card {
  background-color: white;
}

.text-card-foreground {
  color: #111827;
}
</style>
