<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { RefreshCw, PlusIcon } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shadcn-components/ui/card';
import AdminInstallationsTable from '@/components/admin/AdminInstallationsTable.vue';
import AdminInstallationsPagination from '@/components/admin/AdminInstallationsPagination.vue';
import AdminInstallationsSearchInput from '@/components/admin/AdminInstallationsSearchInput.vue';
import { routeMap } from '@/router/routes';
import type { InstallationServiceDetailData } from '@/pages/installation/types/installation-types';

interface Props {
  installations: InstallationServiceDetailData[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  perPage: number;
  paginationMeta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
  paginationLinks: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
}

defineProps<Props>();

const emit = defineEmits<{
  pageChange: [page: number];
  perPageChange: [perPage: number];
  search: [searchTerm: string];
  refresh: [];
}>();

const router = useRouter();
const searchTerm = ref('');

const handlePageChange = (page: number) => {
  emit('pageChange', page);
};

const handlePerPageChange = (perPage: number) => {
  emit('perPageChange', perPage);
};

const handleRefresh = () => {
  emit('refresh');
};

const handleSearch = (searchTerm: string) => {
  emit('search', searchTerm);
};

const handleCreateNew = () => {
  router.push({ name: routeMap.service.children.newInstallation.name });
};
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-col gap-4 md:gap-0 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          {{ $t('admin.installations.title') }}
        </h1>
        <p class="text-muted-foreground">
          {{ $t('admin.installations.description') }}
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="default"
          class="gap-2"
          @click="handleCreateNew"
        >
          <PlusIcon class="h-4 w-4" />
          {{ $t('admin.installations.create-new') }}
        </Button>
        <Button
          variant="outline"
          size="default"
          class="gap-2"
          @click="handleRefresh"
        >
          <RefreshCw class="h-4 w-4" />
          {{ $t('misc.refresh') }}
        </Button>
      </div>
    </div>

    <!-- Search Input -->
    <div class="flex flex-col sm:flex-row gap-4 sm:items-center sm:justify-between">
      <div class="w-full sm:max-w-sm">
        <AdminInstallationsSearchInput
          v-model="searchTerm"
          :placeholder="$t('admin.installations.search-placeholder')"
          @search="handleSearch"
        />
      </div>
    </div>

    <!-- Stat Card example -->
    <!-- <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">
            {{ $t('admin.installations.stats.total-installations') }}
          </CardTitle>
          <Building2 class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ totalItems }}
          </div>
          <p class="text-xs text-muted-foreground">
            {{ $t('admin.installations.stats.installations-description') }}
          </p>
        </CardContent>
      </Card> -->

    <section class="dark:bg-card bg-white rounded-xl">
      <AdminInstallationsTable :installations="installations" />
    </section>

    <!-- Pagination -->
    <AdminInstallationsPagination
      :current-page="currentPage"
      :total-pages="totalPages"
      :has-prev="!!paginationLinks.prev"
      :has-next="!!paginationLinks.next"
      :per-page="perPage"
      :total-items="totalItems"
      @page-change="handlePageChange"
      @per-page-change="handlePerPageChange"
    />
  </div>
</template>
