interface MetricValue {
  metric: string;
  value: number;
  unit: string;
}

type EnergyItem = {
  device_id: string;
  metrics: MetricValue[];
};

type EnergyAggregationResult = Record<string, { value: number; unit: string }>;

self.onmessage = ({ data }: MessageEvent<EnergyItem[]>) => {
  const totals: EnergyAggregationResult = {};

  for (const item of data) {
    for (const metric of item.metrics) {
      if (!metric.metric || typeof metric.value !== 'number') {
        continue;
      }

      if (!totals[metric.metric]) {
        totals[metric.metric] = {
          value: metric.value,
          unit: metric.unit,
        };
      } else {
        totals[metric.metric].value += metric.value;
      }
    }
  }

  self.postMessage(totals);
};

export {};
