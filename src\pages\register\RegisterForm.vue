<script setup lang="ts">
import { reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import PasswordHelper from '@/components/forms/PasswordHelper.vue';
import PageLoader from '@/components/global/PageLoader.vue';
import GreenCheck from '@/components/states/GreenCheck.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { InputPassword } from '@/shadcn-components/ui/input-password';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import { executeWebkitMessage } from '@/util/facades/webkit-app.ts';

enum RegisterFormState {
  IDLE,
  SENT,
  ERROR,
}

const { t } = useI18n();
const componentState = reactive({
  global: ComponentStateType.OK as ComponentStateType,
  registration: RegisterFormState.IDLE,
});

const credentials = reactive({
  name: '',
  surname: '',
  phone: '',
  email: '',
  password: '',
  repeatedPassword: '',
});

const onRegisterRequest = async() => {
  componentState.global = ComponentStateType.LOADING;
  try {
    await customAxios.post('/register', {
      name: credentials.name,
      surname: credentials.surname,
      phone_number: credentials.phone,
      email: credentials.email,
      password: credentials.password,
      password_confirmation: credentials.repeatedPassword,
    });
    componentState.registration = RegisterFormState.SENT;
  } catch (e: any) {
    if (e.response?.status === 422 && e.response.data?.message && e.response.data.message === 'validation.unique') {
      deployToast(ToastType.ERROR, {
        text: t('register.mail-already-used'),
        timeout: 6000,
      });
      return;
    }
    deployToast(ToastType.ERROR, {
      text: t('register.failed-to-register'),
      timeout: 6000,
    });
  } finally {
    componentState.global = ComponentStateType.OK;
  }
};

const onAppRegisterSuccess = () => {
  executeWebkitMessage('onRegisterSuccess');
};

</script>

<template>
  <div class="mx-auto w-[18rem] lw:w-[22rem]">
    <div
      v-if="componentState.global === ComponentStateType.OK"
      class="w-full grid gap-6"
    >
      <div
        v-if="[RegisterFormState.IDLE, RegisterFormState.ERROR].includes(componentState.registration)"
        class="grid gap-2 text-center"
      >
        <h1 class="text-3xl font-bold">
          {{ $t('register.account-create-title') }}
        </h1>
        <p class="text-balance text-muted-foreground">
          {{ $t('register.account-create-desc') }}
        </p>
      </div>
      <form
        v-if="[RegisterFormState.IDLE, RegisterFormState.ERROR].includes(componentState.registration)"
        autocomplete="off"
        class="grid gap-4"
        @submit.prevent="onRegisterRequest"
      >
        <div class="grid gap-2">
          <Label
            for="new-user-name"
            class=""
          >
            {{ $t('misc.name') }}
          </Label>
          <Input
            id="new-user-name"
            v-model="credentials.name"
            name="name"
            autocomplete="off"
            type="text"
            required
          />
        </div>
        <div class="grid gap-2">
          <Label
            for="new-user-surname"
            class=""
          >
            {{ $t('misc.surname') }}
          </Label>
          <Input
            id="new-user-surname"
            v-model="credentials.surname"
            name="sur"
            autocomplete="off"
            type="text"
            required
          />
        </div>
        <div class="grid gap-2">
          <Label
            for="new-user-phone"
            class=""
          >
            {{ $t('misc.phone') }}
          </Label>
          <Input
            id="new-user-phone"
            v-model="credentials.phone"
            name="phone"
            autocomplete="off"
            type="tel"
            required
          />
        </div>
        <div class="grid gap-2">
          <Label
            for="new-user-mail"
            class=""
          >
            {{ $t('misc.mail') }}
          </Label>
          <Input
            id="new-user-mail"
            v-model="credentials.email"
            name="email"
            autocomplete="email"
            type="email"
            required
          />
        </div>
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="password">{{ $t('misc.password') }}</Label>
          </div>
          <InputPassword
            id="password"
            v-model="credentials.password"
            type="password"
            name="password"
            autocomplete="off"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="confirm-password">{{ $t('register.confirm-password') }}</Label>
          </div>
          <InputPassword
            id="confirm-password"
            v-model="credentials.repeatedPassword"
            name="confirm-password"
            autocomplete="off"
            type="password"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <PasswordHelper :credentials="{newPassword: credentials.password, repeatedNewPassword: credentials.repeatedPassword}" />
        <Button
          type="submit"
          class="w-full"
        >
          {{ $t('register.create-account') }}
        </Button>
      </form>
      <div
        v-else
        class="grid justify-items-center gap-4"
      >
        <GreenCheck class="w-16 h-16" />
        <div class="font-bold text-center">
          <div>{{ $t('register.success-register') }}!</div>
          <div class="mt-2 text-sm text-prim-col-foreground-contrast font-normal">
            {{ $t('register.success-register-desc') }}.
          </div>
        </div>
        <component
          :is="isMobileSubdomain ? 'div' : 'router-link'"
          :replace="true"
          :to="{name: routeMap.home.children.dashboardInstallations.name}"
          class="block"
          @click="isMobileSubdomain ? onAppRegisterSuccess() : undefined"
        >
          <Button
            type="submit"
            class="w-fit"
          >
            {{ $t('misc.back-to-home') }}
          </Button>
        </component>
      </div>
    </div>
    <div v-else-if="componentState.global === ComponentStateType.LOADING">
      <PageLoader :flex-center="true" />
    </div>
  </div>
</template>
