<script lang="ts" setup>
import { Share, Pencil } from 'lucide-vue-next';
import { computed, reactive } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import DashboardDivider from '@/pages/dashboard/DashboardDivider.vue';
import ShareInstallationDialog from '@/pages/dashboard/ShareInstallationDialog.vue';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { TooltipProvider } from '@/shadcn-components/ui/tooltip';
import { type InverterCurrentDataTransformed } from '@/util/types/api-responses';
import CreateOrEditInstallationDialog from '@/pages/dashboard/CreateOrEditInstallationDialog.vue';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import type { InstallationDetailData } from '../types/installation-types';
import Tooltip from '@/shadcn-components/ui/tooltip/Tooltip.vue';
import TooltipTrigger from '@/shadcn-components/ui/tooltip/TooltipTrigger.vue';
import TooltipContent from '@/shadcn-components/ui/tooltip/TooltipContent.vue';
import { getInstallationInverterType, getInstallationStatus } from '../helpers/inverter-helper';
import { useAuthStore } from '@/stores/auth-store';

interface Props {
  installation: InstallationDetailData,
  inverterCurrentData: InverterCurrentDataTransformed,
}

const props = defineProps<Props>();
const emit = defineEmits(['installationUpdated']);
const authStore = useAuthStore();

const modals = reactive({
  shareInstallation: {
    isOpened: false,
  },
  editInstallation: {
    isOpened: false,
  },
});

const pairedDevices = computed(() => props.installation.deviceInstances);
const inverterType = computed(() => getInstallationInverterType({ installationDetail: props.installation }));
</script>

<template>
  <div class="flex flex-wrap gap-2 items-center justify-start bg-prim-col-foreground-1 p-4 relative select-none rounded-xl">
    <div class="flex items-center gap-2">
      <div>
        <div class="font-bold">
          {{ installation.title }}
        </div>
        <div
          v-if="installation.is_owner"
          class="text-xs"
        >
          {{ $t('misc.title') }}
        </div>
        <div v-else-if="authStore.userHelpers.canServiceInstallation" class="text-xs bg-yellow-500 text-black w-fit px-1 py-0.5 rounded-md">
          Servis
        </div>
        <div
          v-else
          class="text-xs bg-yellow-500 text-black w-fit px-1 py-0.5 rounded-md"
        >
          {{ $t('installation.shared-installation') }}
        </div>
      </div>
    </div>
    <DashboardDivider />
    <div class="flex items-center gap-2">
      <div>
        <div
          v-if="inverterCurrentData && installation && inverterType !== null"
          class="text-sm font-bold"
        >
          {{ getInstallationStatus({inverterType: inverterType, inverterCurrentData}) }}
        </div>
        <div v-else>
          -
        </div>
        <div class="text-xs">
          Status
        </div>
      </div>
    </div>
    <DashboardDivider />
    <div class="flex items-center gap-2">
      <div>
        <div class="font-bold">
          {{ pairedDevices.length }}
        </div>
        <div class="text-xs">
          {{ $t('installation.paired-devices-counted', pairedDevices.length) }}
        </div>
      </div>
    </div>
    <div class="flex-1 md:flex-none md:ml-auto md:mr-0 flex items-center gap-2">
      <TooltipProvider v-if="!isMobileSubdomain && installation.is_owner" :delay-duration="0">
        <Tooltip>
          <TooltipTrigger
            as-child
            as="button"
          >
            <div>
              <ShadCnButton
                type="submit"
                variant="default"
                :disabled="true"
                size="icon"
                class="px-1 text-xs sm:text-sm rounded-[0.5rem] w-fit h-7 sm:h-8 sm:px-2 dark:text-white text-black font-medium cursor-default"
              >
                {{ $t('installation.add-device' ) }}
              </ShadCnButton>
            </div>
          </TooltipTrigger>
          <TooltipContent side="left">
            {{ $t('installation.add-device-tooltip-2') }}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <ShadCnButton
        v-if="installation.is_owner"
        type="submit"
        variant="default"
        size="icon"
        class="px-0! py-0! text-xs sm:text-sm rounded-[0.5rem] flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 sm:px-2 dark:text-white text-black"
        :title="$t('installation.share-installation')"
        @click="modals.shareInstallation.isOpened = true;"
      >
        <div class="w-[1.1rem]">
          <Share class="w-full h-full" />
        </div>
      </ShadCnButton>
      <ShadCnButton
        v-if="installation.is_owner || authStore.userHelpers.canServiceInstallation"
        type="submit"
        variant="default"
        size="icon"
        class="px-0! py-0! text-xs sm:text-sm rounded-[0.5rem] flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 sm:px-2 dark:text-white text-black"
        :title="$t('installation.edit-installation')"
        @click="modals.editInstallation.isOpened = true;"
      >
        <div class="w-[1.1rem]">
          <Pencil class="w-full h-full" />
        </div>
      </ShadCnButton>
    </div>
    <suspense v-if="modals.shareInstallation.isOpened">
      <ShareInstallationDialog
        v-model="modals.shareInstallation.isOpened"
        :installation="installation"
      />
      <template #fallback>
        <teleport to="body">
          <div class="fixed w-screen h-dvh bg-black/60 z-50 top-0 left-0">
            <PageLoader :absolute-center="true" />
          </div>
        </teleport>
      </template>
    </suspense>
    <suspense v-if="modals.editInstallation.isOpened">
      <CreateOrEditInstallationDialog
        v-model="modals.editInstallation.isOpened"
        :installation-id="installation?.id"
        :is-edit-dialog="true"
        @installation-updated="emit('installationUpdated')"
      />
      <template #fallback>
        <teleport to="body">
          <div class="fixed w-screen h-dvh bg-black/60 z-50 top-0 left-0">
            <PageLoader :absolute-center="true" />
          </div>
        </teleport>
      </template>
    </suspense>
  </div>
</template>
