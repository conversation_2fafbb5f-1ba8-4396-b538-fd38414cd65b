export enum DeviceTypes {
  INVERTER = 'inverter',
  DONGLE = 'dongle',
  WALL_PLUG = 'wall_plug',
  THERMOMETER = 'thermometer',
  PANEL = 'solar_panel',
  AC = 'ac',
  DATA_LOGGER = 'data_logger',
  AC_WATER_HEATER = 'AC_Water_Heater',
  DC_WATER_HEATER = 'DC_Water_Heater',
}

export type DeviceGeneric<T = Record<string, {value: string | number, unit: string|undefined}>> = {
  id: string;
  name?: string;
  type: DeviceTypes;
  vendor: string;
  model: string;
  lastUpdated?: string;
  online?: boolean;
} & T;

export type DeviceMetric = {value: string | number, unit: string|undefined};

export type InverterDevice = DeviceGeneric<{
  currentPower: DeviceMetric;
  dailyPower: DeviceMetric;
  totalPower: DeviceMetric;
}>;

export type PanelDevice = DeviceGeneric<{
  count: DeviceMetric;
  peakOutput: DeviceMetric;
}>;

export type ACDevice = DeviceGeneric<{
  currentConsumption: DeviceMetric;
  todayConsumption: DeviceMetric;
  totalConsumption: DeviceMetric;
}>;

export type DataLoggerDevice = DeviceGeneric<{
  currentPower: DeviceMetric;
  tpa: DeviceMetric;
  ts04: DeviceMetric;
}>;

export type WallPlugDevice = DeviceGeneric<{
  currentConsumption: DeviceMetric;
  todayConsumption: DeviceMetric;
  totalConsumption: DeviceMetric;
}>;

export type ThermometerDevice = DeviceGeneric<{
  temperature: DeviceMetric;
  humidity: DeviceMetric;
}>;

export type AnyDevice =
  | InverterDevice
  | PanelDevice
  | ACDevice
  | DataLoggerDevice
  | ThermometerDevice
  | WallPlugDevice;

export type AnyDeviceKeys =
  & InverterDevice
  & PanelDevice
  & ACDevice
  & DataLoggerDevice
  & ThermometerDevice
  & WallPlugDevice;
