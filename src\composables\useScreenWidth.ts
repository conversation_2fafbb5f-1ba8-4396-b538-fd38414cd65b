import { ref, onMounted, onUnmounted, computed } from 'vue';

export function useScreenWidth() {
  const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 0);

  function handleResize() {
    windowWidth.value = window.innerWidth;
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  const screenWidth = computed(() => windowWidth.value);

  return { screenWidth };
}
