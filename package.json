{"name": "antik-solar-cloud", "private": true, "version": "0.0.1-beta", "type": "module", "scripts": {"dev": "vite --port 8383", "build": "vue-tsc && vite build", "preview": "vite preview", "lint-gitlab": "oxlint . --fix -D correctness --ignore-path .gitignore && pnpm type-check", "lint": "eslint . --fix && pnpm type-check", "oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@antik-web/axios-auth-refresh-response-data": "^4.0.0", "@babel/runtime": "^7.26.9", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@intlify/unplugin-vue-i18n": "^6.0.3", "@mdi/font": "^7.4.47", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/vite": "^4.0.12", "@unhead/vue": "^2.0.0-rc.10", "@vueuse/core": "^13.0.0", "apexcharts": "^4.5.0", "axios": "^1.8.2", "bezier-easing": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "echarts": "^5.6.0", "eslint": "^9.22.0", "globals": "^16.0.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.479.0", "luxon": "^3.5.0", "open-props": "^1.7.13", "pinia": "^3.0.1", "radix-vue": "^1.9.17", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vite-plugin-vuetify": "^2.1.0", "vue": "^3.5.13", "vue-api-query": "^1.11.0", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.2", "vue-router": "^4.5.0", "vue-toastification": "2.0.0-rc.5", "vue3-apexcharts": "^1.8.0", "vue3-lottie": "^3.3.1", "vuetify": "^3.7.15"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/container-queries": "^0.1.1", "@tsconfig/node22": "^22.0.0", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.4.2", "@types/node": "^22.13.10", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint-import-resolver-typescript": "^3.8.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-newlines": "^1.4.0", "eslint-plugin-oxlint": "^0.15.14", "eslint-plugin-vue": "^10.0.0", "jiti": "^2.4.2", "oxlint": "^0.15.14", "sass": "^1.85.1", "tailwindcss": "^4.0.12", "typescript": "^5.8.2", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.12.4"}