import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { createHead } from '@unhead/vue/client';
import { createApp, defineAsyncComponent } from 'vue';
import Toast from 'vue-toastification';
// eslint-disable-next-line import/no-named-as-default
import Vue3Lottie from 'vue3-lottie';
import { loadLayoutMiddleware } from '@/router/middleware/layout-middleware';
import icons from '@/setup/import/icons';
import { TokenService, useAuthStore } from '@/stores/auth-store';
import type { CustomWindow } from '@/../typings/window';
import type { PluginOptions } from 'vue-toastification';
import '@/assets/styles.css';
import '@/assets/css/anims.css';

icons.addIconsToLibrary();
(window as CustomWindow).evB = new EventTarget();

const options: PluginOptions = {
  transition: 'Vue-Toastification__custom',
  maxToasts: 7,
  newestOnTop: true,
};

const isLightTheme = localStorage.getItem('isLight');
if (!isLightTheme) {
  document.body.classList.add('dark');
}

const head = createHead();

const createAndMountApp = async() => {
  import('@/assets/css/vuetify/styles/elements/_index.sass');
  import('@/assets/css/vuetify/styles/generic/_index.scss');
  import('@/assets/css/vuetify/styles/settings/_index.sass');
  import('@/assets/css/vuetify/styles/tools/_index.sass');
  import('@/assets/css/vuetify/styles/_normalize.scss');
  import('@mdi/font/css/materialdesignicons.css');
  import('@/assets/css/vue-toastification/_toastification.scss');
  import('@/assets/css/colors.css');
  await import('@/App.vue');
  const App = defineAsyncComponent(() => import('@/App.vue'));
  const piniaStore = await import('@/pinia');
  const app = createApp(App).use(piniaStore.default);
  if (TokenService.getAccessToken()) {
    await useAuthStore()
      .fetchUser();
  }
  const router = await import('./router');
  router.default.beforeEach((to, from, next) => {
    loadLayoutMiddleware(to, from, next, app);
  });
  const i18n = await import('@/i18n');
  const vuetify = await import('@/vuetify');
  app.use(router.default)
    .component('font-awesome-icon', FontAwesomeIcon)
    .use(i18n.default)
    .use(Toast, options)
    .use(vuetify.default)
    .provide('i18n_instance', i18n.default)
    .use(Vue3Lottie, { name: 'LottieAnimation' })
    .use(head);

  app.mount('#app');
  import('@/assets/styles.css');
};

createAndMountApp()
  .catch(() => console.error('Failed to init vue app, check backend server.'));
