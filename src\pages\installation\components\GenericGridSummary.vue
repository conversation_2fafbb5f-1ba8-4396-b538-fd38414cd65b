<script lang="ts" setup>
import Bulb<PERSON><PERSON><PERSON> from '@/assets/lottie/bulb.json';
import EnergyJSON from '@/assets/lottie/energy-solar.json';
import UpJSON from '@/assets/lottie/up.json';

interface GridData {
  producedEnergy: {
    value: number;
    unit: string;
  } | undefined;
  energyFromNetwork: {
    value: number;
    unit: string;
  } | undefined;
  energyToNetwork: {
    value: number;
    unit: string;
  } | undefined;
  houseConsumption: {
    value: number;
    unit: string;
  } | undefined;
}

interface Props {
  gridData: GridData;
  animationSpeed?: number;
}

withDefaults(defineProps<Props>(), {
  animationSpeed: 1,
});
</script>

<template>
  <div class="flex flex-col p-1 h-full">
    <div class="grid grid-cols-4 sm:grid-cols-4 place-items-center flex-1 gap-1 [&>div]:min-h-48 min-h-48 sm:[&>div]:min-h-48 sm:min-h-48">
      <!-- Vyrobená energia -->
      <div class="relative rounded-xl h-fit w-full gap-1 flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.produced-energy') }}
        </div>
        <div class="relative mt-4 h-12 w-12 sm:h-24 sm:w-24 [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white">
          <LottieAnimation
            :animation-data="EnergyJSON"
            :height="`100%`"
            :width="`100%`"
            :speed="gridData.producedEnergy && gridData.producedEnergy.value > 0 ? animationSpeed : 0"
          />
        </div>
        <div v-if="gridData.producedEnergy !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(gridData.producedEnergy.value * 1000) / 1000 }} <br class="sm:hidden">{{ gridData.producedEnergy.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>

      <!-- Energia prijatá zo siete -->
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.energy-from-network') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white rotate-180">
          <LottieAnimation
            :animation-data="UpJSON"
            :height="`100%`"
            :width="`100%`"
            :loop="false"
          />
        </div>
        <div v-if="gridData.energyFromNetwork !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(gridData.energyFromNetwork.value * 1000) / 1000 }} <br class="sm:hidden">{{ gridData.energyFromNetwork.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>

      <!-- Energia predaná do siete -->
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.energy-to-network') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white">
          <LottieAnimation
            :animation-data="UpJSON"
            :height="`100%`"
            :width="`100%`"
            :loop="false"
          />
        </div>
        <div v-if="gridData.energyToNetwork !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(gridData.energyToNetwork.value * 1000) / 1000 }} <br class="sm:hidden">{{ gridData.energyToNetwork.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>

      <!-- Spotreba domácnosti -->
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.house-consumption') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white">
          <LottieAnimation
            :animation-data="BulbJSON"
            :height="`100%`"
            :width="`100%`"
            :loop="false"
          />
        </div>
        <div v-if="gridData.houseConsumption !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(gridData.houseConsumption.value * 1000) / 1000 }} <br class="sm:hidden">{{ gridData.houseConsumption.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>
    </div>
  </div>
</template>
