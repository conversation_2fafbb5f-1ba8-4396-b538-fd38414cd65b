<!-- WebviewShadow.vue -->
<template>
  <!-- This host element gets a shadow root attached at runtime -->
  <div ref="host" />
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

type Mode = 'open' | 'closed';

const props = defineProps<{
  html: string;              // raw HTML to render
  css?: string;              // optional CSS for the shadow root
  mode?: Mode;               // 'open' | 'closed' (default 'closed')
  delegatesFocus?: boolean;  // optional
}>();

const host = ref<HTMLElement | null>(null);

let shadow: ShadowRoot | null = null;
let sheet: CSSStyleSheet | null = null; // for constructable stylesheets

function applyCss() {
  if (!shadow) {return;}

  // Prefer constructable stylesheets where supported
  const supportsConstructable =
    'adoptedStyleSheets' in Document.prototype &&
    'replaceSync' in CSSStyleSheet.prototype;

  if (props.css && supportsConstructable) {
    if (!sheet) {sheet = new CSSStyleSheet();}
    sheet.replaceSync(props.css);
    // Keep any existing sheets (if you later append more)
    const current = (shadow as any).adoptedStyleSheets ?? [];
    const withoutThis = current.filter((s: CSSStyleSheet) => s !== sheet);
    (shadow as any).adoptedStyleSheets = [...withoutThis, sheet];
  } else if (props.css) {
    // Fallback: <style> element inside the shadow root
    // Remove any old <style data-shadow-style>
    shadow.querySelectorAll('style[data-shadow-style]').forEach(n => n.remove());
    const style = document.createElement('style');
    style.setAttribute('data-shadow-style', '');
    style.textContent = props.css;
    shadow.prepend(style);
  }
}

function renderHtml() {
  if (!shadow) {return;}

  // Clear previous content except our <style> fallback (if present)
  [...shadow.childNodes].forEach(node => {
    if (!(node.nodeName === 'STYLE' && (node as Element).getAttribute?.('data-shadow-style') !== null)) {
      shadow!.removeChild(node);
    }
  });

  // Insert new HTML
  // NOTE: If this HTML can be user-supplied, consider sanitizing it (e.g., DOMPurify) before setting innerHTML.
  const wrapper = document.createElement('div');
  wrapper.innerHTML = props.html ?? '';
  shadow.appendChild(wrapper);
}

onMounted(() => {
  if (!host.value) {return;}

  shadow = host.value.attachShadow({
    mode: props.mode ?? 'closed',
    delegatesFocus: props.delegatesFocus ?? false,
  });

  applyCss();
  renderHtml();
});

// Update when props change
watch(() => props.css, () => applyCss());
watch(() => props.html, () => renderHtml());

onBeforeUnmount(() => {
  shadow = null;
  sheet = null;
});
</script>
