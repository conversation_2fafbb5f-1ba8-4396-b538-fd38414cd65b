export interface ConfigurationOption {
  label: string;
  value: string | number | boolean;
}

export type BackendFormFieldType =
  | 'select'
  | 'text'
  | 'number'
  | 'switch'
  | 'checkbox'
  | 'button'
  | 'radio';

export interface BackendFormField {
  key: string;
  label: string;
  type: BackendFormFieldType;
  options?: ConfigurationOption[];
  required?: boolean;
  default?: string | number | boolean;
  help?: string;
  value?: string;
}

export type BackendFormSchema = BackendFormField[];

export interface LaravelValidationError {
  message: string;
  errors: Record<string, string[]>;
}

export interface FormErrors {
  [fieldKey: string]: string[];
}
