<script lang="ts" setup>
import { useDebounce } from '@vueuse/core';
import { X, Search } from 'lucide-vue-next';
import { computed, watch } from 'vue';
import { cn } from '@/shadcn-utils';
import type { HTMLAttributes } from 'vue';

interface Props {
  placeholder?: string;
  class?: HTMLAttributes['class'];
  debounceMs?: number;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  class: undefined,
  debounceMs: 300,
});

const emit = defineEmits<{
  'search': [value: string];
}>();

const modelValue = defineModel<string>({ default: '' });

const debouncedValue = useDebounce(modelValue, props.debounceMs);

const hasValue = computed(() => modelValue.value.length > 0);

const clearSearch = () => {
  modelValue.value = '';
};

watch(debouncedValue, newValue => {
  emit('search', newValue);
});
</script>

<template>
  <div class="relative w-full">
    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
      <Search class="h-4 w-4 text-muted-foreground" />
    </div>

    <input
      v-model="modelValue"
      type="text"
      :placeholder="placeholder"
      :class="cn(
        'ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500 flex h-10 w-full rounded-md border pl-10 pr-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
        props.class
      )"
    >

    <div
      v-if="hasValue"
      class="absolute inset-y-0 right-0 flex items-center pr-3"
    >
      <button
        type="button"
        class="text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
        @click="clearSearch"
      >
        <X class="h-4 w-4" />
      </button>
    </div>
  </div>
</template>
