<script lang="ts" setup>
import { DateTime } from 'luxon';
import { isToday } from '@/util/facades/date';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';
import type { InstallationDetailData } from '../types/installation-types';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  installationDetail: InstallationDetailData,
}

defineProps<Props>();

</script>

<template>
  <div class="w-full h-full grid grid-cols-2 [&>div]:p-1 [&_div]:rounded-[calc(0.25rem+4px)] gap-0.5 text-center text-[0.95rem]">
    <div class="flex flex-col bg-prim-col-foreground-contrast/10 items-center justify-center">
      <div class="text-xs">
        {{ $t('misc.date-created') }}
      </div>
      <div class="font-medium">
        {{ DateTime.now().toLocaleString(DateTime.DATE_MED) }}
      </div>
    </div>
    <div class="flex flex-col items-center justify-center">
      <div class="text-xs">
        {{ $t('installation.classification') }}
      </div>
      <div class="font-medium">
        {{ installationDetail.classification ? $t(`installation.classification-values.${installationDetail.classification}`) : '-' }}
      </div>
    </div>
    <div class="flex flex-col items-center justify-center">
      <div class="text-xs">
        {{ $t('installation.pv-capacity') }}
      </div>
      <div v-if="installationDetail?.pv_capacity" class="font-medium">
        {{ installationDetail.pv_capacity }} kW
      </div>
      <div v-else class="font-medium">
        -
      </div>
    </div>
    <div class="flex flex-col bg-prim-col-foreground-contrast/10 items-center justify-center">
      <div class="text-xs">
        {{ $t('installation.battery-capacity') }}
      </div>
      <div v-if="installationDetail?.battery_capacity" class="font-medium">
        {{ installationDetail.battery_capacity }} kWh
      </div>
      <div v-else class="font-medium">
        -
      </div>
    </div>
    <div class="flex flex-col bg-prim-col-foreground-contrast/10 items-center justify-center">
      <div class="text-xs">
        {{ $t('misc.locality') }}
      </div>
      <div class="font-medium">
        {{ installationDetail.locality ?? '-' }}
      </div>
    </div>
    <div class="flex flex-col items-center justify-center">
      <div class="text-xs">
        {{ $t('installation.last-update') }}
      </div>
      <div v-if="inverterCurrentData && inverterCurrentData.time" class="flex items-center font-medium gap-1">
        <div
          class="h-2 w-2 rounded-full"
          :class="[inverterCurrentData.expired && DateTime.fromISO(inverterCurrentData.time) < DateTime.now().minus({minute: 5}) ? 'bg-orange-400' : 'bg-green-400']"
        />
        <div>{{ DateTime.fromISO(inverterCurrentData?.time).toFormat(isToday(DateTime.fromISO(inverterCurrentData?.time)) ? 'HH:mm:ss' : 'd.M.y HH:mm') }}</div>
      </div>
      <div v-else class="flex items-center font-medium">
        <div>-</div>
      </div>
    </div>
  </div>
</template>
