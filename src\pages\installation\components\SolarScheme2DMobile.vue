<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { isLightModeEnabled } from '@/composables/theme';
import { animateArrows, drawLine, updateTextPosition, type ExtendedTextOptions } from '@/util/facades/svg-scheme.ts';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';
import { FlowChartSection, getFlowChartSectionValue, getInstallationInverterType, normalizeUnitByThreshold } from '../helpers/inverter-helper';
import type { InstallationDetailData } from '../types/installation-types';
import type { WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';
import SolarSchemeWithExportsMobile from '@/SolarSchemeWithExportsMobile.vue';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
  waterHeaterCurrentData: WaterHeaterCurrentDataTransformed,
  installationDetail: InstallationDetailData,
}

type DirectionalRecord = {
  in?: { value: SVGElement | undefined };
  out?: { value: SVGElement | undefined };
};

interface ArrowsDirection { entityDirection: 'in' | 'out', order: 'ascending' | 'descending' }

const props = defineProps<Props>();

const schemeRef = ref<InstanceType<typeof SolarSchemeWithExportsMobile> | null>(null);
const inverterType = computed(() => getInstallationInverterType({ installationDetail: props.installationDetail }));

let disabledGroups: (keyof (InstanceType<typeof SolarSchemeWithExportsMobile>['wholeGroups']))[] = [
  'heatingGroup',
  'wallBoxGroup'
];

const updateTextSafe = (
  circleElement: SVGCircleElement,
  valueResult: { value?: number; unit?: string } | null,
  key: string,
  options?: ExtendedTextOptions
) => {
  if (valueResult?.value !== undefined && valueResult?.value !== null) {
    const value = Math.abs(valueResult.value);
    const formatted = options?.fractionDigits !== undefined
      ? value.toFixed(options.fractionDigits)
      : value.toString();

    updateTextPosition(circleElement, `${formatted}${options?.spaceBeforeUnit ? ' ' : ''}${valueResult.unit ?? ''}`, key, options);
    return;
  }
  updateTextPosition(circleElement, '-', key, options);
};

const animateArrowSafe = (valueResult: { value?: number; unit?: string } | null, svgGroupLocation: DirectionalRecord, direction: ArrowsDirection) => {
  if (svgGroupLocation.in?.value) {
    animateArrows(svgGroupLocation['in'].value!, direction.order, false);
  }
  if (svgGroupLocation.out?.value) {
    animateArrows(svgGroupLocation['out'].value!, direction.order, false);
  }
  animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, false);
  if (valueResult && (valueResult?.value !== undefined || valueResult?.value !== null) && valueResult?.value !== 0) {
    animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, true);
    return;
  }
  animateArrows(svgGroupLocation[direction.entityDirection]!.value!, direction.order, false);
};

const getArrowsDirection = (directionToEntity: 'ascending' | 'descending', valueResult: { value?: number; unit?: string } | null,): {entityDirection: 'in' | 'out', order: 'ascending' | 'descending'} => {
  if ((valueResult?.value !== undefined || valueResult?.value !== null) && valueResult!.value! > 0) {
    return directionToEntity === 'ascending' ? { order: 'ascending', entityDirection: 'out' } : { order: 'descending', entityDirection: 'out' };
  }
  return directionToEntity === 'ascending' ? { order:'descending', entityDirection: 'in' } : { order:'ascending', entityDirection: 'in' };
};

const render = () => {
  if (!props.inverterCurrentData?.metrics || Object.keys(props.inverterCurrentData.metrics).length === 0 || !inverterType.value) {
    return;
  }

  const mergedCurrentData: InverterCurrentDataTransformed | WaterHeaterCurrentDataTransformed = {
    metrics: {
      ...props.inverterCurrentData.metrics,
      ...props.waterHeaterCurrentData?.metrics
    },
    time: props.inverterCurrentData.time,
  };

  const photovoltaics = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.PHOTOVOLTAICS, currentData: props.inverterCurrentData });
  const battery = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.BATTERY, currentData: props.inverterCurrentData });
  const batterySoc = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.BATTERY_SOC, currentData: props.inverterCurrentData });
  const grid = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.GRID, currentData: props.inverterCurrentData });
  const household = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HOUSEHOLD, currentData: mergedCurrentData, min: 0 });
  const waterHeater = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HEATING, currentData: props.waterHeaterCurrentData });
  const waterHeaterTemp = getFlowChartSectionValue({ inverterType: inverterType.value, section: FlowChartSection.HEATING_TEMP, currentData: props.waterHeaterCurrentData, min: 0 });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.main.value!, normalizeUnitByThreshold({ ...photovoltaics!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'photovoltaics_main', { fontSize: '4.5' });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.battery.batteryMain.value!, normalizeUnitByThreshold({ ...battery!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'battery_batteryMain', { fontSize: '4.5' });
  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.battery.batteryState.value!, batterySoc, 'battery_batteryState', { classList: ['main-chart-text'] });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.grid.main.value!, normalizeUnitByThreshold({ ...grid!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'grid_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.heating.main.value!, '0W', 'heating_main', { fontSize: '4.5' });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.tuv.main.value!, normalizeUnitByThreshold({ ...waterHeater!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'tuv_main', { fontSize: '4.5' });
  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.tuv.tuvStateCurrentTemperatureCenter.value!, waterHeaterTemp, 'tuv_tuvStatePercent', { classList: ['main-chart-text'] });

  updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.household.main.value!, normalizeUnitByThreshold({ ...household!, reduceOverThreshold: 100, reductionDivider: 1000, abs: true }), 'household_main', { fontSize: '4.5' });

  updateTextPosition(schemeRef.value!.dynamicTextCenterPoints.wallBox.main.value!, '0W', 'wallBox_main', { fontSize: '4.5' });

  if (props.installationDetail?.pv_capacity) {
    const photovoltaicsPercent = ((photovoltaics?.value ?? 0) / (props.installationDetail?.pv_capacity ?? 1) / 1000) * 100;
    updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, { value: photovoltaicsPercent, unit: '%' }, 'photovoltaics_currentPowerPercent', { fractionDigits: 1, classList: ['main-chart-text'] });
    drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsCurrentPowerChart.value!, 100 - photovoltaicsPercent);
  } else {
    updateTextSafe(schemeRef.value!.dynamicTextCenterPoints.photovoltaics.currentPower.value!, null, 'photovoltaics_currentPowerPercent', { classList: ['main-chart-text'] });
    drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsCurrentPowerChart.value!, 100);
  }
  // drawLine(schemeRef.value!.charts.photovoltaics.photovoltaicsTodaysPowerChart.value!, 100 - percentage.value);
  if (!disabledGroups.includes('batteryGroup') && batterySoc?.value) {
    drawLine(schemeRef.value!.charts.battery.batteryStateChart.value!, 100 - batterySoc?.value);
  } else {
    drawLine(schemeRef.value!.charts.battery.batteryStateChart.value!, 100);
  }
  if (!disabledGroups.includes('tuvGroup') && props.waterHeaterCurrentData?.metrics?.temp_max?.value && waterHeaterTemp?.value) {
    drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100 - (Math.round(waterHeaterTemp!.value) / props.waterHeaterCurrentData.metrics.temp_max.value) * 100);
  } else {
    drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100);
  }
  // drawLine(schemeRef.value!.charts.grid.gridConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.grid.gridPowerChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.heating.heatingTodayConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.tuv.tuvStateChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.household.householdConsumptionChart.value!, 100 - percentage.value);
  // drawLine(schemeRef.value!.charts.wallBox.wallboxConsumptionChart.value!, 100 - percentage.value);

  // - je push do siete, + je cerpanie zo siete
  const batteryArrowsDirection = getArrowsDirection('ascending', battery);
  const gridArrowsDirection = getArrowsDirection('descending', grid);
  animateArrowSafe(photovoltaics, schemeRef.value!.arrows.photovoltaics, { entityDirection: 'out', order: 'ascending' });
  animateArrowSafe(battery, schemeRef.value!.arrows.battery, batteryArrowsDirection);
  animateArrowSafe(grid, schemeRef.value!.arrows.grid, gridArrowsDirection);
  animateArrowSafe(household, schemeRef.value!.arrows.household, { entityDirection: 'in', order: 'descending' });
  animateArrowSafe(waterHeater, schemeRef.value!.arrows.tuv, { entityDirection: 'in', order: 'ascending' });

  // animateArrowSafe(test, schemeRef.value!.arrows.heating, { entityDirection: 'in', order: 'ascending' });
  // animateArrowSafe(test, schemeRef.value!.arrows.wallBox, { entityDirection: 'in', order: 'descending' });
  // animateArrows(schemeRef.value!.arrows.tuv.in.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.household.in.value!, 'ascending', true);
  // animateArrows(schemeRef.value!.arrows.wallBox.in.value!, 'ascending', true);
};

if (props.inverterCurrentData?.metrics && (!props.inverterCurrentData.metrics.atk_battery_charge?.value && !props.inverterCurrentData.metrics.atk_battery_power?.value)) {
  disabledGroups.push('batteryGroup');
}

if (!props.waterHeaterCurrentData || (!props.waterHeaterCurrentData?.metrics?.temp?.value)) {
  disabledGroups.push('tuvGroup');
}

const updateDisabledGroups = () => {
  Object.keys(schemeRef.value!.wholeGroups).forEach(g => {
    schemeRef.value?.wholeGroups[g as keyof typeof schemeRef.value.wholeGroups]?.value?.classList.remove('opacity-30');
  });
  disabledGroups.forEach(g => schemeRef.value!.wholeGroups[g].value!.classList.add('opacity-30'));
};

onMounted(() => {
  if (props.inverterCurrentData) {
    updateDisabledGroups();
    render();
  }
});

watch([() => props.inverterCurrentData, () => props.waterHeaterCurrentData], () => {
  if (props.inverterCurrentData?.metrics && (!props.inverterCurrentData.metrics.atk_battery_charge?.value && !props.inverterCurrentData.metrics.atk_battery_power?.value)) {
    disabledGroups.push('batteryGroup');
  } else {
    disabledGroups = disabledGroups.filter(g => g !== 'batteryGroup');
  }
  if (!props.waterHeaterCurrentData || (!props.waterHeaterCurrentData?.metrics?.temp?.value)) {
    disabledGroups.push('tuvGroup');
  } else {
    disabledGroups = disabledGroups.filter(g => g !== 'tuvGroup');
  }
  updateDisabledGroups();
  render();
});

</script>

<template>
  <div
    class="w-full h-full rounded-2xl"
    :class="[
      isLightModeEnabled ? 'bg-white' : 'bg-prim-col-foreground-1',
      !inverterCurrentData?.metrics || Object.keys(inverterCurrentData.metrics).length === 0 ? 'opacity-30' : '',
    ]"
  >
    <SolarSchemeWithExportsMobile ref="schemeRef" />
  </div>
</template>
