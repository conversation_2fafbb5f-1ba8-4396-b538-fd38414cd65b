import type { CustomWindow } from '@/../typings/window';

export const executeWebkitMessage = (functionName: keyof CustomWindow, payload: string | null = null) => {
  if ((window as CustomWindow).webkit?.messageHandlers?.[functionName] !== undefined) {
    (window as CustomWindow).webkit.messageHandlers[functionName].postMessage(payload);
    return;
  }

  if ((window as CustomWindow)[functionName] !== undefined) {
    (window as CustomWindow)[functionName].execute(payload);
  }
};
