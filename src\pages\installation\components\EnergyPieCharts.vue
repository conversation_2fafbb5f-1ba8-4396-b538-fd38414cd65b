<script setup lang="ts">
import { computed } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import {
  CanvasRenderer
} from 'echarts/renderers';
import {
  PieChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { isLightModeEnabled } from '@/composables/theme';
import { useI18n } from 'vue-i18n';

use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
]);

interface GridData {
  producedEnergy: {
    value: number;
    unit: string;
  } | undefined;
  energyFromNetwork: {
    value: number;
    unit: string;
  } | undefined;
  energyToNetwork: {
    value: number;
    unit: string;
  } | undefined;
  houseConsumption: {
    value: number;
    unit: string;
  } | undefined;
}

interface Props {
  gridData: GridData;
}

const props = defineProps<Props>();
const { t } = useI18n();

const productionDistributionData = computed(() => {
  if (!props.gridData.producedEnergy || !props.gridData.energyToNetwork || !props.gridData.houseConsumption) {
    return [];
  }

  const produced = props.gridData.producedEnergy.value;
  const toNetwork = props.gridData.energyToNetwork.value;
  const homeConsumptionFromPV = Math.max(0, produced - toNetwork);

  return [
    homeConsumptionFromPV,
    toNetwork
  ];
});

const productionDistributionLabels = computed(() => [
  t('installation.house-consumption'),
  t('metrics.atk_grid_energy_sell'),
]);

const consumptionCoverageData = computed(() => {
  if (!props.gridData.houseConsumption || !props.gridData.energyFromNetwork || !props.gridData.producedEnergy || !props.gridData.energyToNetwork) {
    return [];
  }

  const totalConsumption = props.gridData.houseConsumption.value;
  const fromNetwork = props.gridData.energyFromNetwork.value;
  const fromPV = Math.max(0, totalConsumption - fromNetwork);

  return [
    fromPV,
    fromNetwork
  ];
});

const consumptionCoverageLabels = computed(() => [
  t('charts.pv-coverage'),
  t('charts.grid-coverage'),
]);

const productionChartOptions = computed(() => {
  const unit = props.gridData.producedEnergy?.unit || 'kWh';

  return {
    darkMode: !isLightModeEnabled.value,
    grid: { containLabel: true },
    title: {
      text: t('charts.solar-energy-ratio'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = params.percent;
        const value = params.value;
        return `${params.name}<br/>${Math.round(value * 1000) / 1000} ${unit} (${percentage}%)`;
      },
    },
    legend: {
      show: true,
      orient: 'horizontal',
      bottom: 0,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
        fontSize: 11,
      },
    },
    series: [
      {
        name: t('charts.solar-energy-ratio'),
        type: 'pie',
        top: 30,
        bottom: 30,
        left: 10,
        right: 10,
        radius: ['30%', '65%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 0,
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          padding: [0, 8],
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
        labelLine: {
          show: true,
          showAbove: false,
          length: 10,
          length2: 15,
          smooth: false,
        },
        data: [
          {
            value: productionDistributionData.value[0],
            name: productionDistributionLabels.value[0],
            itemStyle: { color: '#f39c12' },
            label: {
              show: true,
              position: 'outside',
              offset: [-10, 0],
              formatter: (p: any) => {
                const v = +p.value || 0;
                const pct = isFinite(+p.percent)
                  ? Math.min(Math.max(+p.percent, 0), 100)
                  : (v > 0 ? 100 : 0);
                return `${p.name}\n${(Math.round(v * 1000) / 1000)} ${unit}\n(${pct.toFixed(1)}%)`;
              },
            },
          },
          {
            value: productionDistributionData.value[1],
            name: productionDistributionLabels.value[1],
            itemStyle: { color: '#27ae60' },
            label: {
              show: true,
              position: 'outside',
              offset: [10, 0],
              formatter: (p: any) => {
                const v = +p.value || 0;
                const pct = isFinite(+p.percent)
                  ? Math.min(Math.max(+p.percent, 0), 100)
                  : (v > 0 ? 100 : 0);
                return `${p.name}\n${(Math.round(v * 1000) / 1000)} ${unit}\n(${pct.toFixed(1)}%)`;
              },
            },
          },
        ],
      },
    ],
  };
});

const consumptionChartOptions = computed(() => {
  const unit = props.gridData.producedEnergy?.unit || 'kWh';
  const totalConsumption = props.gridData.houseConsumption?.value || 0;

  return {
    darkMode: !isLightModeEnabled.value,
    title: {
      text: t('charts.consumption coverage'),
      left: 'center',
      top: 5,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = params.percent;
        const value = params.value;
        return `${params.name}<br/>${Math.round(value * 1000) / 1000} ${unit} (${percentage}%)`;
      },
    },
    legend: {
      show: true,
      orient: 'horizontal',
      bottom: 0,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
        fontSize: 11,
      },
    },
    series: [
      {
        name: 'Pokrytie spotreby',
        type: 'pie',
        top: 30,
        bottom: 30,
        left: 10,
        right: 10,
        radius: ['30%', '65%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 0,
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          padding: [0, 8],
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
        labelLine: {
          show: true,
          showAbove: false,
          length: 10,
          length2: 15,
          smooth: false,
        },
        data: [
          {
            value: consumptionCoverageData.value[0],
            name: consumptionCoverageLabels.value[0],
            itemStyle: { color: '#27ae60' },
            label: {
              show: true,
              position: 'outside',
              offset: [-10, 0],
              formatter: (p: any) => {
                const v = +p.value || 0;
                const t = +totalConsumption || 0;
                const pct = t > 0
                  ? Math.min(Math.max((v / t) * 100, 0), 100)
                  : (v > 0 ? 100 : 0);
                return `${p.name}\n${(Math.round(v * 1000) / 1000)} ${unit}\n(${pct.toFixed(1)}%)`;
              },
            },
          },
          {
            value: consumptionCoverageData.value[1],
            name: consumptionCoverageLabels.value[1],
            itemStyle: { color: '#e74c3c' },
            label: {
              show: true,
              position: 'outside',
              offset: [10, 0],
              formatter: (p: any) => {
                const v = +p.value || 0;
                const t = +totalConsumption || 0;
                const pct = t > 0
                  ? Math.min(Math.max((v / t) * 100, 0), 100)
                  : (v > 0 ? 100 : 0);
                return `${p.name}\n${(Math.round(v * 1000) / 1000)} ${unit}\n(${pct.toFixed(1)}%)`;
              },
            },
          },
        ],
      },
    ],
  };
});

const hasValidData = computed(() => {
  return props.gridData?.producedEnergy &&
         props.gridData?.energyFromNetwork &&
         props.gridData?.energyToNetwork &&
         props.gridData?.houseConsumption;
});
</script>

<template>
  <div class="flex flex-col p-1 h-full">
    <div v-if="hasValidData" class="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
      <div class="flex flex-col items-center justify-center">
        <VChart
          :option="productionChartOptions"
          class="w-full"
          style="height: 240px;"
          autoresize
        />
      </div>

      <div class="flex flex-col items-center justify-center">
        <VChart
          :option="consumptionChartOptions"
          class="w-full"
          style="height: 240px;"
          autoresize
        />
      </div>
    </div>

    <div v-else class="flex flex-col items-center justify-center h-48 text-gray-500">
      <div class="text-center">
        <div class="text-lg font-medium mb-2">
          Nedostupné dáta
        </div>
        <div class="text-sm">
          Pre zobrazenie grafov sú potrebné všetky energetické údaje
        </div>
      </div>
    </div>
  </div>
</template>
