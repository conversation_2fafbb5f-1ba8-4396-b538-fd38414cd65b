<template>
  <div class="space-y-8">
    <!-- Global date & aggregation controls -->
    <div class="flex flex-wrap gap-4 items-center mt-4 mb-6 bg-prim-col-foreground-1 rounded-xl">
      <label class="font-medium text-sm">
        {{ $t('device-detail.start') }}
        <input
          v-model="start"
          type="datetime-local"
          class="border p-2 rounded-lg ml-2 bg-prim-col-foreground-1 border-prim-col-foreground-contrast/30 chart-date-input"
          @change="reloadAllCharts"
        >
      </label>
      <label class="font-medium text-sm">
        {{ $t('device-detail.end') }}
        <input
          v-model="end"
          type="datetime-local"
          class="border p-2 rounded-lg ml-2 bg-prim-col-foreground-1 border-prim-col-foreground-contrast/30 chart-date-input"
          @change="reloadAllCharts"
        >
      </label>
      <label class="font-medium text-sm">
        {{ $t('device-detail.aggregation') }}
        <select
          v-model="aggregation"
          class="border p-2 rounded-lg ml-2 bg-prim-col-foreground-1 border-prim-col-foreground-contrast/30"
          @change="reloadAllCharts"
        >
          <option value="">
            {{ $t('device-detail.no-aggregation') }}
          </option>
          <option v-for="agg in aggregations" :key="agg" :value="agg">
            {{ $t(`device-detail.aggregations.${agg}`) }}
          </option>
        </select>
      </label>
    </div>

    <!-- Chart cards -->
    <div
      v-for="(chart, index) in charts"
      :key="index"
      class="bg-prim-col-foreground-1 rounded-xl p-4 space-y-4 border"
    >
      <div class="space-y-3">
        <div
          v-for="(metric, metricIndex) in chart.metrics"
          :key="metricIndex"
          class="flex flex-wrap gap-2 items-center rounded-lg"
        >
          <select
            v-model="metric.name"
            class="border p-2 rounded-lg bg-prim-col-foreground-1 border-prim-col-foreground-contrast/30 flex-1 min-w-[200px]"
            @change="loadChartData(index)"
          >
            <option disabled value="">
              {{ $t('device-detail.select-metric') }}
            </option>
            <option
              v-for="availableMetric in historicMetrics"
              :key="availableMetric.name"
              :value="availableMetric.name"
            >
              {{ availableMetric.name }} ({{ availableMetric.unit }}){{ availableMetric.type === 'float_counter' ? ', counter' : '' }}
            </option>
          </select>

          <select
            v-model="metric.aggregationType"
            class="border p-2 rounded-lg bg-prim-col-foreground-1 border-prim-col-foreground-contrast/30 text-sm"
            @change="loadChartData(index)"
          >
            <option
              v-for="aggType in filterAggTypes(metric.metricType || 'float')"
              :key="aggType"
              :value="aggType"
            >
              {{ aggType === 'avg' ? $t('device-detail.average') : aggType.charAt(0).toUpperCase() + aggType.slice(1) }}
            </option>
          </select>

          <button
            v-if="chart.metrics.length > 1"
            class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm cursor-pointer"
            @click="removeMetric(index, metricIndex)"
          >
            {{ $t('device-detail.remove') }}
          </button>
        </div>

        <button
          class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1.5 rounded-lg transition-colors duration-200 text-sm cursor-pointer"
          @click="addMetric(index)"
        >
          {{ $t('device-detail.add-metric') }}
        </button>
      </div>

      <div v-if="chart.error" class="flex items-center h-80 justify-center">
        <span class="text-red-500">{{ chart.error }}</span>
      </div>
      <div v-else class="h-[300px]">
        <v-chart
          v-show="chart.options"
          :theme="isLightModeEnabled ? 'light' : 'dark'"
          :option="chart.options"
          autoresize
          class="w-full rounded-2xl overflow-hidden"
        />
        <div
          v-if="chart.metrics.length === 0 || !chart.metrics.some(m => m.name)"
          class="text-sm text-prim-col-foreground-contrast text-center w-full h-full bg-black/10 rounded-2xl flex items-center justify-center"
        >
          {{ $t('device-detail.select-metric-message') }}
        </div>
      </div>
    </div>

    <button
      class="bg-prim-col-selected-1 cursor-pointer hover:bg-prim-col-selected-2 text-white px-3 py-1.5 rounded-lg transition-colors duration-200 text-sm"
      @click="addChart"
    >
      {{ $t('device-detail.add-chart') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import VChart from 'vue-echarts';
import {
  CanvasRenderer
} from 'echarts/renderers';
import {
  LineChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
} from 'echarts/components';
import { customAxios } from '@/util/axios';
import { isLightModeEnabled } from '@/composables/theme';
import { DateTime } from 'luxon';
import { createMultiSeriesChartOptions } from './echarts-config';

use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
]);

interface Props {
  statsData: {
    metrics: {
      name: string;
      unit: string;
      from?: string;
      to?: string;
      type: 'float' | 'float_counter';
    }[];
  };
  installationId: string | number;
  deviceInstanceId: string | number;
  forbiddenMetrics?: string[]
}

const props = withDefaults(defineProps<Props>(), { forbiddenMetrics: () => [] });

const aggregations = [
  '5_minutes',
  '10_minutes',
  '30_minutes',
  '1_hour',
  '2_hours',
  '3_hours',
  '6_hours',
  '12_hours',
  '1_day',
  '1_week',
  '1_month'
];

const historicMetrics = computed(() =>
  props.statsData.metrics.filter(m => m.from && m.to && !props.forbiddenMetrics?.includes(m.name) )
);

// Aggregation types for per-metric configuration
const aggregationTypes = ['max', 'min', 'avg', 'delta'] as const;
type AggregationType = typeof aggregationTypes[number];

// Enhanced chart structure to support multiple metrics per chart
interface ChartMetric {
  name: string;
  aggregationType: AggregationType | '';
  metricType?: 'float' | 'float_counter';
}

interface Chart {
  metrics: ChartMetric[];
  options: any;
  loading: boolean;
  error: string | null;
}

const charts = ref<Chart[]>([{
  metrics: [{
    name: '',
    aggregationType: '',
  }],
  options: null,
  loading: false,
  error: null,
}]);

function addChart() {
  charts.value.push({
    metrics: [{
      name: '',
      aggregationType: '',
    }],
    options: null,
    loading: false,
    error: null,
  });
}

function addMetric(chartIndex: number) {
  charts.value[chartIndex].metrics.push({
    name: '',
    aggregationType: '',
  });
}

function removeMetric(chartIndex: number, metricIndex: number) {
  charts.value[chartIndex].metrics.splice(metricIndex, 1);
  loadChartData(chartIndex);
}

const filterAggTypes = (metricType: 'float' | 'float_counter'): AggregationType[] => {
  if (!metricType) {
    return aggregationTypes as unknown as AggregationType[];
  }
  if (metricType === 'float') {
    return aggregationTypes.filter(t => t !== 'delta');
  }
  return aggregationTypes.filter(t => t === 'delta');
};

const getDefaultAggType = (metricType: 'float' | 'float_counter') => {
  if (metricType === 'float') {
    return 'avg';
  }
  return 'delta';
};

// Helpers for default start/end values (last 24h)
const now = new Date();
const toLocalInput = (date: Date) => DateTime.fromJSDate(date).toFormat("yyyy-MM-dd'T'HH:mm");
const start = ref(toLocalInput(new Date(now.getTime() - 24 * 60 * 60 * 1000)));
const end = ref(toLocalInput(now));
const aggregation = ref<string>('');

// Reload all charts when filters change
function reloadAllCharts() {
  charts.value.forEach((_, idx) => {
    if (charts.value[idx].metrics.some(m => m.name)) {
      loadChartData(idx);
    }
  });
}

async function loadChartData(index: number) {
  const chart = charts.value[index];
  chart.loading = true;
  chart.error = null;

  // Filter out metrics that don't have a name selected
  const validMetrics = chart.metrics
    .map(m => {
      const historicMetric = historicMetrics.value.find(propMetric => propMetric.name === m.name)!;
      m.metricType = historicMetric.type;

      const allowedAggTypes: AggregationType[] = filterAggTypes(historicMetric.type);
      let aggregationType: AggregationType;

      if (m.aggregationType === '') {
        aggregationType = getDefaultAggType(historicMetric.type);
      } else if (m.aggregationType && (allowedAggTypes).includes(m.aggregationType)) {
        aggregationType = m.aggregationType;
      } else {
        aggregationType = getDefaultAggType(historicMetric.type);
      }

      m.aggregationType = aggregationType;

      return {
        ...historicMetric,
        aggregationType,
      };
    });

  if (validMetrics.length === 0) {
    chart.options = null;
    chart.loading = false;
    return;
  }

  try {
    // Make separate requests per metric because each metric might have different aggregation types
    const responses = await Promise.all(
      validMetrics.map(metric =>
        customAxios.get(
          `/user/device/range/${props.installationId}/${props.deviceInstanceId}`,
          {
            params: {
              type: [metric.name],
              start: start.value,
              end: end.value,
              aggregation: aggregation.value || undefined,
              aggregation_type: metric.aggregationType,
            }
          }
        )
      )
    );

    // Flatten all responses into a single array
    const allData: any[] = [];
    responses.forEach(response => {
      if (Array.isArray(response.data)) {
        allData.push(...response.data);
      }
    });

    if (allData.length === 0) {
      chart.error = 'No data returned from API.';
      chart.options = null;
      chart.loading = false;
      return;
    }

    // Process each metric's data
    const seriesData: any[] = [];
    const uniqueUnits = new Set<string>();

    for (const chartMetric of validMetrics) {
      const metricEntry = allData.find((entry: any) => entry.metric === chartMetric.name);
      const historicMetric = historicMetrics.value.find(m => m.name === chartMetric.name);

      if (!metricEntry || !historicMetric) {continue;}

      const dataArr = metricEntry?.data?.data;
      if (!Array.isArray(dataArr)) {continue;}

      uniqueUnits.add(historicMetric.unit);

      // Extract value based on aggregation type and global aggregation setting
      const extractValue = (point: any) => {
        if (aggregation.value) {
          // When global aggregation is set, use the aggregation type for the metric
          switch (chartMetric.aggregationType) {
            case 'max': return point.max || point.avg || point.v;
            case 'min': return point.min || point.avg || point.v;
            case 'avg': return point.avg || point.v;
            case 'delta': return point.delta || point.v;
            default: return point.avg || point.v;
          }
        } else {
          // When no global aggregation, still use per-metric aggregation type
          // The API should return the appropriate aggregated data based on aggregation_type param
          switch (chartMetric.aggregationType) {
            case 'max': return point.max || point.avg || point.v;
            case 'min': return point.min || point.avg || point.v;
            case 'avg': return point.avg || point.v;
            case 'delta': return point.delta || point.v;
            default: return point.v;
          }
        }
      };

      const series = dataArr.map((point: any) => [
        point.t,
        extractValue(point),
      ]);

      seriesData.push({
        name: chartMetric.name,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { width: 2 },
        data: series,
        unit: historicMetric.unit,
      });
    }

    if (seriesData.length === 0) {
      chart.error = 'No valid data returned for selected metrics.';
      chart.options = null;
      chart.loading = false;
      return;
    }

    chart.options = createMultiSeriesChartOptions(seriesData, Array.from(uniqueUnits));

  } catch (err: any) {
    chart.error = err?.message || 'Failed to load chart data.';
    chart.options = null;
    console.error('Chart data fetch failed:', err);
  } finally {
    chart.loading = false;
  }
}
</script>
