<template>
  <div class="bg-prim-col-foreground-1 p-4 rounded-xl">
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <DeviceCard
        v-for="device in devices"
        :key="device.type"
        :type="device.type"
        :title="device.title"
        :status="device.status"
        :primary-metric="device.primaryMetric"
        :secondary-metric="device.secondaryMetric"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DeviceCard from './DeviceCard.vue';

const devices = ref([
  {
    type: 'battery_storage',
    title: 'Battery',
    status: 'online',
    primaryMetric: { value: 85, unit: '%' },
    secondaryMetric: { value: 12.5, unit: 'kWh' },
  },
  {
    type: 'ev_charger',
    title: 'EV Charger',
    status: 'online',
    primaryMetric: { value: 7.4, unit: 'kW' },
  },
  {
    type: 'domestic_water_heater_dc',
    title: 'Water Heater',
    status: 'online',
    primaryMetric: { value: 45, unit: '°C' },
    secondaryMetric: { value: 2.1, unit: 'kW' },
  },
  {
    type: 'grid',
    title: 'Grid',
    status: 'online',
    primaryMetric: { value: 4.2, unit: 'kW' },
  },
  {
    type: 'heat_pump',
    title: 'Heat Pump',
    status: 'offline',
    primaryMetric: { value: 0, unit: 'kW' },
  },
  {
    type: 'inverter',
    title: 'Inverter',
    status: 'online',
    primaryMetric: { value: 3.8, unit: 'kW' },
    secondaryMetric: { value: 18.5, unit: 'kWh/day' },
  },
]);
</script>
