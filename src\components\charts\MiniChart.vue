<template>
  <vue-apex-charts
    class="dark:bg-prim-col-1 bg-white rounded-md"
    s
    type="line"
    width="100%"
    height="100%"
    :options="chartOptions"
    :series="series"
  />
</template>

<script setup lang="ts">
import VueApexCharts from 'vue3-apexcharts';
import type { ApexOptions } from 'apexcharts';

const generateRandomData = (length: number, min: number, max: number) => {
  return Array.from({ length }, () => Math.floor(Math.random() * (max - min + 1)) + min);
};

// Define the series with random data
const series = [
  {
    name: 'Line Preview',
    data: generateRandomData(10, 10, 50), // Generate 10 random numbers between 10 and 50
  },
];
const chartOptions: ApexOptions = {
  title: {
    text: 'Krivka za 6h',
    align: 'left',
    // margin: number
    offsetX: -5,
    // offsetY: number
    floating: true,
    style: {
      fontSize: '12px',
      fontFamily: '\'Roboto\', sans-serif',
      fontWeight: '300',
      // color: string
    },
  },
  chart: {
    toolbar: {
      show: false, // Disable toolbar
    },
    zoom: {
      enabled: false, // Disable zoom
    },
    offsetX: 0,
    offsetY: 0,
    parentHeightOffset: 0,
  },
  grid: {
    show: true, // Hide grid
    padding: {
      left: -5,
      top: -8,
      right: 5,
      bottom: -5,
    },
  },
  xaxis: {
    labels: {
      show: false, // Hide x-axis labels
    },
    axisBorder: {
      show: false, // Hide x-axis border
    },
    axisTicks: {
      show: false, // Hide x-axis ticks
    },
  },
  yaxis: {
    labels: {
      show: false, // Hide y-axis labels
    },
  },
  stroke: {
    curve: 'smooth', // Smooth line
    width: 2, // Line thickness
  },
  dataLabels: {
    enabled: false, // Disable data labels
  },
  tooltip: {
    enabled: false, // Disable tooltips
  },
  markers: {
    size: 0, // Hide markers
  },
  legend: {
    show: false, // Hide legend
  },
};
</script>
