<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import {
  CanvasRenderer
} from 'echarts/renderers';
import {
  Line<PERSON><PERSON>,
  Bar<PERSON>hart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
} from 'echarts/components';
import { DateTime } from 'luxon';
import { ComponentStateType } from '@/util/types/components';
import { MessageSquareWarning } from 'lucide-vue-next';
import PageLoader from '@/components/global/PageLoader.vue';
import type { DataSeries } from '@/util/types/data-series';
import { useI18n } from 'vue-i18n';
import { metricColorMap } from '@/pages/installation/helpers/installation-charts-config';
import type { ApiMetricKeys } from '@/util/types/api-responses';
import { isLightModeEnabled } from '@/composables/theme';
import { useScreenWidth } from '@/composables/useScreenWidth';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
]);

const props = defineProps<{
  dataSeries?: DataSeries;
  componentState: { data: ComponentStateType };
  xAxisTimeFormat: string;
  options?: any;
  yAxis?: any[];
  metricToYAxisIndex?: Record<string, number>;
  groupId?: string;
}>();

const chartRef = ref<any>(null);
const zoomState = ref<{ start: number; end: number } | null>(null);
const legendSelection = ref<Record<string, boolean>>({});
const { locale, t } = useI18n();
const { screenWidth } = useScreenWidth();

function convertDataPoints(data: { x: string, y: number }[]) {
  return data.map(point => [DateTime.fromISO(point.x).toMillis(), point.y]);
}

const defaultColors = [
  '#5470C6', '#91CC75', '#EE6666', '#fac858', '#73c0de', '#3ba272', '#fc8452'
];

const defaultOptions = computed(() => {
  const dataSeriesArray = props.dataSeries ?? [];

  const uniqueUnits = Array.from(new Set(dataSeriesArray.map(s => s.unit)));
  const yAxis = props.yAxis && props.yAxis.length
    ? props.yAxis
    : uniqueUnits.map((unit, idx) => ({
      type: 'value',
      name: unit,
      position: idx % 2 === 0 ? 'left' : 'right',
      offset: idx > 1 ? 50 * (idx - 1) : 0,
      alignTicks: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: defaultColors[idx % defaultColors.length]
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2'
        },
      },
      axisLabel: {
        formatter: '{value}'
      }
    }));

  // Build metric name → yAxis index mapping, or use provided
  const metricToYAxisIndex = props.metricToYAxisIndex || {};
  if (!props.metricToYAxisIndex) {
    // Default: group by unit, assign index by first seen
    const unitToIndex: Record<string, number> = {};
    uniqueUnits.forEach((unit, idx) => { unitToIndex[unit] = idx; });
    dataSeriesArray.forEach(series => {
      metricToYAxisIndex[series.originalMetricName || series.name] = unitToIndex[series.unit];
    });
  }

  const echartSeries = dataSeriesArray.map(series => ({
    name: series.name,
    type: series.type === 'column' ? 'bar' : 'line',
    smooth: 0.0,
    symbol: 'none',
    lineStyle: { width: 2 },
    areaStyle: {
      opacity: 0.25,
    },
    data: convertDataPoints(series.data),
    yAxisIndex: metricToYAxisIndex[series.originalMetricName || series.name] ?? 0,
    color: metricColorMap[series.originalMetricName as ApiMetricKeys] ?? defaultColors[metricToYAxisIndex[series.originalMetricName || series.name] ?? 0],
  }));

  return {
    darkMode: true,
    color: defaultColors,
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: (params: any) => {
        return params
          .map((p: any, index: number) => {
            const val = p.value[1];
            const time = DateTime.fromMillis(p.value[0]).setLocale(locale.value).toFormat('HH:mm:ss, d. LLL yyyy');
            const seriesObj = dataSeriesArray.find(s => s.name === p.seriesName);
            if (['atk_battery_power', 'atk_grid_power'].includes(seriesObj?.originalMetricName as string)) {
              p.seriesName = t(`metrics.${seriesObj?.originalMetricName}${val > 0 ? '+' : (val < 0 ? '-' : '')}`);
            }
            const unit = seriesObj?.unit || '';
            return `${index === 0 ? `${time}<br>` : ''}${p.marker} ${p.seriesName}: ${val} ${unit}`;
          })
          .join('<br>');
      },
      confine: 'true',
      textStyle: {
        overflow: 'breakAll',
        width: 40,
      },
    },
    legend: {
      show: true,
      selected: legendSelection.value,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
      }
    },
    grid: screenWidth.value > 600 ?
      { left: 50, right: 30 + 30 * (yAxis.length - 1), bottom: 75, top: 60 }
      : { left: 50, right: 15 + 25 * (yAxis.length - 1), bottom: 75, top: 100 },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2',
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
      },
      axisLabel: {
        formatter: (val: number) => DateTime.fromMillis(val).setLocale('sk').toFormat(props.xAxisTimeFormat),
      }
    },
    yAxis,
    series: echartSeries,
    // dataZoom: [
    //   {
    //     type: 'inside',
    //     realtime: true,
    //     xAxisIndex: 0,
    //     throttle: 50,
    //   }
    // ]
  };
});

const chartOptions = computed(() => {
  if (props.options) {
    const opt = { ...props.options };
    if (props.yAxis && props.yAxis.length) {
      opt.yAxis = props.yAxis;
    }
    return opt;
  }
  return defaultOptions.value;
});

function handleDataZoom(params: any) {
  if (params.batch && params.batch.length > 0) {
    zoomState.value = {
      start: params.batch[0].start ?? 0,
      end: params.batch[0].end ?? 100
    };
  } else if (params.start !== undefined && params.end !== undefined) {
    zoomState.value = {
      start: params.start,
      end: params.end
    };
  }
}

function handleLegendSelectChanged(params: any) {
  legendSelection.value = { ...params.selected };
}

watch(
  () => props.dataSeries,
  async() => {
    if (!chartRef.value || !zoomState.value) {return;}
    await nextTick();
    chartRef.value.dispatchAction({
      type: 'dataZoom',
      start: zoomState.value.start,
      end: zoomState.value.end,
    });
  }
);
</script>

<template>
  <div class="sm:px-4 relative">
    <VChart
      v-if="dataSeries?.length && dataSeries[0]?.data"
      ref="chartRef"
      autoresize
      :option="chartOptions"
      class="w-full h-full"
      @datazoom="handleDataZoom"
      @legendselectchanged="handleLegendSelectChanged"
    />
    <div
      v-else-if="componentState.data === ComponentStateType.ERROR"
      class="absolute-center p-1 rounded-md flex flex-col items-center gap-2"
    >
      <MessageSquareWarning class="h-14 w-14 opacity-30" />
      <div>{{ $t('misc.failed-to-get-data') }}</div>
    </div>
    <div
      v-else-if="!dataSeries?.length"
      class="absolute-center p-1 rounded-md flex flex-col items-center gap-2"
    >
      <MessageSquareWarning class="h-14 w-14 opacity-30" />
      <div>{{ $t('misc.no-data') }}</div>
    </div>
    <PageLoader
      v-if="componentState.data === ComponentStateType.LOADING"
      :absolute-center="true"
    />
  </div>
</template>
