{"alerts": {"title": "<PERSON><PERSON><PERSON>"}, "calendar": {"pick-a-day": "Pick a day", "pick-a-range": "Pick a range", "pick-date": "Pick a date", "pick-interval": "Pick an interval", "x-days": "{n} days | {n} day | {n} days", "x-months": "{n} months | {n} month | {n} months"}, "charts": {"ac-output": "AC Output", "battery-discharge": "Battery(W)(Discharge)", "contribution-ratio": "Contribution Ratio", "energy-metrics": "Energy Metrics", "feed-in": "Feed-in", "generation-income": "Generation & Income", "grid": "Grid", "in-house": "In-house", "in-house-kwh": "In-house(kWh)", "income-eur": "Income (EUR)", "load": "Load(W)", "load-consumption": "Load Consumption", "meter-buy": "Meter(W)(Buy)", "power": "Power", "pv": "PV(W)", "pv-bat": "PV + BAT", "pv-kwh": "PV(kWh)", "self-cons": "Self-Cons. Ratio", "self-cons-ratio": "Self-Cons. Ratio(%)", "sell-kwh": "Sell(kWh)", "soc": "SOC(%)", "solar-production": "Solar production", "pv-coverage": "Covered by FV", "grid-coverage": "From grid", "solar-energy-ratio": "FV production breakdown", "consumption coverage": "Consumption coverage"}, "installation": {"add-device": "Add device", "add-device-desc": "Enter your device pairing code. You can also assign a name to the device.", "add-device-tooltip": "To add a device, it is necessary to have an own installation created", "battery": "Battery", "battery-capacity": "Battery capacity", "classification": "Classification", "classification-values": {"on_grid": "On-grid", "hybrid": "Hybrid"}, "create-installation": "Create installation", "create-installation-desc": "Please fill in the name or description of the installation. You will be able to add your devices under the installation.", "device-counted": "devices | device | devices", "device-detail": "Device detail", "devices": {"inverter": "Inverter", "pairing": "Pairing"}, "edit-installation": "Edit installation", "energy-from-network": "Energy received from the network", "energy-to-network": "Energy sold to network", "form": {"device-add-already-created": "This device has already been registered.", "device-add-fail": "Failed to add device", "device-add-success": "The device has been successfully added. If it is successfully paired, it will appear under the installation.", "installation-fail-create": "Failed to create the installation", "installation-fail-edit": "Failed to update installation", "installation-share-fail": "Failed to share installation", "installation-share-success": "Activation email has been sent to {user}", "installation-share-user-exists": "The user is already assigned to the installation.", "installation-success-create": "Installation created successfully", "installation-success-edit": "Installation updated", "pv-capacity": "PV capacity", "panels-count": "Panels count", "battery-capacity": "Battery capacity"}, "house-consumption": "House consumption", "installations-shared-with-me": "Shared installations", "is-not-shared": "This installation is not shared to any user.", "last-update": "Last updated", "my-installations": "My installations", "new-device-added": "New device found", "no-installations": "No installations", "no-installations-desc": "Create an installation and add your first device to it", "paired-devices-counted": "paired devices | paired device | paired devices", "pairing-code": "Pairing code", "power": "Power", "produced-energy": "Produced energy", "pv-capacity": "PV capacity", "real-time-energy-flow": "Energy flow in real time", "share-installation": "Share installation", "share-installation-accepted": "The shared installation has been added to your dashboard.", "share-installation-desc": "After successfully submitting the form, an email will be sent to the user to activate the display of your installation in their dashboard.", "shared-installation": "Shared installation", "sharing": "Sharing", "title-base": "Installation", "title-counted": "installations | installation | installations", "title-plural": "Installations", "todays-summary": "Today's summary", "user-email": "User email", "remove-shared-user": "Remove viewing privileges", "add-device-tooltip-2": "Use the app for initial pairing. The devices will then be paired automatically.", "today-summary": "Today's summary", "total-summary": "Overall summary", "pick-a-day": "Select day", "plant-usage-chart": "Power plant utilization chart", "not-exists-or-forbidden": "The installation does not exist or you do not own it.", "energetic-blocks": "Energetic blocks", "my-plant": "My power plant", "photovoltaics": "Photovoltaics", "household": "Household", "my-electroinstallation": "My electroinstallation", "no-supported-devices": "No supported devices!", "device-online": "Device online", "device-offline": "Device is offline", "last-activity": "Last activity", "back-to-installation": "Back to installation", "day": "Day", "summary-data": {"tiles-view": "Tiles", "pie-view": "Pie charts"}}, "login": {"current-password": "Current password", "errorLoggingIn": "<PERSON><PERSON> was not successful", "failed-to-reset-pw": "Error when requesting password change. Please try again later.", "forgot-password": "Forgot password", "forgot-password-desc": "After sending the form with an email, you will receive an email with a link to reset your password", "forgotten-password": "Forgotten password", "log-out": "Log out", "login": "<PERSON><PERSON>", "loginDesc": "To enter, it is necessary to authenticate using an e-mail and a password.", "min-pw-length": "Password must contain at least 8 characters", "needAccount": "Don't have an account?", "new-password": "New password", "pw-do-not-match": "Passwords do not match", "pw-helper": {"length": "Password must be at least 10 characters long.", "lowercase": "Password must contain at least one lowercase letter.", "number": "Password must contain at least one number.", "symbol": "Password must contain at least one symbol.", "uppercase": "Password must contain at least one uppercase letter."}, "pw-successfully-set": "The password has been successfully set", "reask-new-password": "New request for password change", "repeat-new-password": "Repeat new password", "reset-needed": "Reset needed", "reset-password": "Reset password", "reset-password-desc": "To set a new password, fill out the following form", "reset-pw-succ": "Your password was changed successfully", "signIn": "Sign in", "signUp": "Sign up", "token-revoked": "Your login has expired. Please refresh the application."}, "metrics": {"atk_battery_charge": "Battery percentage", "atk_battery_power": "Battery power", "atk_battery_power+": "Battery draining", "atk_battery_power-": "Battery charging", "atk_grid_power": "Grid power", "atk_grid_power+": "Drawn power", "atk_grid_power-": "Excess power", "atk_solar_power": "Solar production", "total_load_power": "Consumption", "atk_grid_energy_buy": "Bought energy", "atk_grid_energy_sell": "Sold energy", "atk_solar_energy": "Produced energy", "atk_home_power": "Household load"}, "misc": {"422-try-again-later": "Too many requests. Please try again later.", "access-denied": "Access denied", "back-to-home": "Back to homepage", "cancel": "Cancel", "choose-from-menu": "Pick an item from menu", "code": "Code", "contacts": "Contacts", "continue": "Continue", "continue-prompt": "Do you want to continue?", "copy-to-clipboard": "Copy to clipboard", "create": "Create", "created": "Date created", "dashboard": "Dashboard", "date-created": "Date created", "description": "Description", "edit": "Edit", "failed-to-get-data": "Failed to get data from the server", "form-sent-successfully": "Formular was sent successfully", "getting-ready": "Getting ready", "hide": "<PERSON>de", "household": "Household", "loading": "Loading", "locality": "Locality", "mail": "Mail", "mode": "Mode", "name": "Name", "name-surname": "Name and surname", "own-title": "Custom title", "password": "Password", "phone": "Phone number", "photo": "Photo", "refresh": "Refresh", "remove": "Remove", "reset": "Reset", "save": "Save", "search": "Search", "send": "Send", "settings": "Settings", "show": "Show", "show-more": "Show more", "start": "Start", "surname": "Surname", "theme": "Theme", "title": "Title", "today": "Today", "token-not-valid": "This password request has already expired", "type": "Type", "unassigned": "Not assigned", "undefined": "Undefined", "upload-photo": "Upload photo", "select-option": "Select an option", "24h": "24h", "image": "Image", "unknown-state": "Unknown state", "last-24h": "Last 24 hours", "no-data": "No data", "apps": "Apps", "app-version": "App version", "api-version": "API Version", "failed-to-save-item": "Failed to save the record", "validation-errors-found": "Errors found during validation process", "instruction": "Instruction", "month": "Month", "year": "Year", "retry": "Retry", "back": "Back", "next": "Next", "previous": "Previous", "select": "Select", "selected": "Selected", "selected-user": "Selected User", "page-of": "Page {current} of {total}", "creating": "Creating", "actions": "Actions", "email": "Email", "full-address": "Full address", "toggle-theme": "Toggle theme", "get-support": "Get Support", "all": "All", "items-per-page": "Items per page"}, "monitoring": {"title": "Monitoring"}, "register": {"account-create-desc": "To create a new account, please fill out the form below", "account-create-title": "Create an account", "confirm-password": "Repeat password", "create-account": "Create account", "email-verified": "Email address verified successfully", "failed-to-register": "An error occurred during registration process. Please try again later.", "failed-verify": "Failed to verify email address", "success-register": "Registration successfull", "success-register-desc": "Please confirm your email address to complete the registration", "verifying-email": "Verifying email address", "mail-already-used": "This e-mail is already being used for other account."}, "reports": {"title": "Reports"}, "role-management": {"delete-role-action": "Are you sure you want to delete role {name}?", "edit-roles-failed": "Changes were not saved. Server side error.", "failed-create-role": "Failed to create role. Server side error.", "failed-delete-role": "Failed to delete role.", "role": "Role", "role-name": "Role name", "title": "Role management"}, "settings": {"change-password": "Change password", "pw-change": "Password change", "choose-platform": "Choose platform", "personal-data": "Personal data", "personal": {"updated": "Personal data were updated", "update-failed": "Failed to update personal data"}}, "user": {"delete-user": {"modal-desc": "This action cannot be undone. This will permanently delete the account and remove data from our servers.", "title": "Are you sure you want to delete user {name}?"}, "my-account": "My account"}, "support": {"title": "Get Support", "description": "Contact us for help or send us your suggestions and bug reports.", "form": {"title": "Contact Us", "type": "Type", "suggestion": "Suggestion", "bug": "Bug Report", "subject": "Subject", "description": "Description", "file": "Attachment", "file-limit": "Maximum file size: 20MB", "file-too-large": "File is too large. Maximum size is 20MB.", "submit": "Send Message", "success": "Your message has been sent successfully. We will get back to you soon.", "error": "Failed to send message. Please try again later."}, "contact": {"title": "Contact Information", "company": "Company", "address": "Address", "phone": "Phone", "email": "Email"}}, "user-management": {"actions": "Actions", "contact": "Contact", "create-user": "Create user", "create-user-desc": "Provide user details. When you click save, an email with activation link will be sent to user which then can choose his password.", "delete-user": "Delete user", "delete-user-action": "Are you sure you want to delete user {name}?", "description": "Manage users, their roles, and permissions in your organization.", "edit-failed": "Changes were not saved. Server side error.", "edit-user": "Edit user", "employee-id": "Employee ID", "loading": "Loading users...", "no-contact-info": "No contact info", "no-permissions": "No permissions", "no-roles": "No roles", "no-users": "No users found", "permissions": "Permissions", "permissions-count": "permissions", "roles": "Roles", "search-placeholder": "Search users by name, email...", "success-delete-user": "Successfully removed user", "title": "User management", "user": "User", "user-create-fail": "An error ocurred when creating new user", "user-create-success": "New user has been created", "user-delete-fail": "Failed to delete user", "user-update-success": "User data has been updated", "user-update-fail": "Failed to update user data", "filter-by-role": "Filter by role", "organization": "Organization"}, "tuya": {"scan-qr-instruction": "Scan the QR code using the Tuya Smart app", "waiting-for-auth": "Waiting for confirmation...", "auth-success": "Authentication successful!", "auth-failed": "Authentication failed or timed out."}, "device-detail": {"basic-information": "Basic Information", "identifier": "Identifier", "device-type": "Device type", "vendor": "<PERSON><PERSON><PERSON>", "model": "Model", "serial-number": "Serial number", "all-metrics": "All Metrics", "last-update": "Last update:", "history-charts": "History charts", "start": "Start:", "end": "End:", "aggregation": "Aggregation:", "no-aggregation": "No aggregation", "select-metric": "Select a metric", "average": "Average", "remove": "Remove", "add-metric": "Add Metric", "select-metric-message": "Please, select a metric to display the chart", "add-chart": "Add Chart", "aggregations": {"5_minutes": "5 minutes", "10_minutes": "10 minutes", "30_minutes": "30 minutes", "1_hour": "1 hour", "2_hours": "2 hours", "3_hours": "3 hours", "6_hours": "6 hours", "12_hours": "12 hours", "1_day": "1 day", "1_week": "1 week", "1_month": "1 month"}, "configuration": "Device configuration", "config-submitted-successfully": "Configuration sucessfully sent to device", "send-to-device": "Send to device", "configuration-not-available": "Configuration not available for this device.", "clicking-means-send-instruction": "Clicking on this button will send the instruction to device"}, "admin": {"installations": {"title": "Installation management", "description": "Manage all installations in the system", "loading": "Loading installations...", "access-denied": "Access denied", "access-denied-title": "Access Denied", "access-denied-message": "You don't have permission to view admin installations.", "fetch-error": "Failed to load installations", "error-title": "Error Loading Data", "error-message": "There was an error loading the installations. Please try again.", "no-installations": "No installations found", "no-installations-description": "There are currently no installations in the system.", "no-location": "No location", "unknown": "Unknown", "view-details": "View details", "classification": {"on_grid": "On-grid", "hybrid": "Hybrid"}, "table": {"title": "Installations List", "description": "View and manage all installations in the system", "installation": "Installation", "location": "Location", "type": "Type", "pv-capacity": "PV Capacity", "battery-capacity": "Battery Capacity", "devices": "Devices", "shared": "Shared", "actions": "Actions", "customer": "Customer", "battery": "Battery"}, "stats": {"total-installations": "Total Installations", "installations-description": "Active installations in system", "total-pv-capacity": "Total PV Capacity", "pv-capacity-description": "Combined solar capacity", "total-battery-capacity": "Total Battery Capacity", "battery-capacity-description": "Combined battery storage", "total-shared-users": "Shared Users", "shared-users-description": "Users with shared access"}, "pagination": {"showing": "Showing {from} to {to} of {total} installations"}, "search-placeholder": "Search installations...", "create-new": "New installation", "no-address": "No address", "goto": "Navigate to installations admin", "created-at": "Created"}, "new-installation": {"title": "New Installation Wizard", "description": "Create a new installation by following these simple steps", "step1": {"title": "Select User Type", "description": "Choose whether to create an installation for an existing user or create a new user account.", "existing-user": {"title": "Existing User", "description": "Select from existing users in the system", "button": "Choose Existing User"}, "new-user": {"title": "Create New User", "description": "Create a new user account for this installation", "button": "Create New User"}}, "step2a": {"title": "Select Existing User", "description": "Search and select an existing user from the list below.", "search-placeholder": "Search users by name or email...", "no-results": "No users found matching your search.", "no-users": "No users available in the system."}, "step2b": {"title": "Create New User", "description": "Fill in the details to create a new user account. A password will be generated and sent to the user's email.", "create-user": "Create User", "user-created-success": "User created successfully!", "user-creation-failed": "Failed to create user. Please try again.", "created-user": "Created User", "create-another": "Create Another User", "note": "* Required fields. The user will receive login credentials via email.", "success": {"title": "User Created Successfully!", "description": "The new user account has been created and is ready to use."}}, "step3": {"title": "Create Installation", "description": "Fill in the installation details for the selected user.", "selected-user": "Installation will be created for", "create-installation": "Create Installation", "creating": "Creating Installation", "installation-created-success": "Installation created successfully!", "installation-creation-failed": "Failed to create installation. Please try again."}, "step4": {"title": "Installation Created Successfully!"}, "success": {"title": "Success!", "description": "The installation has been created successfully and is now available in the system.", "back-to-list": "Back to Installations", "view-installation": "View Installation"}}}, "service": {"title": "Service Management", "installations": {"title": "Installation Management"}, "new-installation": {"title": "New Installation"}, "title-short": "Service"}}