import type { CustomWindow } from '@/../typings/window';

export enum CustomEvents {
  FETCHED_INSTALLATION_DATA = 'FETCHED_INSTALLATION_DATA',
  SERVICE_UPDATED_INSTALLATION = 'SERVICE_UPDATED_INSTALLATION',
}

export const sendCustomEvent = (payload: Record<string, string>, name: CustomEvents) => {
  const event = new CustomEvent(name, { detail: payload });
  (window as CustomWindow).evB.dispatchEvent(event);
};
