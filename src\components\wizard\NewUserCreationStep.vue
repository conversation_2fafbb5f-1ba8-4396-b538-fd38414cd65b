<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Check } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';
import type { UserData } from '@/stores/auth-store';
import PageLoader from '@/components/global/PageLoader.vue';
import GreenCheck from '@/components/states/GreenCheck.vue';

enum UserCreationState {
  IDLE,
  LOADING,
  SUCCESS,
  ERROR,
}

const emit = defineEmits<{
  'user-created': [user: UserData];
}>();

const { t } = useI18n();
const componentState = ref(ComponentStateType.OK);
const userCreationState = ref(UserCreationState.IDLE);
const createdUser = ref<UserData | null>(null);

const credentials = reactive({
  name: '',
  surname: '',
  phone: '',
  email: '',
});

const onCreateUser = async() => {
  if (userCreationState.value === UserCreationState.LOADING) {
    return;
  }

  userCreationState.value = UserCreationState.LOADING;
  componentState.value = ComponentStateType.LOADING;

  try {
    const response = await customAxios.post<{ data: UserData }>('/service/users', {
      name: credentials.name,
      surname: credentials.surname,
      phone: credentials.phone,
      email: credentials.email,
    });

    createdUser.value = response.data.data;
    userCreationState.value = UserCreationState.SUCCESS;
    componentState.value = ComponentStateType.OK;

    emit('user-created', response.data.data);

    deployToast(ToastType.SUCCESS, {
      text: t('admin.new-installation.step2b.user-created-success'),
      timeout: 6000,
    });
  } catch (e: any) {
    userCreationState.value = UserCreationState.ERROR;
    componentState.value = ComponentStateType.OK;

    if (e.response?.status === 422 && e.response.data?.message && e.response.data.message === 'validation.unique') {
      deployToast(ToastType.ERROR, {
        text: t('register.mail-already-used'),
        timeout: 6000,
      });
      return;
    }

    deployToast(ToastType.ERROR, {
      text: t('admin.new-installation.step2b.user-creation-failed'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <div class="space-y-6">
    <p class="text-muted-foreground">
      {{ $t('admin.new-installation.step2b.description') }}
    </p>

    <div
      v-if="componentState === ComponentStateType.LOADING"
      class="flex justify-center py-8"
    >
      <PageLoader />
    </div>

    <div
      v-else-if="userCreationState === UserCreationState.SUCCESS && createdUser"
      class="text-center space-y-4"
    >
      <GreenCheck class="w-16 h-16 mx-auto" />
      <div>
        <h3 class="text-lg font-semibold text-green-600">
          {{ $t('admin.new-installation.step2b.success.title') }}
        </h3>
        <p class="text-muted-foreground mt-2">
          {{ $t('admin.new-installation.step2b.success.description') }}
        </p>
      </div>

      <div class="mt-6 p-4 bg-green-400/10 border border-green-800/20 rounded-lg text-left">
        <div class="flex items-center space-x-2 mb-2">
          <Check class="h-4 w-4 text-green-600" />
          <span class="font-medium text-green-500">{{ $t('admin.new-installation.step2b.created-user') }}:</span>
        </div>
        <div class="ml-6 space-y-1">
          <p class="font-medium">
            {{ [createdUser.name, createdUser.surname].filter(Boolean).join(' ') }}
          </p>
          <p class="text-sm text-muted-foreground">
            {{ createdUser.email }}, {{ createdUser.phone }}
          </p>
        </div>
      </div>
    </div>

    <form
      v-else
      autocomplete="off"
      class="space-y-4"
      @submit.prevent="onCreateUser"
    >
      <div class="grid gap-2">
        <Label for="new-user-name">
          {{ $t('misc.name') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-name"
          v-model="credentials.name"
          name="name"
          autocomplete="off"
          type="text"
          required
          :disabled="userCreationState === UserCreationState.LOADING"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-surname">
          {{ $t('misc.surname') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-surname"
          v-model="credentials.surname"
          name="surname"
          autocomplete="off"
          type="text"
          required
          :disabled="userCreationState === UserCreationState.LOADING"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-phone">
          {{ $t('misc.phone') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-phone"
          v-model="credentials.phone"
          name="phone"
          autocomplete="off"
          type="tel"
          required
          :disabled="userCreationState === UserCreationState.LOADING"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-email">
          {{ $t('misc.email') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-email"
          v-model="credentials.email"
          name="email"
          autocomplete="email"
          type="email"
          required
          :disabled="userCreationState === UserCreationState.LOADING"
        />
      </div>

      <div class="pt-4">
        <Button
          type="submit"
          class="w-full"
          :disabled="userCreationState === UserCreationState.LOADING"
        >
          <span v-if="userCreationState === UserCreationState.LOADING">
            {{ $t('misc.creating') }}...
          </span>
          <span v-else>
            {{ $t('admin.new-installation.step2b.create-user') }}
          </span>
        </Button>
      </div>

      <div class="text-xs text-muted-foreground">
        <p>{{ $t('admin.new-installation.step2b.note') }}</p>
      </div>
    </form>
  </div>
</template>
