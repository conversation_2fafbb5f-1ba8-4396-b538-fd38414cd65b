<script lang="ts" setup>
import { useForwardPropsEmits } from 'radix-vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/shadcn-components/ui/popover';
import type { PopoverRootEmits, PopoverRootProps } from 'radix-vue';

type Props = PopoverRootProps & {
  align?: 'start' | 'center' | 'end' | undefined
  contentClasses?: string,
}

const props = defineProps<Props>();
const emits = defineEmits<PopoverRootEmits>();

const forwarded = useForwardPropsEmits(props, emits);
</script>

<template>
  <Popover v-bind="forwarded">
    <PopoverTrigger>
      <slot name="popover-trigger" />
    </PopoverTrigger>
    <PopoverContent
      side="top"
      :align="align"
      :class="contentClasses"
    >
      <slot name="popover-content" />
    </PopoverContent>
  </Popover>
</template>
