// import { ChartNoAxesCombined, Home, Lock, MonitorCheck, TriangleAlert, UserRoundCog } from 'lucide-vue-next';
import { Home, Lock, UserRoundCog, Building2 } from 'lucide-vue-next';
import { routeMap } from '@/router/routes';
import { AvailablePermissions } from '@/util/types/roles-and-permissions';
import type { FunctionalComponent } from 'vue';

export interface NavLink {
  name: string,
  icon: FunctionalComponent,
  i18nTitle: string,
  neededPermissions?: AvailablePermissions[],
  permissionMode?: 'all' | 'any' // Default is 'all' for backward compatibility
}

export interface NavLinks {
  top: NavLink[],
  bottom: NavLink[],
}

export const navLinks: NavLinks = {
  top: [
    {
      name: routeMap.home.children.dashboardInstallations.name,
      icon: Home,
      i18nTitle: routeMap.home.meta.i18nTitle,
    },
    {
      name: routeMap.service.children.installations.name,
      icon: Building2,
      i18nTitle: routeMap.service.children.installations.meta.i18nTitle,
      neededPermissions: [AvailablePermissions.SERVICE_INSTALLATION],
      permissionMode: 'any',
    },
    // {
    //   name: routeMap.reports.name,
    //   icon: ChartNoAxesCombined,
    //   i18nTitle: routeMap.reports.meta.i18nTitle,
    // },
    // {
    //   name: routeMap.alerts.name,
    //   icon: TriangleAlert,
    //   i18nTitle: routeMap.alerts.meta.i18nTitle,
    // },
    // {
    //   name: routeMap.monitoring.name,
    //   icon: MonitorCheck,
    //   i18nTitle: routeMap.monitoring.meta.i18nTitle,
    // },
  ],
  bottom: [
    {
      name: routeMap.management.children.users.name,
      icon: UserRoundCog,
      i18nTitle: routeMap.management.children.users.meta.i18nTitle,
      neededPermissions: [AvailablePermissions.USER_MANAGE],
    },
    {
      name: routeMap.management.children.roles.name,
      icon: Lock,
      i18nTitle: routeMap.management.children.roles.meta.i18nTitle,
      neededPermissions: [AvailablePermissions.ROLE_MANAGE],
    },
  ],
} satisfies NavLinks;
