<script lang="ts" setup>
import { Settings } from 'lucide-vue-next';
import { type NavLink, navLinks } from '@/router/navbar';
import { routeMap } from '@/router/routes';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shadcn-components/ui/tooltip';
import { useAuthStore } from '@/stores/auth-store';

const authStore = useAuthStore();
const userPermissions = authStore.user?.permissions ?? [];

const filterFunction = (link: NavLink) => {
  if (!link.neededPermissions) {return true;}

  if (link.permissionMode === 'any') {
    return link.neededPermissions.some(permission => userPermissions.includes(permission));
  }

  // Default behavior: all permissions required
  return link.neededPermissions.every(permission => userPermissions.includes(permission));
};

const topNavLinks = navLinks.top.filter(filterFunction);
const bottomNavLinks = navLinks.bottom.filter(filterFunction);
</script>

<template>
  <aside class="fixed inset-y-0 left-0 z-10 hidden w-14 flex-col border-r border-prim-col-foreground-2 bg-prim-col-1 sm:flex">
    <nav class="flex flex-col items-center gap-4 px-2 sm:py-5">
      <TooltipProvider
        v-for="navLink in topNavLinks"
        :key="navLink.name"
        :delay-duration="0"
      >
        <Tooltip>
          <TooltipTrigger as-child>
            <router-link
              active-class="bg-prim-col-foreground-2 [&_svg]:text-white!"
              class="group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full text-lg hover:bg-prim-col-foreground-2/50 [&_svg]:text-black/50 dark:[&_svg]:text-muted-foreground font-semibold md:h-8 md:w-8 md:text-base transition-all duration-150"
              :to="{ name: navLink.name }"
            >
              <component
                :is="navLink.icon"
                class="h-5 w-5 text-white"
              />
              <span class="sr-only">{{ $t(navLink.i18nTitle) }}</span>
            </router-link>
          </TooltipTrigger>
          <TooltipContent side="right">
            {{ $t(navLink.i18nTitle) }}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </nav>
    <nav class="mt-auto flex flex-col items-center gap-4 px-2 sm:py-5">
      <TooltipProvider
        v-for="navLink in bottomNavLinks"
        :key="navLink.name"
        :delay-duration="0"
      >
        <Tooltip>
          <TooltipTrigger as-child>
            <router-link
              active-class="bg-prim-col-foreground-2 [&_svg]:text-white!"
              class="group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full text-lg hover:bg-prim-col-foreground-2/50 [&_svg]:text-black/50 dark:[&_svg]:text-muted-foreground font-semibold md:h-8 md:w-8 md:text-base transition-all duration-150"
              :to="{ name: navLink.name }"
            >
              <component
                :is="navLink.icon"
                class="h-5 w-5 text-white"
              />
              <span class="sr-only">{{ $t(navLink.i18nTitle) }}</span>
            </router-link>
          </TooltipTrigger>
          <TooltipContent side="right">
            {{ $t(navLink.i18nTitle) }}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider :delay-duration="0">
        <Tooltip>
          <TooltipTrigger as-child>
            <router-link
              active-class="bg-prim-col-foreground-2 [&_svg]:text-white!"
              class="group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full text-lg hover:bg-prim-col-foreground-2/50 [&_svg]:text-black/50 dark:[&_svg]:text-muted-foreground font-semibold md:h-8 md:w-8 md:text-base transition-all duration-150"
              :to="{ name: routeMap.settings.name }"
            >
              <Settings class="h-5 w-5 text-white" />
              <span class="sr-only">{{ $t('misc.settings') }}</span>
            </router-link>
          </TooltipTrigger>
          <TooltipContent side="right">
            {{ $t('misc.settings') }}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </nav>
  </aside>
</template>
