<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { ExternalLink, MapPin, Zap, Battery, X, ServerIcon, Pencil } from 'lucide-vue-next';
import { Badge } from '@/shadcn-components/ui/badge';
import { Button } from '@/shadcn-components/ui/button';
import { routeMap } from '@/router/routes';
import type { InstallationServiceDetailData } from '@/pages/installation/types/installation-types';
import { useI18n } from 'vue-i18n';
import { reactive } from 'vue';
import CreateOrEditInstallationDialog from '@/pages/dashboard/CreateOrEditInstallationDialog.vue';
import PageLoader from '../global/PageLoader.vue';
import { DateTime } from 'luxon';

interface Props {
  installations: InstallationServiceDetailData[];
  loading?: boolean;
}

withDefaults(defineProps<Props>(), {
  loading: false,
});

const router = useRouter();
const { t } = useI18n();
const modals = reactive({
  editInstallation: {
    isOpened: false,
    id: '',
  },
});

const navigateToInstallation = (installationId: string) => {
  router.push({
    name: routeMap.home.children.installation.name,
    params: { installationId },
  });
};

const formatCapacity = (capacity: number | null, unit: string = 'kW') => {
  if (capacity === null || capacity === undefined) {return null;}
  return `${capacity} ${unit}`;
};

const getClassificationBadgeVariant = (classification: string | null) => {
  switch (classification) {
    case 'on_grid':
      return 'default';
    case 'hybrid':
      return 'secondary';
    default:
      return 'outline';
  }
};

const hasDevices = (installation: InstallationServiceDetailData) => {
  return installation.deviceInstances && installation.deviceInstances.length > 0;
};

const getDeviceCount = (installation: InstallationServiceDetailData) => {
  return installation.deviceInstances?.length || 0;
};

const showEditInstallationModal = (id: string) => {
  modals.editInstallation.id = id;
  modals.editInstallation.isOpened = true;
};
</script>

<template>
  <div class="w-full">
    <div class="rounded-xl border dark:border-[var(--color-border)] border-gray-300/70 overflow-x-auto">
      <!-- Header -->
      <div class="grid grid-cols-[minmax(6rem,1fr)_minmax(6rem,1fr)_minmax(10rem,1fr)_9rem_6rem_6rem_6rem_6rem_6rem_6rem] gap-4 p-4 dark:bg-muted/50 bg-prim-col-foreground-1 border-b dark:border-black border-gray-300/70 font-medium text-sm will-change-transform min-w-full w-fit">
        <div class="w-full">
          {{ $t('admin.installations.table.customer') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.table.installation') }}
        </div>
        <div class="w-full">
          {{ $t('misc.full-address') }}
        </div>
        <div class="w-full">
          {{ $t('user-management.organization') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.created-at') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.table.type') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.table.pv-capacity') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.table.battery') }}
        </div>
        <div class="w-full">
          {{ $t('admin.installations.table.devices') }}
        </div>
        <div class="w-full text-right">
          {{ $t('admin.installations.table.actions') }}
        </div>
      </div>

      <!-- Installation rows -->
      <div class="divide-y">
        <router-link
          v-for="installation in installations"
          :key="installation.id"
          :to="{ name: routeMap.home.children.installation.name, params: { installationId: installation.id } }"
          class="grid grid-cols-[minmax(6rem,1fr)_minmax(6rem,1fr)_minmax(10rem,1fr)_9rem_6rem_6rem_6rem_6rem_6rem_6rem] gap-4 p-4 hover:bg-prim-col-selected-1/10 transition-colors items-center min-w-full w-fit text-black dark:text-white dark:border-black border-gray-300/70"
        >
          <div class="font-medium flex items-center">
            <span class="font-semibold line-clamp-1 inline-block break-all">
              {{ installation.owners?.map(owner => [owner.name, owner.surname].filter(Boolean).join(' '))?.join(', ') ?? '-' }}
            </span>
            <span v-if="!installation.owners?.length" class="font-semibold line-clamp-1 inline-block break-all text-black dark:text-white">
              -
            </span>
          </div>

          <div class="font-medium flex items-center">
            <div class="flex flex-col">
              <span class="font-semibold break-all">
                {{ installation.title }}
              </span>
            </div>
          </div>

          <div class="">
            <div class="flex items-center gap-1">
              <MapPin class="h-4 w-4" />
              <span class="text-sm">
                {{ installation.full_address || t('admin.installations.no-address') }}
              </span>
            </div>
            <template v-if="!installation.full_address && installation.locality">
              <span class="text-sm opacity-60">
                ({{ $t('admin.installations.table.location') }} {{ installation.locality }})
              </span>
            </template>
          </div>

          <div class="text-black dark:text-white">
            <span v-if="installation.organization">
              {{ installation.organization.name }}
            </span>
            <span v-else>-</span>
          </div>

          <div class="text-black dark:text-white">
            <span v-if="installation.created_at">
              {{ DateTime.fromISO(installation.created_at).toFormat('dd.MM.yyyy') }}
            </span>
            <span v-else>-</span>
          </div>

          <div class="">
            <Badge :variant="getClassificationBadgeVariant(installation.classification)" class="text-black dark:text-white capitalize">
              {{ installation.classification ?? $t('admin.installations.unknown') }}
            </Badge>
          </div>

          <div class="">
            <div class="flex items-center gap-1">
              <template v-if="formatCapacity(installation.pv_capacity)">
                <Zap class="h-4 w-4 text-yellow-500" />
                <span class="font-mono text-sm">
                  {{ formatCapacity(installation.pv_capacity) }}
                </span>
              </template>
              <X v-else class="h-4 w-4 text-red-500" />
            </div>
          </div>

          <div class="">
            <div class="flex items-center gap-1">
              <template v-if="installation.battery_capacity">
                <Battery class="h-4 w-4 text-green-500" />
                <span class="font-mono text-sm">
                  {{ formatCapacity(installation.battery_capacity, 'kWh') }}
                </span>
              </template>
              <X v-else class="h-4 w-4 text-red-500" />
            </div>
          </div>

          <div class="">
            <div class="flex items-center gap-1">
              <template v-if="hasDevices(installation)">
                <ServerIcon class="h-4 w-4 text-gray-400" />
                <span class="text-sm font-mono">
                  {{ getDeviceCount(installation) }}
                </span>
              </template>
              <X v-else class="h-4 w-4 text-red-500" />
            </div>
          </div>

          <div class="text-right flex justify-end items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              class="h-8 w-8 p-0"
              :title="$t('installation.edit-installation')"
              @click.prevent.stop="showEditInstallationModal(installation.id)"
            >
              <Pencil class="h-4 w-4" />
              <span class="sr-only">
                {{ $t('installation.edit-installation') }}
              </span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              class="h-8 w-8 p-0"
              @click.prevent.stop="navigateToInstallation(installation.id)"
            >
              <ExternalLink class="h-4 w-4" />
              <span class="sr-only">
                {{ $t('admin.installations.view-details') }}
              </span>
            </Button>
          </div>
        </router-link>
      </div>

      <!-- Empty states -->
      <div
        v-if="installations.length === 0 && !loading"
        class="text-center py-8"
      >
        <div class="text-muted-foreground mb-2">
          {{ $t('admin.installations.no-installations') }}
        </div>
      </div>

      <div
        v-if="loading"
        class="text-center py-8"
      >
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2" />
        <div class="text-muted-foreground">
          {{ $t('admin.installations.loading') }}
        </div>
      </div>
    </div>

    <suspense v-if="modals.editInstallation.isOpened">
      <CreateOrEditInstallationDialog
        v-model="modals.editInstallation.isOpened"
        :installation-id="modals.editInstallation?.id"
        :is-edit-dialog="true"
        :send-custom-event-after-submit="true"
      />
      <template #fallback>
        <teleport to="body">
          <div class="fixed w-screen h-dvh bg-black/60 z-50 top-0 left-0">
            <PageLoader :absolute-center="true" />
          </div>
        </teleport>
      </template>
    </suspense>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
