<template>
  <g>
    <Arc
      v-if="inactive"
      :radius="radius"
      :thickness="thickness"
      :offset-y="offsetY"
      :offset-x="offsetX"
      :transition-delay="transitionDelay"
      :stroke-width="inactiveStrokeWidth"
      :stroke="inactiveStroke"
      :fill="inactiveFill"
      :start-angle="outerAngle"
      :start-inner-angle="innerAngleAdjustment"
      :end-angle="180"
    />

    <Arc
      v-if="active"
      :radius="radius"
      :thickness="thickness"
      :offset-y="offsetY"
      :offset-x="offsetX"
      :transition-delay="transitionDelay"
      :stroke-width="activeStrokeWidth"
      :stroke="activeStroke"
      :fill="activeFill"
      :start-angle="180"
      :end-angle="360 - outerAngle"
      :end-inner-angle="360 - innerAngleAdjustment"
    />
  </g>
</template>

<script lang="ts">
import Arc from './Arc.vue';
import { innerAnglePointerAdjustment } from '@/components/gauge/lib/chart';
import styleProps from '@/components/gauge/lib/svgStyleProps';

export default {
  components: {
    Arc,
  },
  props: {
    thickness: {
      type: Number,
      required: true,
    },
    radius: {
      type: Number,
      required: true,
    },
    offsetX: {
      type: Number,
      default: 0,
    },
    offsetY: {
      type: Number,
      default: 0,
    },
    angle: {
      type: Number,
      default: 4,
    },
    transitionDelay: {
      type: Number,
      required: false,
      default: 0,
    },
    active: {
      type: Boolean,
      default: true,
    },
    ...styleProps('active', { fill: 'currentcolor' }),
    inactive: {
      type: Boolean,
      default: true,
    },
    ...styleProps('inactive', { fill: 'currentcolor' }),
  },
  computed: {
    innerRadius() {
      return this.radius - this.thickness;
    },
    outerAngle() {
      return this.angle;
    },
    innerAngleAdjustment() {
      return innerAnglePointerAdjustment(this.outerAngle, this.radius, this.innerRadius);
    },
  },
};
</script>
