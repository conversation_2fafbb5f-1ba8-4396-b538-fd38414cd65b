<script setup lang="ts">
import { Users, UserPlus } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';

enum UserSelectionType {
  EXISTING = 'existing',
  NEW = 'new',
}

interface Props {
  selectedType: UserSelectionType | null;
}

defineProps<Props>();

const emit = defineEmits<{
  'selection-changed': [type: UserSelectionType];
}>();

const selectType = (type: UserSelectionType) => {
  emit('selection-changed', type);
};
</script>

<template>
  <div class="space-y-6">
    <p class="text-muted-foreground">
      {{ $t('admin.new-installation.step1.description') }}
    </p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Existing User Option -->
      <div
        class="border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md"
        :class="{
          'dark:border-primary border-black bg-primary/10': selectedType === UserSelectionType.EXISTING,
          'dark:border-border border-black/10': selectedType !== UserSelectionType.EXISTING,
        }"
        @click="selectType(UserSelectionType.EXISTING)"
      >
        <div class="flex flex-col items-center text-center space-y-4">
          <div
            class="w-16 h-16 rounded-full flex items-center justify-center"
            :class="{
              'bg-primary text-primary-foreground': selectedType === UserSelectionType.EXISTING,
              'bg-muted text-muted-foreground': selectedType !== UserSelectionType.EXISTING,
            }"
          >
            <Users class="h-8 w-8" />
          </div>
          <div>
            <h3 class="text-lg font-semibold">
              {{ $t('admin.new-installation.step1.existing-user.title') }}
            </h3>
            <p class="text-sm text-muted-foreground mt-2">
              {{ $t('admin.new-installation.step1.existing-user.description') }}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            :class="{
              'border-primary text-primary': selectedType === UserSelectionType.EXISTING,
            }"
          >
            {{ $t('admin.new-installation.step1.existing-user.button') }}
          </Button>
        </div>
      </div>

      <!-- New User Option -->
      <div
        class="border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md"
        :class="{
          'dark:border-primary border-black bg-primary/10': selectedType === UserSelectionType.NEW,
          'dark:border-border border-black/10': selectedType !== UserSelectionType.NEW,
        }"
        @click="selectType(UserSelectionType.NEW)"
      >
        <div class="flex flex-col items-center text-center space-y-4">
          <div
            class="w-16 h-16 rounded-full flex items-center justify-center"
            :class="{
              'bg-primary text-primary-foreground': selectedType === UserSelectionType.NEW,
              'bg-muted text-muted-foreground': selectedType !== UserSelectionType.NEW,
            }"
          >
            <UserPlus class="h-8 w-8" />
          </div>
          <div>
            <h3 class="text-lg font-semibold">
              {{ $t('admin.new-installation.step1.new-user.title') }}
            </h3>
            <p class="text-sm text-muted-foreground mt-2">
              {{ $t('admin.new-installation.step1.new-user.description') }}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            :class="{
              'border-primary text-primary': selectedType === UserSelectionType.NEW,
            }"
          >
            {{ $t('admin.new-installation.step1.new-user.button') }}
          </Button>
        </div>
      </div>
    </div>

    <div
      v-if="selectedType"
      class="mt-6 p-4 dark:bg-muted/50 bg-gray-100 rounded-lg"
    >
      <p class="text-sm">
        <strong>{{ $t('misc.selected') }}:</strong>
        {{
          selectedType === UserSelectionType.EXISTING
            ? $t('admin.new-installation.step1.existing-user.title')
            : $t('admin.new-installation.step1.new-user.title')
        }}
      </p>
    </div>
  </div>
</template>
