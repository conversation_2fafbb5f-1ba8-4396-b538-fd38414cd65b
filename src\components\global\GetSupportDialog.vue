<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { Textarea } from '@/shadcn-components/ui/textarea';
import { useAuthStore } from '@/stores/auth-store';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { formDataFromObject } from '@/util/facades/forms';
import { XIcon } from 'lucide-vue-next';

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const authStore = useAuthStore();

const supportData = reactive({
  name: authStore.user?.name || '',
  email: '',
  phone: '',
  type: '',
  subject: '',
  description: '',
  file: null as File | null,
});

const isSubmitting = ref(false);

const onSubmit = async() => {
  if (isSubmitting.value) {
    return;
  }

  isSubmitting.value = true;
  try {
    await grecaptcha.ready(() => undefined);
    const token = await grecaptcha.execute('6LeisvArAAAAAPZf4304e8fU_wOaQYcJmv3ZLMRQ', { action: 'submit' });
    const data = formDataFromObject(supportData);
    data.append('g-recaptcha-response', token);
    await customAxios.post('/support/contact', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    // Reset form
    Object.assign(supportData, {
      name: authStore.user?.name || '',
      email: '',
      phone: '',
      type: '',
      subject: '',
      description: '',
      file: null,
    });

    isOpened.value = false;
    deployToast(ToastType.SUCCESS, {
      text: t('support.form.success'),
      timeout: 6000,
    });
  } catch (e) {
    console.error(e);
    deployToast(ToastType.ERROR, {
      text: t('support.form.error'),
      timeout: 6000,
    });
  } finally {
    isSubmitting.value = false;
  }
};

const onFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file && file.size > 20 * 1024 * 1024) {
    deployToast(ToastType.ERROR, {
      text: t('support.form.file-too-large'),
      timeout: 6000,
    });
    target.value = '';
    return;
  }
  supportData.file = file || null;
};
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      class=""
      :style="{margin: 'auto'}"
    >
      <div class="bg-prim-col-1 p-6 rounded-2xl w-[60rem] max-w-[90vw] max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-bold text-xl">
            {{ $t('support.title') }}
          </h3>
          <button class="cursor-pointer" @click="isOpened = false;">
            <XIcon />
          </button>
        </div>
        <p class="text-black/50 dark:text-white/60 text-sm mb-6">
          {{ $t('support.description') }}
        </p>

        <div class="grid grid-cols-1 gap-4">
          <!-- Contact Information -->
          <div>
            <h4 class="font-semibold text-lg mb-4">
              {{ $t('support.contact.title') }}
            </h4>
            <div class="bg-prim-col-foreground-1/20 p-4 rounded-lg grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 md:grid-cols-4 flex-wrap items-center">
              <div>
                <div class="font-medium text-sm">
                  {{ $t('support.contact.company') }}
                </div>
                <div class="text-sm">
                  ANTIK Telecom s.r.o
                </div>
              </div>
              <div>
                <div class="font-medium text-sm">
                  {{ $t('support.contact.address') }}
                </div>
                <div class="text-sm">
                  Čárskeho 10, 04001, Košice
                </div>
              </div>
              <div>
                <div class="font-medium text-sm">
                  {{ $t('support.contact.phone') }}
                </div>
                <a href="tel:+421 55 30 76527" target="_blank" class="block text-sm hover:underline">
                  +421 55 30 76527
                </a>
              </div>
              <div>
                <div class="font-medium text-sm">
                  {{ $t('support.contact.email') }}
                </div>
                <a href="mailto:<EMAIL>" target="_blank" class="text-sm hover:underline">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
          <div>
            <h4 class="font-semibold text-lg mb-4">
              {{ $t('support.form.title') }}
            </h4>

            <form
              autocomplete="off"
              class="space-y-4"
              @submit.prevent="onSubmit"
            >
              <div class="flex items-center gap-4">
                <Label
                  for="support-name"
                  class="text-right w-20 min-w-20"
                >
                  {{ $t('misc.name') }} <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="support-name"
                  v-model="supportData.name"
                  name="name"
                  type="text"
                  required
                  :disabled="isSubmitting"
                />
              </div>

              <div class="flex items-center gap-4">
                <Label
                  for="support-email"
                  class="text-right w-20 min-w-20"
                >
                  {{ $t('misc.email') }} <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="support-email"
                  v-model="supportData.email"
                  name="email"
                  type="email"
                  required
                  :disabled="isSubmitting"
                />
              </div>

              <div class="flex items-center gap-4">
                <Label
                  for="support-phone"
                  class="text-right w-20 min-w-20"
                >
                  {{ $t('misc.phone') }}
                </Label>
                <Input
                  id="support-phone"
                  v-model="supportData.phone"
                  name="phone"
                  type="tel"
                  :disabled="isSubmitting"
                />
              </div>

              <div class="flex items-center gap-4">
                <Label
                  for="support-type"
                  class="text-right w-20 min-w-20"
                >
                  {{ $t('support.form.type') }} <span class="text-red-500">*</span>
                </Label>
                <select
                  id="support-type"
                  v-model="supportData.type"
                  name="type"
                  required
                  :disabled="isSubmitting"
                  class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm outline-hidden"
                >
                  <option value="" disabled>
                    {{ $t('misc.select-option') }}
                  </option>
                  <option value="suggestion">
                    {{ $t('support.form.suggestion') }}
                  </option>
                  <option value="bug">
                    {{ $t('support.form.bug') }}
                  </option>
                </select>
              </div>

              <div class="flex items-center gap-4">
                <Label
                  for="support-subject"
                  class="text-right w-20 min-w-20"
                >
                  {{ $t('support.form.subject') }} <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="support-subject"
                  v-model="supportData.subject"
                  name="subject"
                  type="text"
                  required
                  :disabled="isSubmitting"
                />
              </div>

              <div class="flex items-start gap-4">
                <Label
                  for="support-description"
                  class="text-right w-20 min-w-20 mt-2"
                >
                  {{ $t('support.form.description') }} <span class="text-red-500">*</span>
                </Label>
                <Textarea
                  id="support-description"
                  v-model="supportData.description"
                  name="description"
                  required
                  :rows="5"
                  :disabled="isSubmitting"
                  class="resize-none"
                />
              </div>

              <div>
                <div class="flex items-center gap-4">
                  <Label
                    for="support-file"
                    class="text-right w-20 min-w-20"
                  >
                    {{ $t('support.form.file') }}
                  </Label>
                  <input
                    id="support-file"
                    name="file"
                    type="file"
                    accept="*/*"
                    :disabled="isSubmitting"
                    class="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-secondary file:text-secondary-foreground hover:file:bg-secondary/80"
                    @change="onFileChange"
                  >
                </div>
                <p class="text-xs text-muted-foreground ml-24 mt-1">
                  {{ $t('support.form.file-limit') }}
                </p>
              </div>

              <p class="text-xs text-muted-foreground text-right -my-2">
                This site is protected by reCAPTCHA and the Google
                <a href="https://policies.google.com/privacy">Privacy Policy</a> and
                <a href="https://policies.google.com/terms">Terms of Service</a> apply.
              </p>

              <div class="flex items-center justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="default"
                  class="bg-gray-400/80 hover:bg-gray-400"
                  :disabled="isSubmitting"
                  @click="isOpened = false"
                >
                  <span>{{ $t('misc.cancel') }}</span>
                </Button>
                <Button
                  type="submit"
                  :disabled="isSubmitting"
                >
                  {{ $t('support.form.submit') }}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </v-dialog>
  </Teleport>
</template>
