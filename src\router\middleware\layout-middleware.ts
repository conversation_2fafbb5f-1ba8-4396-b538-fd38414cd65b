import { useAuthStore } from '@/stores/auth-store';
import type { App } from 'vue';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

export async function loadLayoutMiddleware(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext, app: App) {
  try {
    const layout = (to.meta.layout as (v: boolean | undefined) => string)?.(<PERSON><PERSON><PERSON>(useAuthStore().user)) || 'EmptyLayout';
    const layoutComponent = await import(`@/layouts/${layout}.vue`);
    app.config.globalProperties.$layout = layoutComponent.default;
  } catch {
    const layoutComponent = await import('@/layouts/BasicMenuLayout.vue');
    app.config.globalProperties.$layout = layoutComponent.default;
  }
  next();
}
