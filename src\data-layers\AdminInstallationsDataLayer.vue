<script lang="ts" setup>
import { onBeforeUnmount, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { ComponentStateType } from '@/util/types/components';
import type { InstallationServiceDetailData } from '@/pages/installation/types/installation-types';
import AdminInstallationsPage from '@/pages/AdminInstallationsPage.vue';
import { usePaginatedApi } from '@/composables/usePaginatedApi';
import type { CustomWindow } from 'typings/window';
import { CustomEvents } from '@/util/facades/event';

const { t } = useI18n();

const {
  data: installations,
  componentState,
  currentPage,
  totalPages,
  totalItems,
  perPage,
  paginationMeta,
  paginationLinks,
  handlePageChange,
  handlePerPageChange,
  handleSearch,
  refresh,
  initialize,
} = usePaginatedApi<InstallationServiceDetailData>({
  endpoint: '/service/installations',
  initialPerPage: 15,
  searchParam: 'search_term',
  useUrlPagination: true,
  errorMessages: {
    fetchError: t('admin.installations.fetch-error'),
    accessDenied: t('admin.installations.access-denied'),
  },
});

const onInstallationUpdated = () => {
  refresh();
};

(window as CustomWindow).evB.addEventListener(CustomEvents.SERVICE_UPDATED_INSTALLATION, onInstallationUpdated);

onMounted(() => {
  initialize();
});

onBeforeUnmount(() => {
  (window as CustomWindow).evB.removeEventListener(CustomEvents.SERVICE_UPDATED_INSTALLATION, onInstallationUpdated);
});
</script>

<template>
  <section>
    <AdminInstallationsPage
      v-if="componentState === ComponentStateType.OK || componentState === ComponentStateType.LOADING"
      :installations="installations"
      :current-page="currentPage"
      :total-pages="totalPages"
      :total-items="totalItems"
      :per-page="perPage"
      :pagination-meta="paginationMeta"
      :pagination-links="paginationLinks"
      @page-change="handlePageChange"
      @per-page-change="handlePerPageChange"
      @search="handleSearch"
      @refresh="refresh"
    />

    <div
      v-else-if="componentState === ComponentStateType.ERROR"
      class="flex items-center justify-center min-h-[400px]"
    >
      <div class="text-center">
        <h3 class="text-lg font-semibold text-destructive mb-2">
          {{ $t('admin.installations.error-title') }}
        </h3>
        <p class="text-muted-foreground mb-4">
          {{ $t('admin.installations.error-message') }}
        </p>
        <button
          class="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          @click="refresh"
        >
          {{ $t('misc.retry') }}
        </button>
      </div>
    </div>

    <div
      v-else-if="componentState === ComponentStateType.NOT_FOUND_OR_FORBIDDEN"
      class="flex items-center justify-center min-h-[400px]"
    >
      <div class="text-center">
        <h3 class="text-lg font-semibold text-destructive mb-2">
          {{ $t('admin.installations.access-denied-title') }}
        </h3>
        <p class="text-muted-foreground">
          {{ $t('admin.installations.access-denied-message') }}
        </p>
      </div>
    </div>

    <div
      v-if="componentState === ComponentStateType.LOADING"
      class="flex items-center justify-center min-h-[400px]"
    >
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
        <p class="text-muted-foreground">
          {{ $t('admin.installations.loading') }}
        </p>
      </div>
    </div>
  </section>
</template>
