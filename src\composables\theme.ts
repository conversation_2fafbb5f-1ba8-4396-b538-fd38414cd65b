import { computed, ref } from 'vue';
import vuetify from '@/vuetify';

const isLightMode = ref(<PERSON><PERSON>an(localStorage.getItem('isLight')));

export const isLightModeEnabled = computed({
  get(): boolean {
    return isLightMode.value;
  },
  set(newValue) {
    if (newValue) {
      localStorage.setItem('isLight', '1');
      vuetify.theme.global.name.value = 'light';
      document.body.classList.remove('dark');
    } else {
      localStorage.removeItem('isLight');
      vuetify.theme.global.name.value = 'dark';
      document.body.classList.add('dark');
    }
    isLightMode.value = newValue;
  },
});
