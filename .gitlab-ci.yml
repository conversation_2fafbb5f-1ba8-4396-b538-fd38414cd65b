stages:
  - install_and_lint
  - build
  - deploy

image: node:lts-alpine

.pnpm:
  cache:
    # key: ${CI_COMMIT_REF_SLUG}
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
      - node_modules/
  before_script:
    - apk update && apk add curl git
    - npm i -g corepack@latest
    - corepack enable
    - corepack prepare pnpm@10.2.0 --activate
    - pnpm config set store-dir .pnpm-store

.install_and_lint:
  stage: install_and_lint
  extends:
    - .pnpm
  script:
    - pnpm i
    - pnpm add envsub
    - pnpm lint
  artifacts:
    expire_in: 1 day
    paths:
      - node_modules/
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store

.deploy:
  image: alpine:latest
  before_script:
    - apk update && apk add openssh-client bash rsync
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan ${DEPLOY_SERVER} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - rsync -crtvz --delete dist/ version.json ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}

install_and_lint:
  extends:
    - .install_and_lint
  rules:
    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "preview" && $CI_MERGE_REQUEST_TITLE !~ />>UP/
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
    - if: $CI_COMMIT_BRANCH == "dev"

version dev:
  image: alpine:latest
  stage: build
  before_script:
    - apk update && apk add jq
  script:
    - jq --arg number "${CI_COMMIT_REF_NAME}" --arg build "${CI_COMMIT_SHA}" '. + {number:$number,build:$build}' -c version.stub > version.json
  only:
    - dev
  artifacts:
    paths:
      - version.json

#version mr:
#  image: alpine:latest
#  stage: build
#  environment:
#    name: mr/$CI_MERGE_REQUEST_IID
#  before_script:
#    - apk update && apk add jq
#  script:
#    - jq --arg number "$(git tag --list | sort -V | tail -n1)-mr.${CI_MERGE_REQUEST_IID}+${CI_COMMIT_SHORT_SHA}" --arg build "${CI_COMMIT_SHA}" '. + {number:$number,build:$build}' -c version.stub > version.json
#  rules:
#    - if: $CI_MERGE_REQUEST_ID && 'CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"' && $CI_MERGE_REQUEST_TITLE !~ />>UP/ && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "main"
#  artifacts:
#    paths:
#      - version.json

version prod:
  image: alpine:latest
  stage: build
  before_script:
    - apk update && apk add jq
  script:
    - jq --arg number "${CI_COMMIT_REF_NAME}" --arg build "${CI_COMMIT_SHA}" '. + {number:$number,build:$build}' -c version.stub > version.json
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
  artifacts:
    paths:
      - version.json

build dev:
  stage: build
  environment:
    name: dev
  needs:
    - job: install_and_lint
      artifacts: true
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env API_URL=$API_URL
      --env OAUTH_CLIENT_ID=$OAUTH_CLIENT_ID
      --env OAUTH_CLIENT_SECRET=$OAUTH_CLIENT_SECRET
      .env.ci .env
    - pnpm build
  only:
    - dev
  artifacts:
    expire_in: 1 day
    paths:
      - dist

build mr:
  stage: build
  environment:
    name: mr/$CI_MERGE_REQUEST_IID
  needs:
    - job: install_and_lint
      artifacts: true
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env API_URL=$API_URL
      --env OAUTH_CLIENT_ID=$OAUTH_CLIENT_ID
      --env OAUTH_CLIENT_SECRET=$OAUTH_CLIENT_SECRET
      .env.ci .env
    - pnpm build
  rules:
    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "preview" && $CI_MERGE_REQUEST_TITLE !~ />>UP/ && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "main"
  artifacts:
    expire_in: 1 day
    paths:
      - dist

build prod:
  stage: build
  environment:
    name: production
  needs:
    - job: install_and_lint
      artifacts: true
    - job: version prod
      artifacts: true
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env API_URL=$API_URL
      --env OAUTH_CLIENT_ID=$OAUTH_CLIENT_ID
      --env OAUTH_CLIENT_SECRET=$OAUTH_CLIENT_SECRET
      .env.ci .env
    - pnpm build
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
  artifacts:
    expire_in: 14 days
    paths:
      - dist

deploy dev:
  stage: deploy
  environment:
    name: dev
  needs:
    - job: version dev
      artifacts: true
    - job: build dev
      artifacts: true
  variables:
    DEPLOY_USER: "gitlab"
    DEPLOY_SERVER: "dev4.antik.sk"
    DEPLOY_PATH: "/home/<USER>/web/solarcloud/fe"
  extends:
    - .deploy
  only:
    - dev

upload langs dev:
  stage: deploy
  environment:
    name: dev
  script:
    - rsync -crtvz src/lang/*.json ${DEPLOY_USER}@${DEPLOY_SERVER}:${LANG_DEPLOY_PATH}
  variables:
    DEPLOY_USER: "gitlab"
    DEPLOY_SERVER: "dev4.antik.sk"
  only:
    - dev
  extends:
    - .deploy

#deploy mr:
#  stage: deploy
#  environment:
#    name: mr/$CI_MERGE_REQUEST_IID
#    url: https://$CI_MERGE_REQUEST_IID.mr.arm.dev4.antik.sk
#    on_stop: undeploy mr
#  needs:
#    - job: version mr
#      artifacts: true
#    - job: build mr
#      artifacts: true
#  variables:
#    DEPLOY_USER: "gitlab"
#    DEPLOY_SERVER: "dev4.antik.sk"
#    DEPLOY_PATH: "/home/<USER>/web/arm/fe.mr/$CI_MERGE_REQUEST_IID"
#  extends:
#    - .deploy
#  rules:
#    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "preview" && $CI_MERGE_REQUEST_TITLE !~ />>UP/ && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "main"
#  when: always

#undeploy mr:
#  stage: deploy
#  variables:
#    DEPLOY_USER: "gitlab"
#    DEPLOY_SERVER: "dev4.antik.sk"
#    DEPLOY_PATH: "/home/<USER>/web/arm/fe.mr/$CI_MERGE_REQUEST_IID"
#  extends:
#    - .deploy
#  script:
#    - ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "cd $(dirname ${DEPLOY_PATH}) && if [ -f ./$CI_MERGE_REQUEST_IID/index.html ]; then rm -rf ./$CI_MERGE_REQUEST_IID/; fi"
#  environment:
#    name: mr/$CI_MERGE_REQUEST_IID
#    action: stop
#  rules:
#    - if: $CI_MERGE_REQUEST_ID && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "dev" && $CI_COMMIT_BRANCH != "preview" && $CI_MERGE_REQUEST_TITLE !~ />>UP/ && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "main"
#      when: manual

deploy prod:
  stage: deploy
  environment:
    name: production
  needs:
    - job: version prod
      artifacts: true
    - job: build prod
      artifacts: true
  variables:
    DEPLOY_USER: "gitlab"
    DEPLOY_SERVER: "*************"
    DEPLOY_PATH: "/opt/antik/antikcloud/fe"
  extends:
    - .deploy
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/

upload langs prod:
  stage: deploy
  environment:
    name: production
  script:
    - rsync -crtvz src/lang/*.json ${DEPLOY_USER}@${DEPLOY_SERVER}:${LANG_DEPLOY_PATH}
  variables:
    DEPLOY_USER: "gitlab"
    DEPLOY_SERVER: "*************"
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
  extends:
    - .deploy