<script lang="ts" setup>
import { DateTime } from 'luxon';
import { useI18n } from 'vue-i18n';
import DateIntervalPicker from '@/components/global/DateIntervalPicker.vue';
import DayPicker from '@/components/global/DayPicker.vue';
import GeneralPopover from '@/components/global/GeneralPopover.vue';
import { MetricChartViewType } from '@/util/types/installation';
import type { InstallationDetailData } from '../types/installation-types';
import { useMetricChartInput } from '../composables/useMetricChartInput';
import { useMetricChartData } from '@/composables/useMetricChartData';
import EChartsSeriesRenderer from '@/components/charts/metrics/EChartsSeriesRenderer.vue';
import { yAxisCounters, yAxisFloats } from '../helpers/installation-charts-config';
import ChartSectionChartTitle from './chart-section-chunks/ChartSectionChartTitle.vue';
import { RotateCcwIcon } from 'lucide-vue-next';
import ButtonVariant1 from '@/components/ButtonVariant1.vue';

interface Props {
  installation: InstallationDetailData,
}

const props = defineProps<Props>();
const { locale } = useI18n();

const {
  metricChartInputData,
  metricChartInputSetters,
} = useMetricChartInput();

const { dataSeries: dataSeriesFloats, componentState: componentStateFloats, fetchAllData: fetchAllDataFloats } = useMetricChartData({
  inputs: metricChartInputData,
  installation: props.installation,
  floatMetrics: ['atk_solar_power', 'atk_battery_power', 'atk_battery_charge', 'atk_home_power'],
});

const { dataSeries: dataSeriesCounters, componentState: componentStateCounters, fetchAllData: fetchAllDataCounters } = useMetricChartData({
  inputs: metricChartInputData,
  installation: props.installation,
  counterMetrics: ['atk_grid_energy_buy', 'atk_grid_energy_sell', 'atk_solar_energy'],
});

const fetchAllData = () => {
  fetchAllDataFloats();
  fetchAllDataCounters();
};

metricChartInputSetters.setToday();

const customInputsStateKeeper = {
  customDay: '',
  customInterval: {
    dateStart: '',
    dateEnd: '',
  },
};

const onCustomDayChanged = (dateStart: string) => {
  customInputsStateKeeper.customDay = dateStart;
  metricChartInputSetters.setCustomDay(dateStart);
};

const onCustomIntervalChanged = ({ dateStart, dateEnd }: Record<string, string>) => {
  customInputsStateKeeper.customInterval.dateStart = dateStart;
  customInputsStateKeeper.customInterval.dateEnd = dateEnd;
  metricChartInputSetters.setCustomInterval(dateStart, dateEnd);
};
</script>

<template>
  <div class="h-full">
    <h2 class="font-bold text-2xl mt-4">
      {{ $t('installation.plant-usage-chart') }}
    </h2>
    <div class="bg-prim-col-foreground-1 dark:bg-prim-col-foreground-1 rounded-xl mt-4">
      <div class="flex flex-col sm:flex-row items-center justify-between p-2 sm:pr-2">
        <!-- <v-tabs
          v-model="chartType"
          align-tabs="center"
          color="rgb(var(--prim-col-selected-1))"
          show-arrows
          class="w-fit hidden! lg:flex!"
        >
          <v-tab
            v-for="{ value, title } in chartTabMap"
            :key="value"
            :value="value"
          >
            {{ title }}
          </v-tab>
        </v-tabs>

        <div class="w-[250px] mx-4 h-12 lg:hidden">
          <v-select
            v-model="chartType"
            :items="chartTabMap"
            item-title="title"
            variant="plain"
            item-value="value"
            class="-mt-1.5"
          />
        </div> -->

        <div class="flex flex-wrap gap-1.5 sm:gap-2 text-sm pt-2 pl-2">
          <ButtonVariant1
            class="h-8 w-fit px-2 rounded-md"
            :active="metricChartInputData.viewType === MetricChartViewType.DAY"
            @click="metricChartInputSetters.setToday"
          >
            <span class="font-medium">{{ $t('misc.24h') }}</span>
          </ButtonVariant1>
          <ButtonVariant1
            class="h-8 w-8 rounded-md"
            :active="metricChartInputData.viewType === MetricChartViewType.SEVEN_DAYS"
            @click="metricChartInputSetters.setSevenDays"
          >
            <span
              class="font-medium"
              :title="$t('calendar.x-days', 7)"
            >7d</span>
          </ButtonVariant1>
          <ButtonVariant1
            class="h-8 w-8 rounded-md"
            :active="metricChartInputData.viewType === MetricChartViewType.ONE_MONTH"
            @click="metricChartInputSetters.setOneMonth"
          >
            <span
              class="font-medium"
              :title="$t('calendar.x-months', 1)"
            >1m</span>
          </ButtonVariant1>
          <ButtonVariant1
            class="h-8 w-8 rounded-md"
            :active="metricChartInputData.viewType === MetricChartViewType.THREE_MONTHS"
            @click="metricChartInputSetters.setThreeMonths"
          >
            <span
              class="font-medium"
              :title="$t('calendar.x-months', 3)"
            >3m</span>
          </ButtonVariant1>
          <GeneralPopover
            align="end"
            content-classes="bg-prim-col-foreground-1 p-2.5 rounded-xl"
          >
            <template #popover-trigger>
              <ButtonVariant1
                class="h-8 w-fit px-2 rounded-md"
                :active="metricChartInputData.viewType === MetricChartViewType.PICK_DAY"
              >
                <span class="font-medium">{{ $t('calendar.pick-a-day') }}</span>
              </ButtonVariant1>
            </template>
            <template #popover-content>
              <DayPicker
                :initial-day="customInputsStateKeeper.customDay"
                :max="DateTime.now().minus({day: 1}).toISODate()"
                @day-changed="onCustomDayChanged"
              />
            </template>
          </GeneralPopover>
          <GeneralPopover
            align="end"
            content-classes="bg-prim-col-foreground-1 p-2.5 rounded-xl w-fit max-w-screen overflow-auto"
          >
            <template #popover-trigger>
              <ButtonVariant1
                class="h-8 w-fit px-2 rounded-md"
                :active="metricChartInputData.viewType === MetricChartViewType.PICK_INTERVAL"
              >
                <span class="font-medium">{{ $t('calendar.pick-a-range') }}</span>
              </ButtonVariant1>
            </template>
            <template #popover-content>
              <DateIntervalPicker
                :initial-interval="customInputsStateKeeper.customInterval"
                :max="DateTime.now().toISODate()"
                @interval-changed="onCustomIntervalChanged"
              />
            </template>
          </GeneralPopover>
        </div>
        <div class="px-2">
          <button
            class="cursor-pointer h-8 w-fit px-2 rounded-md flex items-center justify-center select-none bg-prim-col-1/60 hover:bg-prim-col-selected-1"
            @click="fetchAllData"
          >
            <RotateCcwIcon class="w-4 h-4" />
          </button>
        </div>
      </div>

      <ChartSectionChartTitle :metric-chart-input-data="metricChartInputData" :metric-chart-input-setters="metricChartInputSetters" />

      <EChartsSeriesRenderer
        :key="`power-${locale}`"
        class="h-[22rem]"
        :data-series="dataSeriesFloats"
        :component-state="componentStateFloats"
        :x-axis-time-format="metricChartInputData.xAxisTimeFormat"
        :y-axis="yAxisCounters.yAxis"
        :metric-to-y-axis-index="yAxisCounters.metricToYAxis"
      />

      <EChartsSeriesRenderer
        :key="`power-${locale}-2`"
        class="h-[20rem]"
        :data-series="dataSeriesCounters"
        :component-state="componentStateCounters"
        :x-axis-time-format="metricChartInputData.xAxisTimeFormat"
        :y-axis="yAxisFloats.yAxis"
      />
    <!--      <GenerationsChart v-if="chartType === ChartType.GENERATION" :key="`generations-${locale}`" class="h-[calc(100%-48px)]" />-->
    <!--      <SelfConsChart v-if="chartType === ChartType.SELFCONS" :key="`selfCons-${locale}`" class="h-[calc(100%-48px)]" />-->
    <!--      <ContributionChart v-if="chartType === ChartType.CONTRIBUTION" :key="`contribution-${locale}`" class="h-[calc(100%-48px)]" />-->
    <!--      <EnergyChart v-if="chartType === ChartType.ENERGY" :key="`energy-${locale}`" class="h-[calc(100%-48px)] my-auto" />-->
    </div>
  </div>
</template>
