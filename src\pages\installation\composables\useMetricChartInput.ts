import { reactive } from 'vue';
import { DateTime } from 'luxon';
import { MetricChartViewType, type MetricChartInputs, type MetricChartInputSetters } from '@/util/types/installation';
import { isToday, setToEndOfTheDay, setToMidnight } from '@/util/facades/date';

export const useMetricChartInput = () => {
  const metricChartInputData = reactive<MetricChartInputs>({} as MetricChartInputs);

  const setToday = () => {
    Object.assign(metricChartInputData, {
      viewType: MetricChartViewType.DAY,
      dateStart: DateTime.now().minus({ day: 1 }).toISO(),
      dateEnd: DateTime.now().toISO(),
      aggregationFloat: '2_minutes',
      aggregationCounter: '1_hour',
      xAxisTimeFormat: 'HH:mm',
    });
  };

  const setSevenDays = () => {
    Object.assign(metricChartInputData, {
      viewType: MetricChartViewType.SEVEN_DAYS,
      dateStart: setToMidnight(DateTime.now().minus({ days: 7 })).toISODate(),
      dateEnd: DateTime.now().toISODate(),
      aggregationFloat: '10_minutes',
      aggregationCounter: '1_day',
      xAxisTimeFormat: 'dd.MM.yyyy HH:mm',
    });
  };

  const setOneMonth = () => {
    Object.assign(metricChartInputData, {
      viewType: MetricChartViewType.ONE_MONTH,
      dateStart: setToMidnight(DateTime.now().minus({ months: 1 })).toISODate(),
      dateEnd: DateTime.now().toISODate(),
      aggregationFloat: '2_hours',
      aggregationCounter: '1_day',
      xAxisTimeFormat: 'dd.MM.yyyy HH:mm',
    });
  };

  const setThreeMonths = () => {
    Object.assign(metricChartInputData, {
      viewType: MetricChartViewType.THREE_MONTHS,
      dateStart: setToMidnight(DateTime.now().minus({ months: 3 })).toISODate(),
      dateEnd: DateTime.now().toISODate(),
      aggregationFloat: '1_day',
      aggregationCounter: '1_week',
      xAxisTimeFormat: 'dd.MM.yyyy HH:mm',
    });
  };

  const setCustomDay = (customDayInput: string) => {
    try {
      const date = DateTime.fromISO(customDayInput);
      if (isToday(date)) {
        setToday();
        return;
      }

      Object.assign(metricChartInputData, {
        viewType: MetricChartViewType.PICK_DAY,
        dateStart: setToMidnight(date).toISODate(),
        dateEnd: setToEndOfTheDay(date).toISODate(),
        aggregationFloat: '2_minutes',
        aggregationCounter: '1_hour',
        xAxisTimeFormat: 'HH:mm',
      });
    } catch {
      console.error('Invalid custom day input');
    }
  };

  const setCustomInterval = (start: string, end: string) => {
    try {
      const dateStart = DateTime.fromISO(start);
      const dateEnd = DateTime.fromISO(end);

      if (isToday(dateStart) && isToday(dateEnd)) {
        setToday();
        return;
      }

      const daysDifference = dateEnd.diff(dateStart, 'days').days;
      let aggregationFloat: string | undefined;
      let aggregationCounter: string | undefined;

      switch (true) {
        case (daysDifference <= 1):
          aggregationFloat = undefined;
          aggregationCounter = '1_hour';
          break;
        case (daysDifference <= 7):
          aggregationFloat = '5_minutes';
          aggregationCounter = '1_day';
          break;
        case (daysDifference <= 30):
          aggregationFloat = '1_hour';
          aggregationCounter = '1_day';
          break;
        case (daysDifference <= 90):
          aggregationFloat = '1_day';
          aggregationCounter = '1_week';
          break;
        case (daysDifference <= 182):
          aggregationFloat = '7_days';
          aggregationCounter = '1_month';
          break;
        default:
          aggregationFloat = '1_month';
          aggregationCounter = '1_month';
          break;
      }

      Object.assign(metricChartInputData, {
        viewType: MetricChartViewType.PICK_INTERVAL,
        dateStart: setToMidnight(dateStart).toISODate(),
        dateEnd: setToEndOfTheDay(dateEnd).toISODate(),
        aggregationFloat,
        aggregationCounter,
        xAxisTimeFormat: 'dd.MM.yyyy HH:mm',
      });
    } catch {
      console.error('Invalid custom interval input');
    }
  };

  const addDay = () => {
    try {
      let date = DateTime.fromISO(metricChartInputData.dateStart);
      if (isToday(date.plus({ day: 1 }))) {
        setToday();
        return;
      }
      date = date.plus({ day: 1 });

      Object.assign(metricChartInputData, {
        viewType: MetricChartViewType.PICK_DAY,
        dateStart: setToMidnight(date).toISODate(),
        dateEnd: setToEndOfTheDay(date).toISODate(),
        aggregationFloat: '2_minutes',
        aggregationCounter: '1_hour',
        xAxisTimeFormat: 'HH:mm',
      });
    } catch {
      console.error('Invalid custom day input');
    }
  };

  const subtractDay = () => {
    try {
      const date = DateTime.fromISO(metricChartInputData.dateStart).minus({ day: 1 });

      Object.assign(metricChartInputData, {
        viewType: MetricChartViewType.PICK_DAY,
        dateStart: setToMidnight(date).toISODate(),
        dateEnd: setToEndOfTheDay(date).toISODate(),
        aggregationFloat: '2_minutes',
        aggregationCounter: '1_hour',
        xAxisTimeFormat: 'HH:mm',
      });
    } catch {
      console.error('Invalid custom day input');
    }
  };

  return {
    metricChartInputData,
    metricChartInputSetters: {
      setToday,
      setSevenDays,
      setOneMonth,
      setThreeMonths,
      setCustomDay,
      setCustomInterval,
      addDay,
      subtractDay,
    } as MetricChartInputSetters
  };
};
