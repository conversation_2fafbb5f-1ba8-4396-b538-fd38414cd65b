<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { Textarea } from '@/shadcn-components/ui/textarea';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { formDataFromObject } from '@/util/facades/forms';
import type { UserData } from '@/stores/auth-store';
import type { InstallationFormData, InstallationServiceDetailData } from '@/pages/installation/types/installation-types';
import PageLoader from '@/components/global/PageLoader.vue';

interface Props {
  selectedUser: UserData | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'installation-created': [installationId: string];
}>();

const { t } = useI18n();
const isSubmitting = ref(false);

const installationData = reactive<InstallationFormData>({
  title: '',
  description: '',
  pv_capacity: undefined,
  battery_capacity: undefined,
  pv_count: undefined,
  locality: '',
  full_address: '',
  image: undefined,
  classification: undefined,
  user_id: props.selectedUser?.id || undefined,
});

const onSubmit = async() => {
  if (isSubmitting.value) {
    return;
  }
  isSubmitting.value = true;
  try {
    const data = formDataFromObject({
      ...installationData,
      user_id: props.selectedUser!.id,
    });

    const response = await customAxios.post<{ data: InstallationServiceDetailData }>('/service/installations', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    emit('installation-created', response.data.data.id);

    deployToast(ToastType.SUCCESS, {
      text: t('admin.new-installation.step3.installation-created-success'),
      timeout: 6000,
    });
  } catch (error: any) {
    console.error('Failed to create installation:', error);
    deployToast(ToastType.ERROR, {
      text: t('admin.new-installation.step3.installation-creation-failed'),
      timeout: 6000,
    });
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="space-y-6">
    <p class="text-muted-foreground">
      {{ $t('admin.new-installation.step3.description') }}
    </p>

    <div
      v-if="selectedUser"
      class="p-4 dark:bg-primary/5 bg-gray-100 border border-primary rounded-lg"
    >
      <div class="flex items-center space-x-2">
        <span class="font-medium">{{ $t('admin.new-installation.step3.selected-user') }}:</span>
      </div>
      <div class="mt-2">
        <p class="font-medium">
          {{ [selectedUser.name, selectedUser.surname].filter(Boolean).join(' ') }}
        </p>
        <p class="text-sm text-muted-foreground">
          {{ selectedUser.email }}
        </p>
      </div>
    </div>

    <form
      autocomplete="off"
      class="space-y-6"
      @submit.prevent="onSubmit"
    >
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Title -->
        <div class="md:col-span-2">
          <Label for="installation-title">
            {{ $t('misc.title') }} *
          </Label>
          <Input
            id="installation-title"
            v-model="installationData.title"
            name="title"
            type="text"
            required
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <!-- Description -->
        <div class="md:col-span-2">
          <Label for="installation-description">
            {{ $t('misc.description') }}
          </Label>
          <Textarea
            id="installation-description"
            v-model="installationData.description!"
            name="description"
            :rows="4"
            :disabled="isSubmitting"
            class="mt-2 border-prim-col-1"
            style="resize: none;"
          />
        </div>

        <!-- PV Capacity -->
        <div>
          <Label for="pv_capacity">
            <div>{{ t('installation.form.pv-capacity') }}</div>
            <div class="text-xs font-normal text-muted-foreground">(kW)</div>
          </Label>
          <Input
            id="pv_capacity"
            v-model.number="installationData.pv_capacity!"
            name="pv_capacity"
            type="number"
            min="0"
            step="any"
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <!-- Battery Capacity -->
        <div>
          <Label for="battery_capacity">
            <div>{{ t('installation.form.battery-capacity') }}</div>
            <div class="text-xs font-normal text-muted-foreground">(kWh)</div>
          </Label>
          <Input
            id="battery_capacity"
            v-model.number="installationData.battery_capacity!"
            name="battery_capacity"
            type="number"
            min="0"
            step="any"
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <!-- PV Count -->
        <div>
          <Label for="pv_count">
            {{ t('installation.form.panels-count') }}
          </Label>
          <Input
            id="pv_count"
            v-model.number="installationData.pv_count!"
            name="pv_count"
            type="number"
            min="0"
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <!-- Locality -->
        <div>
          <Label for="locality">
            {{ $t('misc.locality') }}
          </Label>
          <Input
            id="locality"
            v-model="installationData.locality!"
            name="locality"
            type="text"
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <div class="md:col-span-2">
          <Label for="installation-full-address">
            {{ $t('misc.full-address') }}
          </Label>
          <Input
            id="installation-full-address"
            v-model="installationData.full_address!"
            name="full_address"
            type="text"
            :disabled="isSubmitting"
            class="mt-2"
          />
        </div>

        <!-- Classification -->
        <div class="md:col-span-2">
          <Label for="classification">
            {{ $t('misc.type') }}
          </Label>
          <select
            id="classification"
            v-model="installationData.classification"
            name="classification"
            :disabled="isSubmitting"
            class="mt-2 w-full rounded-md border dark:border-input border-prim-col-1 bg-background px-3 py-2 text-sm outline-hidden"
          >
            <option value="" disabled>
              {{ $t('misc.select-option') }}
            </option>
            <option value="on_grid">
              On-grid
            </option>
            <option value="hybrid">
              Hybrid
            </option>
          </select>
        </div>

        <!-- Image Upload -->
        <div class="md:col-span-2">
          <Label for="image">
            {{ $t('misc.image') }}
          </Label>
          <input
            id="image"
            name="image"
            type="file"
            accept="image/png, image/jpeg"
            :disabled="isSubmitting"
            class="mt-2 w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-secondary file:text-secondary-foreground hover:file:bg-secondary/80"
            @change="e => installationData.image = (e.target as HTMLInputElement).files?.[0] ?? undefined"
          >
        </div>
      </div>

      <div class="pt-4">
        <Button
          type="submit"
          class="w-full"
          :disabled="isSubmitting"
        >
          <PageLoader
            v-if="isSubmitting"
            class="mr-2 h-4 w-4"
          />
          <span v-if="isSubmitting">
            {{ $t('admin.new-installation.step3.creating') }}...
          </span>
          <span v-else>
            {{ $t('admin.new-installation.step3.create-installation') }}
          </span>
        </Button>
      </div>
    </form>
  </div>
</template>
