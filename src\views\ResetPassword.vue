<script lang="ts" setup>
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo-w-text.svg?skipsvgo';
import ResetPasswordForm from '@/pages/reset-password/ResetPasswordForm.vue';
import { routeMap } from '@/router/routes';

</script>

<template>
  <div class="w-screen h-[100dvh] flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px] bg-prim-col-1 lg:bg-transparent">
      <router-link
        :to="{name: routeMap.home.children.dashboardInstallations.name}"
        class="hidden bg-muted lg:flex items-center justify-center"
      >
        <SolarCloudLogo class="h-52 [&_.st1]:fill-prim-col-selected-1!" />
      </router-link>
      <div class="flex items-center justify-center py-12 bg-prim-col-1">
        <ResetPasswordForm />
      </div>
    </div>
  </div>
</template>
