<script setup lang="ts">
import { reactive, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { RoleData } from '@/util/types/roles-and-permissions';
import type { UserManagementData } from '@/util/types/user-management';

interface Props {
  allRoles: RoleData[];
  userId?: string | null;
  isEditDialog?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  userId: null,
  isEditDialog: false,
});

const isOpened = defineModel<boolean>();
const { t } = useI18n();

const userForm = reactive({
  name: '',
  surname: '',
  email: '',
  phone: '',
  roles: [] as {
    id: number,
    name: string,
  }[],
});

const emit = defineEmits(['userCreated', 'userUpdated']);

const isLoading = reactive({
  form: false,
  fetchUser: false,
});

const dialogTitle = computed(() => {
  return props.isEditDialog
    ? t('user-management.edit-user')
    : t('user-management.create-user');
});

const submitButtonText = computed(() => {
  return props.isEditDialog
    ? t('misc.save')
    : t('misc.create');
});

// Reset form when dialog opens/closes
watch(isOpened, newValue => {
  if (newValue) {
    if (props.isEditDialog && props.userId) {
      fetchUserData();
    } else {
      resetForm();
    }
  }
});

const resetForm = () => {
  userForm.name = '';
  userForm.surname = '';
  userForm.email = '';
  userForm.phone = '';
  userForm.roles = [];
};

const fetchUserData = async() => {
  if (!props.userId) {return;}

  isLoading.fetchUser = true;
  try {
    const response = await customAxios.get<{ data: UserManagementData[0] }>(`/users/${props.userId}`);
    const userData = response.data.data;

    userForm.name = userData.name || '';
    userForm.surname = userData.surname || '';
    userForm.email = userData.email || '';
    userForm.phone = userData.phone || '';
    userForm.roles = Array.isArray(userData.roles) ? userData.roles : [];
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    deployToast(ToastType.ERROR, {
      text: t('user-management.fetch-user-failed'),
      timeout: 6000,
    });
  } finally {
    isLoading.fetchUser = false;
  }
};

const onSubmit = async() => {
  if (isLoading.form) {return;}

  isLoading.form = true;
  try {
    const payload = {
      name: userForm.name,
      surname: userForm.surname,
      email: userForm.email,
      phone: userForm.phone,
      roles: userForm.roles,
    };

    if (props.isEditDialog && props.userId) {
      await customAxios.put(`/users/${props.userId}`, payload);
      emit('userUpdated');
      deployToast(ToastType.SUCCESS, {
        text: t('user-management.user-update-success'),
        timeout: 6000,
      });
    } else {
      await customAxios.post('/users', payload);
      emit('userCreated');
      deployToast(ToastType.SUCCESS, {
        text: t('user-management.user-create-success'),
        timeout: 6000,
      });
    }

    isOpened.value = false;
    resetForm();
  } catch (error) {
    console.error('Failed to save user:', error);
    deployToast(ToastType.ERROR, {
      text: props.isEditDialog
        ? t('user-management.user-update-fail')
        : t('user-management.user-create-fail'),
      timeout: 6000,
    });
  } finally {
    isLoading.form = false;
  }
};
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      :style="{margin: 'auto'}"
    >
      <form
        autocomplete="off"
        class="bg-prim-col-1 p-6 rounded-2xl w-[35rem] max-w-[90vw]"
        @submit.prevent="onSubmit"
      >
        <h3 class="font-bold text-xl mb-2">
          {{ dialogTitle }}
        </h3>
        <p class="text-black/50 dark:text-white/60 text-sm mb-4">
          {{ isEditDialog ? $t('user-management.edit-user-desc') : $t('user-management.create-user-desc') }}
        </p>

        <div v-if="isLoading.fetchUser" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
        </div>

        <div v-else class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label for="user-name" class="text-right w-20">
              {{ $t('misc.name') }}
            </Label>
            <Input
              id="user-name"
              v-model="userForm.name"
              type="text"
              required
              :disabled="isLoading.form"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="user-surname" class="text-right w-20">
              {{ $t('misc.surname') }}
            </Label>
            <Input
              id="user-surname"
              v-model="userForm.surname"
              type="text"
              :disabled="isLoading.form"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="user-email" class="text-right w-20">
              {{ $t('misc.mail') }}
            </Label>
            <Input
              id="user-email"
              v-model="userForm.email"
              type="email"
              required
              :disabled="isLoading.form"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="user-phone" class="text-right w-20">
              {{ $t('misc.phone') }}
            </Label>
            <Input
              id="user-phone"
              v-model="userForm.phone"
              type="tel"
              :disabled="isLoading.form"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label for="user-roles" class="text-right w-20">
              {{ $t('user-management.roles') }}
            </Label>
            <v-select
              id="user-roles"
              v-model="userForm.roles"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="allRoles"
              item-title="name"
              item-value="id"
              multiple
              variant="solo-filled"
              width="100%"
              :disabled="isLoading.form"
            >
              <template #selection="{ item, index }">
                <v-chip v-if="index < 1">
                  <span class="text-xs">{{ item.title }}</span>
                </v-chip>
                <span
                  v-if="index === 1"
                  class="text-xs text-grey text-caption align-self-center"
                >
                  (+{{ userForm.roles.length - 1 }} others)
                </span>
              </template>
            </v-select>
          </div>
        </div>

        <div class="flex items-center justify-end gap-2">
          <Button
            type="button"
            variant="default"
            class="bg-gray-400/80 hover:bg-gray-400"
            :disabled="isLoading.form"
            @click="isOpened = false"
          >
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button
            type="submit"
            :disabled="isLoading.form || isLoading.fetchUser"
          >
            <div v-if="isLoading.form" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            {{ submitButtonText }}
          </Button>
        </div>
      </form>
    </v-dialog>
  </Teleport>
</template>
