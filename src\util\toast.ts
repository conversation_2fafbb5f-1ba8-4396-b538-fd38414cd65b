import { POSITION, useToast } from 'vue-toastification';
import ErrorIcon from '@/assets/svg/error_icon_toast.svg';
import InfoIcon from '@/assets/svg/info-icon-toast.svg';
import SuccessIcon from '@/assets/svg/success-icon-toast.svg';
import type { PluginOptions } from 'vue-toastification';

export enum ToastType {
  INFO,
  SUCCESS,
  ERROR,
}

export interface ToastOptions {
  text: string,
  timeout?: number,
  position?: POSITION,
}

export const deployToast = (type: ToastType, options: ToastOptions) => {
  const toast = useToast();
  const toastOptions: PluginOptions = {
    position: options.position ?? POSITION.BOTTOM_LEFT,
    timeout: options.timeout ?? 5000,
    closeOnClick: true,
    pauseOnFocusLoss: true,
    pauseOnHover: true,
    draggable: true,
    draggablePercent: 0.6,
    showCloseButtonOnHover: true,
    hideProgressBar: true,
    closeButton: 'button',
    icon: type === ToastType.INFO ? InfoIcon : type === ToastType.SUCCESS ? SuccessIcon : ErrorIcon,
    rtl: false,
  };
  switch (type) {
    case ToastType.INFO:
      toast.info(options.text, toastOptions);
      break;
    case ToastType.ERROR:
      toast.error(options.text, toastOptions);
      break;
    case ToastType.SUCCESS:
      toast.success(options.text, toastOptions);
      break;
  }
};

