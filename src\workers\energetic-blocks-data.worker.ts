import { DeviceTypes, type AnyDevice } from '@/util/types/devices/device-types';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types';
import type { TuyaDevice } from '@/pages/installation/types/tuya-types';

self.onmessage = ({ data }: MessageEvent<{
  installationDetail?: InstallationDetailData;
  tuyaDevices?: TuyaDevice[];
}>) => {
  const appDevices: AnyDevice[] = data.installationDetail?.deviceInstances?.map(v => ({
    id: v.id,
    model: v.device?.model ?? '',
    type: v.device?.type?.name ?? '',
    vendor: v.device?.vendor?.name ?? '',
    lastUpdated: v.last_updated,
  })) as AnyDevice[] ?? [];

  const tuyaDevices: AnyDevice[] = data.tuyaDevices?.map(device => ({
    id: device.id,
    model: device.model,
    type: DeviceTypes.WALL_PLUG,
    vendor: 'Tuya',
    currentConsumption: {
      value: Number(device.status.find(s => s.code === 'cur_power')?.value ?? 0) / 10,
      unit: 'W',
    },
    todayConsumption: {
      value: Number(device.status.find(s => s.code === 'add_ele')?.value ?? 0),
      unit: 'kWh',
    },
    totalConsumption: {
      value: Number(device.status.find(s => s.code === 'add_ele')?.value ?? 0),
      unit: 'kWh',
    },
    online: device.online,
  })) ?? [];

  const combined = [...appDevices, ...tuyaDevices];

  self.postMessage(combined);
};
