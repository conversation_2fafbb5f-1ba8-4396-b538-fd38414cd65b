<script lang="ts" setup>
import CircularChart from '@/components/charts/CircularChart.vue';
import Gauge from '@/components/gauge/Gauge.vue';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
}

defineProps<Props>();
</script>

<template>
  <div class="relative rounded-xl h-full w-full flex flex-col items-center justify-center bg-prim-col-foreground-contrast/15">
    <div class="absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-tl-xl rounded-sm px-2 py-1 text-xs">
      {{ $t('installation.power') }}
    </div>
    <div class="max-h-full w-32 lg:w-24 xl:w-32 3xl:w-36 max-w-[15rem]">
      <gauge
        class="h-full max-h-full! mt-10 lg:mt-14"
        :heading="undefined"
        font-size="0"
        :min="0"
        :max="12000"
        :value="inverterCurrentData?.metrics?.atk_solar_power?.value"
        :padding="false"
        :labels-on-arc="false"
        :active-fill="`rgb(var(--prim-col-selected-1))`"
        :inactive-fill="`rgb(var(--prim-col-foreground-contrast))`"
        :thickness="8"
        :unit="` ${inverterCurrentData?.metrics?.atk_solar_power?.unit}`"
        :pointer-stroke-width="5"
        :pointer-gap="3"
        :pivot-radius="10"
      />
    </div>
    <div class="font-light mb-4 mt-auto text-xl">
      {{ inverterCurrentData?.metrics?.atk_solar_power?.value ?? '0' }} W
    </div>
  </div>
  <div class="h-full w-full flex flex-col items-center justify-center relative">
    <div class="absolute top-0 right-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-tr-xl rounded-sm px-2 py-1 text-xs">
      {{ $t('installation.battery') }}
    </div>
    <div class="w-full h-full">
      <CircularChart
        :percentage="inverterCurrentData?.metrics?.atk_battery_charge?.value"
        stroke-active-color="rgb(var(--prim-col-selected-1))"
        stroke-base-color="rgb(var(--prim-col-foreground-2))"
        icon-fill-color="rgb(var(--prim-col-foreground-contrast))"
        :stroke-width="2"
        svg-container-classes="w-28"
      />
    </div>
  </div>
</template>
