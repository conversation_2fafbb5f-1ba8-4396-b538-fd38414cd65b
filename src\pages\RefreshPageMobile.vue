<script lang="ts" setup>
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo-w-text.svg?skipsvgo';
import { Button } from '@/shadcn-components/ui/button';
import { executeWebkitMessage } from '@/util/facades/webkit-app.ts';

const webkitAppReload = () => {
  executeWebkitMessage('onAppReload');
};
</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2 bg-prim-col-1 lg:bg-transparent">
      <div class="hidden bg-prim-col-2 lg:flex items-center justify-center">
        <SolarCloudLogo class="h-52 [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div class="flex items-center justify-center py-12 bg-prim-col-1">
        <div class="mx-auto w-[18rem] lw:w-[22rem]">
          <div class="w-full grid gap-4">
            <div class="w-fit mx-auto">
              <SolarCloudLogo class="h-16 [&_.st1]:fill-prim-col-selected-1!" />
            </div>
            <div class="grid gap-2 text-center">
              <h1 class="text-3xl font-bold">
                {{ $t('login.reset-needed') }}
              </h1>
              <p class="text-sm text-balance text-muted-foreground">
                {{ $t('login.token-revoked') }}
              </p>
            </div>
            <div
              class="w-fit mx-auto mt-2"
              @click="webkitAppReload"
            >
              <Button
                type="submit"
                class="w-fit"
              >
                {{ $t('misc.reset') }}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
