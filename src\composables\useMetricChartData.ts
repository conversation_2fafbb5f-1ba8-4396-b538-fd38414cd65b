import { ref, reactive, computed, watch } from 'vue';
import { DateTime } from 'luxon';
import { isToday } from '@/util/facades/date';
import { customAxios } from '@/util/axios';
import { sleep } from '@/util/fakers/sleep';
import { ComponentStateType } from '@/util/types/components';
import type { ApiMetricKeys, ApiRangeMultiDataResponse } from '@/util/types/api-responses';
import type { DataSeries } from '@/util/types/data-series';
import type { MetricChartInputs } from '@/util/types/installation';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types';
import { useI18n } from 'vue-i18n';

interface UseMetricChartDataOptions {
  floatMetrics?: ApiMetricKeys[];
  counterMetrics?: ApiMetricKeys[];
  inputs: MetricChartInputs;
  installation: InstallationDetailData;
}

export function useMetricChartData(props: UseMetricChartDataOptions) {
  const { t } = useI18n();

  const dataSeries = ref<DataSeries>();
  const componentState = reactive({
    data: ComponentStateType.OK,
  });

  // Build nameMap for translating metric names
  const nameMap = computed<Record<string, string>>(() => {
    const map: Record<string, string> = {};
    [...(props.floatMetrics || []), ...(props.counterMetrics || [])].forEach(metric => {
      map[metric] = t(`metrics.${metric}`);
    });
    return map;
  });

  // Worker for processing metrics data
  const metricsWorker = new Worker(
    new URL('@/workers/metrics-chart.worker.ts', import.meta.url),
    { type: 'module' }
  );

  metricsWorker.onmessage = async(event: MessageEvent<any>) => {
    dataSeries.value = event.data;
    componentState.data = ComponentStateType.OK;
  };

  // Data fetchers
  const fetchFloatData = async(controller?: AbortController) => {
    if (!props.floatMetrics?.length) {return null;}
    const dates = {
      start: DateTime.fromISO(props.inputs.dateStart),
      end: isToday(DateTime.fromISO(props.inputs.dateEnd))
        ? DateTime.now()
        : DateTime.fromISO(props.inputs.dateEnd).set({ hour: 23, minute: 59, second: 59, millisecond: 999 }),
    };
    return customAxios.get<ApiRangeMultiDataResponse>(
      `metrics/${props.installation.id}/inverter/range`,
      {
        signal: controller?.signal,
        params: {
          type: props.floatMetrics,
          start: dates.start.toISO(),
          end: dates.end.toISO(),
          aggregation: props.inputs.aggregationFloat,
          aggregation_type: 'avg',
        },
      }
    );
  };

  const fetchCounterData = async(controller?: AbortController) => {
    if (!props.counterMetrics?.length) {return null;}
    const dates = {
      start: DateTime.fromISO(props.inputs.dateStart),
      end: isToday(DateTime.fromISO(props.inputs.dateEnd))
        ? DateTime.now()
        : DateTime.fromISO(props.inputs.dateEnd).set({ hour: 23, minute: 59, second: 59, millisecond: 999 }),
    };
    return customAxios.get<ApiRangeMultiDataResponse>(
      `metrics/${props.installation.id}/inverter/range`,
      {
        signal: controller?.signal,
        params: {
          type: props.counterMetrics,
          start: dates.start.toISO(),
          end: dates.end.toISO(),
          aggregation: props.inputs.aggregationCounter,
          aggregation_type: 'delta',
        },
      }
    );
  };

  const fetchAllData = async(controller?: AbortController) => {
    componentState.data = ComponentStateType.LOADING;
    await sleep(250);

    try {
      const [floatRes, counterRes] = await Promise.all([
        fetchFloatData(controller),
        fetchCounterData(controller),
      ]);

      metricsWorker.postMessage({
        float: floatRes?.data ?? [],
        counter: counterRes?.data ?? [],
        nameMap: nameMap.value,
      });
    } catch {
      componentState.data = ComponentStateType.ERROR;
      dataSeries.value = undefined;
    }
  };

  fetchAllData();

  watch(
    [
      () => props.inputs.dateStart,
      () => props.inputs.dateEnd,
      () => props.floatMetrics,
      () => props.counterMetrics,
      () => props.inputs.aggregationFloat,
      () => props.inputs.aggregationCounter,
      () => props.installation.id,
    ],
    async() => {
      const controller = new AbortController();
      await fetchAllData(controller);
    }
  );

  return {
    dataSeries,
    componentState,
    fetchAllData,
  };
}
