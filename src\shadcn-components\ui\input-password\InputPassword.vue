<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { type HTMLAttributes, ref } from 'vue';
import { cn } from '@/shadcn-utils';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  defaultValue?: string | number
  modelValue?: string | number
  class?: HTMLAttributes['class'],
  required?: boolean,
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

const inputEl = ref<HTMLInputElement>();
const inputType = ref('password');

const toggleFieldType = (e: Event) => {
  inputType.value = inputType.value === 'password' ? 'text' : 'password';
  e.stopPropagation();
  e.preventDefault();
};
</script>

<template>
  <div class="relative w-full h-full group">
    <input
      :id="$attrs['id'] as string"
      ref="inputEl"
      v-model="modelValue"
      :type="inputType"
      :required="required"
      :name="$attrs['name'] as string ?? undefined"
      :autocomplete="$attrs['autocomplete'] as string ?? undefined"
      :placeholder="$t('misc.password')"
      :class="cn('ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500 flex group-active:ring-ring group-active:border-gray-500 h-10 w-full rounded-md border bg-background pl-3 pr-12 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50', props.class)
      "
    >
    <div
      class="transition-all absolute -translate-y-1/2 top-1/2 right-[1px] duration-100 hover:bg-black/15 dark:hover:bg-prim-col-foreground-1 hover:[&_svg]:text-prim-col-foreground-contrast rounded-r-md w-12 h-[calc(100%-2px)] flex items-center justify-center select-none cursor-pointer group"
      @click="toggleFieldType"
    >
      <font-awesome-icon
        class="transition-all duration-100 icon-eye text-base text-gray-400 right-[15px]"
        :class="[inputType === 'password' ? '' : 'right-[0.845rem]']"
        :icon="inputType === 'password' ? 'eye' : 'eye-slash'"
      />
    </div>
  </div>
</template>
