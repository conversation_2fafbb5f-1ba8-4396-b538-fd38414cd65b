<script lang="ts" setup>
import { PopoverClose } from 'radix-vue';
import { ref } from 'vue';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';

interface Props {
  max?: string;
  min?: string;
  initialDay?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  dayChanged: [customDate: string] // named tuple syntax
}>();

const dateInput = ref<string>(props.initialDay ?? '');

const onSubmit = () => {
  emit('dayChanged', dateInput.value);
};

</script>

<template>
  <form @submit.prevent="onSubmit">
    <h3 class="font-bold mb-1.5 text-black dark:text-white">
      {{ $t('calendar.pick-date') }}
    </h3>
    <input
      v-model="dateInput"
      type="date"
      :min="min"
      :max="max"
      class="w-full h-9 chart-date-input bg-prim-col-1/60 hover:bg-prim-col-1/80 px-2 rounded-md cursor-pointer outline-hidden"
    >
    <PopoverClose>
      <ShadCnButton
        type="submit"
        variant="default"
        size="icon"
        class="rounded-lg w-fit h-fit px-2 py-1 mt-2 dark:text-white text-black"
      >
        {{ $t('misc.save' ) }}
      </ShadCnButton>
    </PopoverClose>
  </form>
</template>
