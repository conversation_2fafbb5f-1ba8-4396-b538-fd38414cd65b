<script setup lang="ts">
import SunAndMoonSvgIcon from '@/assets/svg/sun-and-moon.svg?skipsvgo';
import { isLightModeEnabled } from '@/composables/theme';

const onChangeMode = () => {
  isLightModeEnabled.value = !isLightModeEnabled.value;
};
</script>

<template>
  <button
    id="theme-toggle"
    class="block theme-toggle"
    title="Toggles light &amp; dark"
    aria-label="light"
    aria-live="polite"
    @click.stop.prevent="onChangeMode"
  >
    <SunAndMoonSvgIcon />
  </button>
</template>

<style lang="css">

.theme-toggle {
  --size: 1.65rem;
  --icon-fill: rgb(var(--prim-col-foreground-contrast));
  --icon-fill-hover: rgb(var(--prim-col-selected-1));
  background: none;
  border: none;
  padding: 0;
  inline-size: var(--size);
  block-size: var(--size);
  aspect-ratio: 1;
  border-radius: 50%;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  outline-offset: 5px;

  & > svg {
    inline-size: 100%;
    block-size: 100%;
    stroke-linecap: round;
  }
}

.dark .theme-toggle {
  --icon-fill: rgb(var(--prim-col-foreground-contrast));
  --icon-fill-hover: rgb(var(--prim-col-selected-1));
}

.sun-and-moon > :is(.moon, .sun, .sun-beams) {
  transform-origin: center;
}

.sun-and-moon > :is(.moon, .sun) {
  fill: var(--icon-fill);
}

.theme-toggle:is(:hover, :focus-visible) > .sun-and-moon > :is(.moon, .sun) {
  fill: var(--icon-fill-hover);
}

.sun-and-moon > .sun-beams {
  stroke: var(--icon-fill);
  stroke-width: 2px;
}

.theme-toggle:is(:hover, :focus-visible) .sun-and-moon > .sun-beams {
  stroke: var(--icon-fill-hover);
}

.dark .sun-and-moon > .sun {
  transform: scale(1.75);
}

.dark .sun-and-moon > .sun-beams {
  opacity: 0;
}

.dark .sun-and-moon > .moon > circle {
  transform: translateX(-7px);
}

@supports (cx: 1) {
  .dark .sun-and-moon > .moon > circle {
    cx: 17;
    transform: translateX(0);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .sun-and-moon > .sun {
    transition: transform .5s var(--ease-elastic-3);
  }

  .sun-and-moon > .sun-beams {
    transition: transform .5s var(--ease-elastic-4), opacity .5s var(--ease-3);
  }

  .sun-and-moon .moon > circle {
    transition: transform .25s var(--ease-out-5);
  }

  @supports (cx: 1) {
    .sun-and-moon .moon > circle {
      transition: cx .25s var(--ease-out-5);
    }
  }

  .dark .sun-and-moon > .sun {
    transition-timing-function: var(--ease-3);
    transition-duration: .25s;
    transform: scale(1.75);
  }

  .dark .sun-and-moon > .sun-beams {
    transition-duration: .15s;
    transform: rotateZ(-25deg);
  }

  .dark .sun-and-moon > .moon > circle {
    transition-duration: .5s;
    transition-delay: .25s;
  }
}
</style>