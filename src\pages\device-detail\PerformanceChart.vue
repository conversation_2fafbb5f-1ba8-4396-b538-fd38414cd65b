<template>
  <div class="h-[300px]">
    <VueApexCharts
      type="line"
      height="100%"
      :options="chartOptions"
      :series="series"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VueApexCharts from 'vue3-apexcharts';

const series = ref([
  {
    name: 'Power Output',
    data: [0, 0, 200, 600, 800, 700, 300, 0]
  }
]);

const chartOptions = ref({
  chart: {
    height: 350,
    type: 'line',
    zoom: {
      enabled: false
    },
    toolbar: {
      show: false
    }
  },
  dataLabels: {
    enabled: false
  },
  stroke: {
    curve: 'smooth',
    width: 3
  },
  colors: ['#4ade80'], // Green color for the line
  xaxis: {
    categories: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
    title: {
      text: 'Time'
    }
  },
  yaxis: {
    title: {
      text: 'Power (W)'
    },
    min: 0
  },
  tooltip: {
    x: {
      format: 'HH:mm'
    }
  },
  grid: {
    borderColor: '#f1f1f1'
  }
});
</script>
