// Cerpanie: +
// Dodavanie: -

import type { InstallationDetailData } from '@/pages/installation/types/installation-types';
import { WorkMode, WorkModeINVT, type InverterCurrentDataTransformed } from '@/util/types/api-responses';
import type { WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';

export enum InverterVendors {
  GOODWE = 'GOODWE',
  INVT = 'INVT',
}

export enum FlowChartSection {
  PHOTOVOLTAICS,
  BATTERY,
  BATTERY_SOC,
  GRID,
  GRID_SELL,
  GRID_BUY,
  HEATING,
  HEATING_TEMP,
  TUV,
  HOUSEHOLD,
  WALLBOX,
}

export type IInverterMetricsVendorMap = Record<InverterVendors, Record<FlowChartSection, string[] | undefined>>;

export const inverterMetricsVendorMap: IInverterMetricsVendorMap = {
  [InverterVendors.GOODWE]: {
    [FlowChartSection.PHOTOVOLTAICS]: ['atk_solar_power'],
    [FlowChartSection.BATTERY]: ['atk_battery_power'],
    [FlowChartSection.BATTERY_SOC]: ['atk_battery_charge'],
    [FlowChartSection.GRID]: ['atk_grid_power'],
    [FlowChartSection.GRID_SELL]: ['atk_grid_energy_sell'],
    [FlowChartSection.GRID_BUY]: ['atk_grid_energy_buy'],
    // [FlowChartSection.HOUSEHOLD_OLD]: ['atk_solar_power', 'atk_battery_power', 'atk_grid_power'], // TODO: NEED TO COUNT, SO PROBABLY USE ARRAY FOR ALL PROPERTIES AND THEN SUM
    [FlowChartSection.HOUSEHOLD]: ['atk_home_power', 'power'],
    [FlowChartSection.HEATING]: ['power'],
    [FlowChartSection.HEATING_TEMP]: ['temp'],
    [FlowChartSection.TUV]: undefined,
    [FlowChartSection.WALLBOX]: undefined,
  },
  [InverterVendors.INVT]: {
    [FlowChartSection.PHOTOVOLTAICS]: ['atk_solar_power'],
    [FlowChartSection.BATTERY]: ['atk_battery_power'],
    [FlowChartSection.BATTERY_SOC]: ['atk_battery_charge'],
    [FlowChartSection.GRID]: ['atk_grid_power'],
    [FlowChartSection.GRID_SELL]: ['atk_grid_energy_sell'],
    [FlowChartSection.GRID_BUY]: ['atk_grid_energy_buy'],
    // [FlowChartSection.HOUSEHOLD_OLD]: ['atk_solar_power', 'atk_battery_power', 'atk_grid_power'],
    [FlowChartSection.HOUSEHOLD]: ['atk_home_power', 'power'],
    [FlowChartSection.HEATING]: ['power'],
    [FlowChartSection.HEATING_TEMP]: ['temp'],
    [FlowChartSection.TUV]: undefined,
    [FlowChartSection.WALLBOX]: undefined,
  }
};

export function normalizeUnitByThreshold({
  value,
  unit,
  reduceOverThreshold,
  reductionDivider,
  abs,
}: {
  value: number;
  unit: string;
  reduceOverThreshold?: number;
  reductionDivider?: number;
  abs?: boolean,
}): { value: number; unit: string } {
  if (!value || !unit) {
    return { value, unit };
  }
  if (
    typeof reduceOverThreshold === 'number' &&
    typeof reductionDivider === 'number' &&
    (abs ? Math.abs(value) : value) > reduceOverThreshold
  ) {
    const adjustedValue = Math.round(((abs ? Math.abs(value) : value) / reductionDivider) * 100) / 100;
    let adjustedUnit: string;
    switch(unit) {
      case 'W':
        adjustedUnit = 'kW';
        break;
      case 'Wh':
        adjustedUnit = 'Wh';
        break;
      default:
        adjustedUnit = unit;
    }

    return { value: adjustedValue, unit: adjustedUnit };
  }

  return { value, unit };
}

export function getFlowChartSectionValue({
  inverterType,
  section,
  currentData,
  valueMultiplier,
  min,
}: {
  inverterType: InverterVendors;
  section: FlowChartSection;
  currentData: InverterCurrentDataTransformed | WaterHeaterCurrentDataTransformed;
  valueMultiplier?: number;
  min?: number;
}): { value: number; unit: string } | null {
  const metricsKeys = inverterMetricsVendorMap[inverterType]?.[section];
  if (!metricsKeys || metricsKeys.length === 0 || !currentData) {
    return null;
  }

  const availableMetrics: any[] = metricsKeys
    .map(key => currentData.metrics[key as keyof (InverterCurrentDataTransformed | WaterHeaterCurrentDataTransformed)['metrics']])
    .filter(m => m !== undefined);

  if (availableMetrics.length === 0) {
    return null;
  }

  let value = Math.round(
    availableMetrics.reduce((sum, metric) => sum + (metric.value as number), 0) * (valueMultiplier ?? 1) * 100
  ) / 100;
  const unit = availableMetrics[0].unit;

  if (typeof min === 'number') {
    value = Math.max(0, value);
  }

  return { value, unit };
}

export function getInstallationInverterType({
  installationDetail,
} : {installationDetail: InstallationDetailData}): InverterVendors | null {
  const inverterInstance = installationDetail.deviceInstances?.find(
    instance => instance.device?.type?.name === 'inverter'
  );

  if (!inverterInstance || !inverterInstance.device?.vendor?.name) {
    return null;
  }

  switch (inverterInstance.device.vendor.name.toLowerCase()) {
    case 'goodwe':
      return InverterVendors.GOODWE;
    case 'invt':
      return InverterVendors.INVT;
    default:
      return null;
  }
}

export function getInstallationStatus({
  inverterType,
  inverterCurrentData,
}: {
  inverterType: InverterVendors;
  inverterCurrentData: InverterCurrentDataTransformed;
}): string {
  if (inverterType === InverterVendors.GOODWE && Number.isInteger(inverterCurrentData.metrics.work_mode?.value)) {
    return WorkMode[inverterCurrentData.metrics.work_mode.value];
  }
  if (inverterType === InverterVendors.INVT && Number.isInteger(inverterCurrentData.metrics.inverter_working_state?.value)) {
    return WorkModeINVT[inverterCurrentData.metrics.inverter_working_state.value];
  }
  return 'Unknown';
}
