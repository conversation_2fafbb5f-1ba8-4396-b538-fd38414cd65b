{
    // Use the project's typescript version
    "typescript.tsdk": "node_modules/typescript/lib",
    // "cSpell.enabledLanguageIds": ["markdown", "plaintext", "text", "yml"],
    // Use prettier to format typescript, javascript and JSON files
    "eslint.format.enable": true,
    "eslint.useFlatConfig": true,
    "editor.detectIndentation": false,
  	"editor.insertSpaces": true,
  	"editor.tabSize": 2,
    "eslint.workingDirectories": [
        {
            "mode": "auto"
        }
    ],
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "prettier.enable": false,
    "editor.formatOnSave": false,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "always"
    },
    "[typescript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features",
    },
    "[javascript]": {
        "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features",
    },
    "vue3snippets.enable-compile-vue-file-on-did-save-code": false,
    "eslint.lintTask.enable": true,
    "i18n-ally.localesPaths": "src/lang",
    "i18n-ally.namespace": false,
    "i18n-ally.pathMatcher": "{locale}.json",
    "i18n-ally.keystyle": "nested",
    "[go]": {
      "editor.defaultFormatter": "golang.go"
    },
    "vite.autoStart": false,
    "[css]": {
      "editor.defaultFormatter": "vscode.css-language-features"
    },
    "[svg]": {
      "editor.defaultFormatter": "jock.svg"
    },
}