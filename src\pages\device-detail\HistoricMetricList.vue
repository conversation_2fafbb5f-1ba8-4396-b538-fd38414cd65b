<template>
  <div>
    <h2 class="text-xl font-bold mb-4">
      Available Historic Metrics
    </h2>
    <div class="space-y-2">
      <div
        v-for="metric in historicMetrics"
        :key="metric.name"
        class="text-sm text-gray-800"
      >
        <span class="font-semibold">{{ metric.name }}</span>
        <span class="text-gray-500">({{ metric.unit }})</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DeviceStatsMetricsApiResponse } from '@/util/types/api-responses';
import { computed } from 'vue';

interface Props {
  statsData: DeviceStatsMetricsApiResponse;
}

const props = defineProps<Props>();

const historicMetrics = computed(() =>
  props.statsData.metrics.filter(m => m.from && m.to)
);
</script>
