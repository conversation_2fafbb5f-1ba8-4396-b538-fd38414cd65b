export const arrayTillValue = <T, >(arr: T[], value: T) => arr.slice(0, arr.findIndex(item => item === value));

export const arrayToObjectByKey = <T extends Record<string, unknown>>(array: T[], key: keyof T): Record<string, T> => {
  return array.reduce((acc: Record<string, T>, item: T) => {
    const itemKey = item[key];
    acc[itemKey as unknown as string] = item; // Ensure key is a string
    return acc;
  }, {});
};
