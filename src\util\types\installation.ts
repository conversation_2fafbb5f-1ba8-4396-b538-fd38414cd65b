export enum ChartType {
  S<PERSON>AR_PRODUCION = 'SOLAR_PRODUCION',
  GENERATION = 'GENERATION',
  SELFCONS = 'SELFCONS',
  CONTRIBUTION = 'CONTRIBUTION',
  ENERGY = 'ENERGY',
}

export enum MetricChartViewType {
  DAY = 'DAY',
  SEVEN_DAYS = 'SEVEN_DAYS',
  ONE_MONTH = 'ONE_MONTH',
  THREE_MONTHS = 'THREE_MONTHS',
  PICK_DAY = 'PICK_DAY',
  PICK_INTERVAL = 'PICK_INTERVAL',
}

export interface MetricChartInputs {
  viewType: MetricChartViewType,
  dateStart: string,
  dateEnd: string,
  xAxisTimeFormat: string,
  aggregationFloat?: string,
  aggregationCounter?: string,
}

export type MetricChartInputSetters = {
  setToday: () => void,
  setSevenDays: () => void,
  setOneMonth: () => void,
  setThreeMonths: () => void,
  setCustomDay: (customDayInput: string) => void,
  setCustomInterval: (customDayInputStart: string, customDayInputEnd: string) => void,
  addDay: () => void,
  subtractDay: () => void,
}
