import type { ApiRangeMultiDataResponse } from '@/util/types/api-responses';
import type { DataSeries } from '@/util/types/data-series';

interface WorkerInput {
  float: ApiRangeMultiDataResponse;
  counter: ApiRangeMultiDataResponse;
  nameMap: Record<string, string>;
}

function filter(item: ApiRangeMultiDataResponse[number]): boolean {
  let pointValueKey: keyof typeof item.data.data[0];
  switch (true) {
    case Boolean(item.data?.data?.[0]) === false:
      return false;
    case Object.hasOwn(item.data.data[0], 'avg'):
      pointValueKey = 'avg';
      break;
    case Object.hasOwn(item.data.data[0], 'v'):
      pointValueKey = 'v';
      break;
    case Object.hasOwn(item.data.data[0], 'delta'):
      pointValueKey = 'delta';
      break;
    default:
      return false;
  }
  const hasValue = item.data.data.some(point => point[pointValueKey] !== 0);
  return !Object.hasOwn(item, 'error') && hasValue;
}

function transform(item: ApiRangeMultiDataResponse[number], nameMap: Record<string, string>): DataSeries[0] {
  const isCounter = item.data.type === 'float_counter';
  return {
    name: nameMap[item.metric] ?? item.metric,
    originalMetricName: item.metric,
    unit: item.data.unit,
    type: isCounter ? 'column' : 'line',
    data: item.data.data.map(point => ({
      x: point.t,
      y: isCounter
        ? roundTo3(point.delta ?? 0)
        : roundTo3(point.avg ?? point.v ?? 0)
    }))
  };
}

function roundTo3(value: number): number {
  return Math.round(value * 1000) / 1000;
}

self.onmessage = (event: MessageEvent<WorkerInput>) => {

  const { float, counter, nameMap } = event.data;
  const dataSeries: DataSeries = [
    ...(float || []).filter(filter).map(item => transform(item, nameMap)),
    ...(counter || []).filter(filter).map(item => transform(item, nameMap))
  ];

  postMessage(dataSeries);
};
