import { DateTime } from 'luxon';
import { isLightModeEnabled } from '@/composables/theme';

export const defaultColors = [
  '#5470C6', '#91CC75', '#EE6666', '#fac858', '#73c0de', '#3ba272', '#fc8452'
];

export function createDefaultChartOptions(metric: { name: string; unit: string }, seriesData: any[]) {
  return {
    darkMode: !isLightModeEnabled.value,
    color: defaultColors,
    tooltip: {
      confine: true,
      textStyle: {
        overflow: 'breakAll',
        width: 40,
      },
      trigger: 'axis',
      axisPointer: { type: 'cross' },
    },
    legend: {
      show: true,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
      padding: [
        16,  // up
        0, // right
        10,  // down
        0, // left
      ]
    },
    grid: {
      left: 50,
      right: 50,
      bottom: 32,
      top: 60
    },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2',
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
      },
      axisLabel: {
        formatter: (val: number) => DateTime.fromMillis(val).toFormat('dd.MM.yyyy'),
      }
    },
    yAxis: {
      type: 'value',
      name: metric.unit,
      alignTicks: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: defaultColors[0]
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2'
        },
      },
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: metric.name,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { width: 2 },
        data: seriesData,
      }
    ],
    dataZoom: [
      {
        type: 'slider'
      }
    ]
  };
}

export function createMultiSeriesChartOptions(seriesData: any[], units: string[]) {
  // Create y-axes for different units
  const yAxes = units.map((unit, index) => ({
    type: 'value',
    name: unit,
    position: index % 2 === 0 ? 'left' : 'right',
    offset: index > 1 ? 50 * Math.floor(index / 2) : 0,
    alignTicks: true,
    axisLine: {
      show: true,
      lineStyle: {
        color: defaultColors[index % defaultColors.length]
      }
    },
    splitLine: {
      show: index === 0, // Only show split lines for the first axis to avoid clutter
      lineStyle: {
        type: 'dashed',
        opacity: '0.2'
      },
    },
    axisLabel: {
      formatter: '{value}'
    }
  }));

  // Assign series to appropriate y-axis based on unit
  const processedSeries = seriesData.map((series, index) => {
    const unitIndex = units.indexOf(series.unit);
    return {
      ...series,
      yAxisIndex: unitIndex >= 0 ? unitIndex : 0,
      color: defaultColors[index % defaultColors.length],
    };
  });

  return {
    darkMode: !isLightModeEnabled.value,
    color: defaultColors,
    tooltip: {
      confine: true,
      textStyle: {
        overflow: 'breakAll',
        width: 40,
      },
      trigger: 'axis',
      axisPointer: { type: 'cross' },
    },
    legend: {
      show: true,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
      padding: [
        16,  // up
        0, // right
        10,  // down
        0, // left
      ]
    },
    grid: {
      left: units.length > 1 ? 80 : 50,
      right: units.length > 1 ? 80 : 50,
      bottom: 32,
      top: 60
    },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: '0.2',
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
      },
      axisLabel: {
        formatter: (val: number) => DateTime.fromMillis(val).toFormat('dd.MM.yyyy'),
      }
    },
    yAxis: yAxes,
    series: processedSeries,
    dataZoom: [
      {
        type: 'inside',
        realtime: true,
        xAxisIndex: 0,
        throttle: 50,
      }
    ]
  };
}