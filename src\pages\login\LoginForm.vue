<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo.svg';
import PageLoader from '@/components/global/PageLoader.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { InputPassword } from '@/shadcn-components/ui/input-password';
import { Label } from '@/shadcn-components/ui/label';
import { useAuthStore } from '@/stores/auth-store';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';

const authStore = useAuthStore();
const { t } = useI18n();
const componentState = ref(ComponentStateType.OK);
const route = useRoute();
const router = useRouter();

const loginCredentials = reactive({
  login: '',
  password: '',
});

const onLoginRequest = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    await authStore.login(loginCredentials.login, loginCredentials.password);
    const queryTmp = { ...route.query };
    delete queryTmp.redirect;
    if (authStore.isUnverified) {
      await router.replace({ name: routeMap.verifyMail.name });
    } else {
      await router.replace({ query: queryTmp, path: route.query.redirect as string ?? '/' });
    }
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('login.errorLoggingIn'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

</script>

<template>
  <div class="mx-auto w-[18rem] lw:w-[22rem]">
    <div
      v-if="componentState === ComponentStateType.OK"
      class="w-full grid gap-4"
    >
      <div class="w-fit mx-auto">
        <SolarCloudLogo class="h-16 [&_.st1]:fill-prim-col-selected-1!" />
      </div>
      <div class="grid gap-2 text-center">
        <h1 class="text-3xl font-bold">
          {{ $t('login.login') }}
        </h1>
        <p class="text-sm text-balance text-muted-foreground">
          {{ $t('login.loginDesc') }}
        </p>
      </div>
      <form
        class="grid gap-4"
        @submit.prevent="onLoginRequest"
      >
        <div class="grid gap-2">
          <Label for="email">{{ $t('misc.mail') }}</Label>
          <Input
            v-model="loginCredentials.login"
            type="email"
            name="email"
            autocomplete="email"
            placeholder="<EMAIL>"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500"
          />
        </div>
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="password">{{ $t('misc.password') }}</Label>
            <router-link
              :to="{name: routeMap.forgotPassword.name}"
              class="ml-auto text-xs underline block"
              tabindex="-1"
            >
              {{ $t('login.forgot-password') }}
            </router-link>
          </div>
          <InputPassword
            id="password"
            v-model="loginCredentials.password"
            type="password"
            name="password"
            autocomplete="current-password"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500"
          />
        </div>
        <Button
          type="submit"
          class="w-full"
        >
          {{ $t('login.signIn') }}
        </Button>
      </form>
      <router-link :to="{name: routeMap.register.name}">
        <Button
          type="submit"
          class="w-full bg-green-400 hover:bg-green-400/80"
        >
          {{ $t('register.create-account') }}
        </Button>
      </router-link>
    </div>
    <div v-else-if="componentState === ComponentStateType.LOADING">
      <PageLoader :flex-center="true" />
    </div>
  </div>
</template>
