import { watch } from 'vue';
import { useBreadcrumbStore } from '@/stores/breadcrumb-store';
import type { RouteLocationNormalizedLoaded } from 'vue-router';

export type I18nString = string;

export type AdvancedBreadcrumbItem = {
  title: string|I18nString,
  routeFullPath?: string,
}

export const resetBreadcrumb = () => {
  const breadcrumbStore = useBreadcrumbStore();
  breadcrumbStore.resetBreadcrumb();
};

export const initializeBasicBreadcrumbBehaviour = (title: string|I18nString, routeName: string, isRoot: boolean, route: RouteLocationNormalizedLoaded) => {
  const breadcrumbStore = useBreadcrumbStore();

  if (isRoot) {
    breadcrumbStore.setBreadcrumb([{
      title,
      routeName,
    }]);
  } else {
    breadcrumbStore.appendBreadcrumb({
      title,
      routeName,
    });
  }

  watch(() => route.fullPath, () => {
    if (isRoot) {
      breadcrumbStore.resetBreadcrumb();
    }
    breadcrumbStore.appendBreadcrumb({
      title,
      routeName,
    });
  });
};

export const initializeAdvancedBreadcrumbBehaviourArr = (items: AdvancedBreadcrumbItem[] | undefined, route: RouteLocationNormalizedLoaded) => {
  const breadcrumbStore = useBreadcrumbStore();

  items?.forEach(item => {
    breadcrumbStore.appendBreadcrumb({
      title: item.title,
      routeFullPath: item.routeFullPath,
    });
  });

  watch(() => route.fullPath, () => {
    items?.forEach(item => {
      breadcrumbStore.appendBreadcrumb({
        title: item.title,
        routeFullPath: item.routeFullPath,
      });
    });
  });
};
