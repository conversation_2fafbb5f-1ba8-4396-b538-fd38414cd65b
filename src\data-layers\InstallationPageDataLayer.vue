<script lang="ts" setup>
import { DateTime } from 'luxon';
import { onBeforeUnmount, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import InstallationPage from '@/pages/InstallationPage.vue';
import { useInstallationStore } from '@/stores/installation-store';
import { customAxios } from '@/util/axios';
import { CustomEvents, sendCustomEvent } from '@/util/facades/event';
import type { InverterCurrentDataResponse, InverterCurrentDataTransformed, ApiMetricKeys, DeltaData } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';
import { deployToast, ToastType } from '@/util/toast.ts';
import { useI18n } from 'vue-i18n';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types';
import type { TuyaDevice } from '@/pages/installation/types/tuya-types';
import type { ApiMetricKeysWaterHeater, WaterHeaterCurrentDataTransformed } from '@/util/types/api-responses-heater';

const metricsCurrentWorker = new Worker(new URL('@/workers/metrics-current.worker.ts', import.meta.url), {
  type: 'module',
});

const metricsCurrentWaterHeaterWorker = new Worker(new URL('@/workers/metrics-current.worker.ts', import.meta.url), {
  type: 'module',
});

const metricsDeltaWorker = new Worker(new URL('@/workers/metrics-delta-multi.worker.ts', import.meta.url), {
  type: 'module',
});

const route = useRoute();
const installationStore = useInstallationStore();
const { t } = useI18n();

const inverterCurrentData = ref<InverterCurrentDataTransformed>();
const waterHeaterCurrentData = ref<WaterHeaterCurrentDataTransformed>();
const deltaData = ref<DeltaData>({} as DeltaData);
const installationDetail = ref<InstallationDetailData>();
const tuyaDevices = ref<TuyaDevice[]>([]);
let isFirstFetch = true;

const componentState = ref({
  global: ComponentStateType.LOADING,
  currentMetrics: ComponentStateType.OK,
  currentMetricsWaterHeater: ComponentStateType.OK,
  todayDelta: ComponentStateType.OK,
});
const fetchDataTimeouts = [] as ReturnType<typeof setTimeout>[];
const clearTimeouts = () => {
  fetchDataTimeouts.forEach(t => clearTimeout(t));
  fetchDataTimeouts.length = 0;
};

metricsCurrentWorker.onmessage = (event: MessageEvent<InverterCurrentDataTransformed>) => {
  inverterCurrentData.value = event.data;
  componentState.value.currentMetrics = ComponentStateType.OK;
};

metricsCurrentWaterHeaterWorker.onmessage = (event: MessageEvent<WaterHeaterCurrentDataTransformed>) => {
  waterHeaterCurrentData.value = event.data;
  componentState.value.currentMetricsWaterHeater = ComponentStateType.OK;
};

const fetchRecentInverterData = async() => {
  try {
    const axiosRes = await customAxios.post<InverterCurrentDataResponse>(`metrics/${route.params.installationId}/inverter/current`, {
      metrics: [
        'atk_solar_energy',
        'atk_grid_energy_buy',
        'atk_grid_energy_sell',
        'safety_country_text',
        'atk_solar_power',
        'atk_home_power',
        'atk_battery_charge',
        'atk_grid_power',
        'atk_battery_power',
        'total_load_power',
        'work_mode',
        'inverter_working_state',
        'small_packet_interval',
      ] as ApiMetricKeys[],
    });
    metricsCurrentWorker.postMessage(axiosRes.data);
    const nextFetchTime = DateTime.fromISO(axiosRes.data[0].time).plus({ seconds: (inverterCurrentData.value?.metrics.small_packet_interval.value ?? 60) + 3 });
    const now = DateTime.now();
    return nextFetchTime.diff(now).milliseconds;
  } catch (e: any) {
    inverterCurrentData.value = undefined;
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.currentMetrics = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.currentMetrics = ComponentStateType.ERROR;
  }
};

const fetchRecentWaterHeaterData = async() => {
  try {
    const axiosRes = await customAxios.post<InverterCurrentDataResponse>(`metrics/${route.params.installationId}/ac-water-heater/current`, {
      metrics: [
        'energy_total',
        'power',
        'temp',
        'temp_max',
      ] as ApiMetricKeysWaterHeater[],
    });
    metricsCurrentWaterHeaterWorker.postMessage(axiosRes.data);
  } catch (e: any) {
    waterHeaterCurrentData.value = undefined;
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.currentMetricsWaterHeater = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.currentMetricsWaterHeater = ComponentStateType.ERROR;
  }
};

metricsDeltaWorker.onmessage = (event: MessageEvent<DeltaData>) => {
  deltaData.value = event.data;
  componentState.value.todayDelta = ComponentStateType.OK;
};

const fetchDeltaMultiData = async() => {
  try {
    const axiosRes = await customAxios.get<{metric: string, value: number}[]>(`metrics/${route.params.installationId}/inverter/delta`, {
      params: {
        type: ['atk_solar_energy', 'atk_grid_energy_buy', 'atk_grid_energy_sell'] as ApiMetricKeys[], // 'atk_grid_energy_buy'
        start: DateTime.now().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).toISO(),
        end: DateTime.now().toISO(),
      },
    });
    metricsDeltaWorker.postMessage(axiosRes.data);
  } catch (e: any) {
    Object.keys(deltaData.value).forEach(k => {
      delete deltaData.value[k as keyof typeof deltaData.value];
    });
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.todayDelta = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.todayDelta = ComponentStateType.ERROR;
  }
};

const fetchInstallationDetail = async() => {
  try {
    const response = await customAxios.get<{data: InstallationDetailData}>(`/user/${route.params.installationId}/detail`);
    installationDetail.value = response.data.data;
  } catch (error) {
    console.error('Failed to fetch installation detail:', error);
  }
};

const fetchTuyaDevices = async() => {
  if (!installationDetail.value?.hasTuyaToken) {
    tuyaDevices.value = [];
    return;
  }
  try {
    const { data } = await customAxios.get<{devices: TuyaDevice[]}>(`/tuya/${route.params.installationId}/devices`);
    tuyaDevices.value = data.devices;
  } catch (e: any) {
    tuyaDevices.value = [];
    console.error(e);
  }
};

const fetchInstallationData = async() => {
  clearTimeouts();
  // these do not include chart data
  const [millis] = await Promise.all([
    fetchRecentInverterData(),
    fetchRecentWaterHeaterData(),
    fetchDeltaMultiData(),
    isFirstFetch ? undefined : fetchInstallationDetail(),
  ]);
  if (isFirstFetch) {
    await fetchInstallationDetail();
    isFirstFetch = false;
  }
  fetchTuyaDevices();

  let delay = 30000;
  if (millis) {
    // Set a minimum delay of 30 seconds (30000 milliseconds)
    delay = millis > 0 ? Math.max(millis, 30000) : 30000;
  }

  fetchDataTimeouts.push(
    setTimeout(() => {
      fetchInstallationData();
    }, delay),
  );

  await installationStore.fetchInstallations();

  sendCustomEvent({ message: 'Hello, children!' }, CustomEvents.FETCHED_INSTALLATION_DATA);
};

await fetchInstallationData();

componentState.value.global = ComponentStateType.OK;

watch(() => route.params.installationId, async() => {
  componentState.value.global = ComponentStateType.LOADING;
  isFirstFetch = true;
  await fetchInstallationData();
  componentState.value.global = ComponentStateType.OK;
});

watch(() => installationDetail.value, (newValue, oldValue) => {
  if (!newValue?.deviceInstances?.length && !oldValue?.deviceInstances?.length) {
    return;
  }
  if (newValue?.id !== oldValue?.id) {
    return;
  }
  if (newValue!.deviceInstances.length > oldValue!.deviceInstances.length) {
    const pairedIds = oldValue!.deviceInstances.map(deviceInstance => deviceInstance.id);
    const newDevices = newValue!.deviceInstances.filter(deviceInstance => !pairedIds.includes(deviceInstance.id));
    newDevices.forEach(newDevice => {
      deployToast(ToastType.INFO, {
        text: t('installation.new-device-added') + ` - ${newDevice.device!.vendor!.name} ${newDevice.device!.model} (${newDevice.device!.type!.name})`,
        timeout: 10000,
      });
    });
  }
});

onBeforeUnmount(() => {
  fetchDataTimeouts.forEach(t => {
    clearTimeout(t);
  });
  fetchDataTimeouts.length = 0;
});
</script>

<template>
  <section>
    <InstallationPage
      v-if="componentState.global === ComponentStateType.OK && ![componentState.currentMetrics, componentState.todayDelta].includes(ComponentStateType.NOT_FOUND_OR_FORBIDDEN)"
      :inverter-current-data="inverterCurrentData!"
      :water-heater-current-data="waterHeaterCurrentData!"
      :delta-data="deltaData"
      :installation-detail="installationDetail!"
      :tuya-devices="tuyaDevices"
      @installation-updated="fetchInstallationData"
    />
    <div v-else-if="componentState.global === ComponentStateType.ERROR">
      {{ $t('misc.failed-to-get-data') }}
    </div>
    <div
      v-else-if="[componentState.currentMetrics, componentState.todayDelta].includes(ComponentStateType.NOT_FOUND_OR_FORBIDDEN)"
      class="absolute-center text-muted-foreground"
    >
      {{ $t('installation.not-exists-or-forbidden') }}
    </div>
  </section>
</template>
