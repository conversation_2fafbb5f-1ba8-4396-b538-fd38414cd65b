<script setup lang="ts">

import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  credentials: {
    newPassword: string,
    repeatedNewPassword: string,
  }
}

const props = defineProps<Props>();
const { t } = useI18n();

const passwordValidationMessages = computed(() => {
  const { newPassword } = props.credentials;
  const messages: string[] = [];

  if (!newPassword) {
    return messages;
  }

  if (newPassword.length < 10) {
    messages.push(t('login.pw-helper.length'));
  }
  if (!/[a-z]/.test(newPassword)) {
    messages.push(t('login.pw-helper.lowercase'));
  }
  if (!/[A-Z]/.test(newPassword)) {
    messages.push(t('login.pw-helper.uppercase'));
  }
  if (!/[0-9]/.test(newPassword)) {
    messages.push(t('login.pw-helper.number'));
  }
  if (!/[^a-zA-Z0-9]/.test(newPassword)) {
    messages.push(t('login.pw-helper.symbol'));
  }

  return messages;
});
</script>

<template>
  <transition :duration="100">
    <div
      v-if="credentials.newPassword && credentials.newPassword.length > 0 && (passwordValidationMessages.length > 0 || credentials.newPassword !== credentials.repeatedNewPassword)"
      class="w-full flex flex-col gap-1"
    >
      <div
        v-if="credentials.newPassword !== credentials.repeatedNewPassword"
        class="bg-red-500 rounded-md text-white p-1 text-xs"
      >
        {{ $t('login.pw-do-not-match') }}
      </div>
      <div
        v-for="(message, index) in passwordValidationMessages"
        :key="index"
        class="bg-red-500 rounded-md text-white p-1 text-xs"
      >
        {{ message }}
      </div>
    </div>
  </transition>
</template>
