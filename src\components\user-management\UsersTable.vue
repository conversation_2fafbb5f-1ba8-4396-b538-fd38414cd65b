<script lang="ts" setup>
import { Pencil, Trash2, Mail, Phone, User } from 'lucide-vue-next';
import { Badge } from '@/shadcn-components/ui/badge';
import { Button } from '@/shadcn-components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { useI18n } from 'vue-i18n';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { UserManagementData } from '@/util/types/user-management';
import type { RoleData } from '@/util/types/roles-and-permissions';

interface Props {
  users: UserManagementData;
  loading?: boolean;
  allRoles?: RoleData[];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  allRoles: () => [],
});

const emit = defineEmits<{
  'edit-user': [userId: string];
  'delete-user': [];
}>();

const { t } = useI18n();

const getRoleNames = (roles: { id: number, name: string }[]) => {
  if (!props.allRoles || !Array.isArray(roles)) {return [];}
  return roles
    .map(role => role.id)
    .map(id => props.allRoles?.find(role => role.id === id)?.name)
    .filter(Boolean);
};

const handleEditUser = (userId: string) => {
  emit('edit-user', userId);
};

const handleDeleteUser = async(user: UserManagementData[0]) => {
  try {
    await customAxios.delete(`/users/${user.id}`);
    emit('delete-user');
    deployToast(ToastType.SUCCESS, {
      text: t('user-management.success-delete-user'),
      timeout: 6000,
    });
  } catch (error) {
    console.error('Failed to delete user:', error);
    deployToast(ToastType.ERROR, {
      text: t('user-management.user-delete-fail'),
      timeout: 6000,
    });
  }
};

const formatUserName = (user: UserManagementData[0]) => {
  const parts = [user.name, user.surname].filter(Boolean);
  return parts.length > 0 ? parts.join(' ') : '-';
};

const hasContactInfo = (user: UserManagementData[0]) => {
  return user.phone || user.email;
};
</script>

<template>
  <div class="w-full">
    <div class="rounded-xl border dark:border-[var(--color-border)] border-gray-300/70 overflow-x-auto">
      <!-- Header -->
      <div class="grid grid-cols-[minmax(8rem,1fr)_minmax(8rem,1fr)_8rem_minmax(6rem,1fr)_minmax(6rem,1fr)_6rem] gap-4 p-4 dark:bg-muted/50 bg-prim-col-foreground-1 border-b dark:border-black border-gray-300/70 font-medium text-sm will-change-transform min-w-full w-fit">
        <div class="w-full">
          {{ $t('user-management.user') }}
        </div>
        <div class="w-full">
          {{ $t('user-management.contact') }}
        </div>
        <div class="w-full">
          {{ $t('user-management.organization') }}
        </div>
        <div class="w-full">
          {{ $t('user-management.roles') }}
        </div>
        <div class="w-full">
          {{ $t('user-management.permissions') }}
        </div>
        <div class="w-full text-right">
          {{ $t('user-management.actions') }}
        </div>
      </div>

      <!-- User rows -->
      <div class="divide-y">
        <div
          v-for="user in users"
          :key="user.id"
          class="grid grid-cols-[minmax(8rem,1fr)_minmax(8rem,1fr)_8rem_minmax(6rem,1fr)_minmax(6rem,1fr)_6rem] gap-4 p-4 hover:bg-prim-col-selected-1/10 transition-colors items-center min-w-full w-fit text-black dark:text-white dark:border-black border-gray-300/70"
        >
          <!-- User Info -->
          <div class="font-medium">
            <div class="flex items-center gap-2">
              <User class="h-4 w-4 text-gray-400" />
              <span class="font-semibold line-clamp-1 inline-block break-all">
                {{ formatUserName(user) }}
              </span>
            </div>
          </div>

          <!-- Contact Info -->
          <div class="">
            <div v-if="hasContactInfo(user)" class="space-y-1">
              <div v-if="user.email" class="flex items-center gap-1 text-sm">
                <Mail class="h-3 w-3 text-gray-400" />
                <span class="truncate">{{ user.email }}</span>
              </div>
              <div v-if="user.phone" class="flex items-center gap-1 text-sm">
                <Phone class="h-3 w-3 text-gray-400" />
                <span class="truncate">{{ user.phone }}</span>
              </div>
            </div>
            <div v-else class="text-sm text-muted-foreground">
              {{ $t('user-management.no-contact-info') }}
            </div>
          </div>

          <div class="">
            <div v-if="user.organization" class="space-y-1">
              <span class="text-sm truncate">{{ user.organization.name }}</span>
            </div>
            <div v-else class="text-sm text-muted-foreground">
              -
            </div>
          </div>

          <!-- Roles -->
          <div class="">
            <div v-if="user.roles && user.roles.length > 0" class="flex flex-wrap gap-1">
              <Badge
                v-for="roleName in getRoleNames(user.roles).slice(0, 2)"
                :key="roleName"
                variant="default"
                class="text-xs"
              >
                {{ roleName }}
              </Badge>
              <Badge
                v-if="getRoleNames(user.roles).length > 2"
                variant="outline"
                class="text-xs"
              >
                +{{ getRoleNames(user.roles).length - 2 }}
              </Badge>
            </div>
            <div v-else class="text-sm text-muted-foreground">
              {{ $t('user-management.no-roles') }}
            </div>
          </div>

          <!-- Permissions -->
          <div class="">
            <div v-if="user.permissions && user.permissions.length > 0" class="text-sm" :title="user?.permissions?.join(', ')">
              <span class="font-mono">{{ user.permissions.length }}</span>
              <span class="text-muted-foreground ml-1">
                {{ $t('user-management.permissions-count') }}
              </span>
            </div>
            <div v-else class="text-sm text-muted-foreground">
              {{ $t('user-management.no-permissions') }}
            </div>
          </div>

          <!-- Actions -->
          <div class="text-right flex justify-end items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              class="h-8 w-8 p-0"
              :title="$t('user-management.edit-user')"
              @click="handleEditUser(user.id.toString())"
            >
              <Pencil class="h-4 w-4" />
              <span class="sr-only">
                {{ $t('user-management.edit-user') }}
              </span>
            </Button>

            <AlertDialog>
              <AlertDialogTrigger as-child>
                <Button
                  variant="ghost"
                  size="sm"
                  class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  :title="$t('user-management.delete-user')"
                >
                  <Trash2 class="h-4 w-4" />
                  <span class="sr-only">
                    {{ $t('user-management.delete-user') }}
                  </span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {{ $t('user-management.delete-user-action', { name: formatUserName(user) }) }}
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {{ $t('user.delete-user.modal-desc') }}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                  <AlertDialogAction
                    class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    @click="handleDeleteUser(user)"
                  >
                    {{ $t('misc.continue') }}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>

      <!-- Empty states -->
      <div
        v-if="users.length === 0 && !loading"
        class="text-center py-8"
      >
        <div class="text-muted-foreground mb-2">
          {{ $t('user-management.no-users') }}
        </div>
      </div>

      <div
        v-if="loading"
        class="text-center py-8"
      >
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2" />
        <div class="text-muted-foreground">
          {{ $t('user-management.loading') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
