<script setup lang="ts">
import { ref } from 'vue';
import GenericFormRenderer from './GenericFormRenderer.vue';
import type { BackendFormField, FormErrors } from '@/pages/device-detail/types/device-configuration-types';

// Example form data
const formData = ref({
  name: '',
  email: '',
  age: null,
  country: '',
  newsletter: false,
  terms: false,
});

// Example errors (simulating Laravel validation response)
const formErrors = ref<FormErrors>({
  name: ['validation.required'],
  email: ['validation.email'],
  terms: ['validation.accepted'],
});

// Example fields configuration
const fields: BackendFormField[] = [
  {
    key: 'name',
    label: 'Full Name',
    type: 'text',
    required: true,
    help: 'Enter your full name',
  },
  {
    key: 'email',
    label: 'Email',
    type: 'text',
    required: true,
  },
  {
    key: 'age',
    label: 'Age',
    type: 'number',
  },
  {
    key: 'country',
    label: 'Country',
    type: 'select',
    options: [
      { label: 'Slovakia', value: 'sk' },
      { label: 'Czech Republic', value: 'cz' },
      { label: 'Germany', value: 'de' },
      { label: 'Austria', value: 'at' },
    ],
  },
  {
    key: 'newsletter',
    label: 'Subscribe to Newsletter',
    type: 'checkbox',
  },
  {
    key: 'terms',
    label: 'Accept Terms',
    type: 'checkbox',
    required: true,
  },
];

const toggleErrors = () => {
  if (Object.keys(formErrors.value).length > 0) {
    formErrors.value = {};
  } else {
    formErrors.value = {
      name: ['validation.required'],
      email: ['validation.email'],
      terms: ['validation.accepted'],
    };
  }
};

const onSubmit = () => {
  console.log('Form data:', formData.value);
};
</script>

<template>
  <div class="max-w-2xl mx-auto p-6">
    <h2 class="text-2xl font-bold mb-6">
      Generic Form Renderer Examples
    </h2>

    <!-- Example 1: Default layout with error handling -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">
        Example 1: Default Layout with Error Handling
      </h3>
      <form class="space-y-4" @submit.prevent="onSubmit">
        <div class="grid gap-4">
          <GenericFormRenderer
            v-model="formData"
            :fields="fields"
            :errors="formErrors"
          />
        </div>
        <div class="flex gap-2">
          <button
            type="submit"
            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Submit
          </button>
          <button
            type="button"
            class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
            @click="toggleErrors"
          >
            {{ Object.keys(formErrors).length > 0 ? 'Clear Errors' : 'Show Errors' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Example 2: Custom grid layout with errors -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">
        Example 2: Custom Grid Layout (2 columns) with Errors
      </h3>
      <form class="space-y-4" @submit.prevent="onSubmit">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <GenericFormRenderer
            v-model="formData"
            :fields="fields"
            :errors="formErrors"
            field-class="flex flex-col space-y-2"
            label-class="font-medium text-sm"
          />
        </div>
        <button
          type="submit"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Submit
        </button>
      </form>
    </div>

    <!-- Debug output -->
    <div class="mt-8 p-4 bg-gray-100 rounded">
      <h4 class="font-semibold mb-2">
        Current Form Data:
      </h4>
      <pre class="text-sm mb-4">{{ JSON.stringify(formData, null, 2) }}</pre>

      <h4 class="font-semibold mb-2">
        Current Errors:
      </h4>
      <pre class="text-sm">{{ JSON.stringify(formErrors, null, 2) }}</pre>
    </div>
  </div>
</template>
