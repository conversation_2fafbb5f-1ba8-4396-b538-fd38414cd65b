<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  active: boolean
  colorVariant?: 'blueish' | 'orangeish'
}

const props = withDefaults(defineProps<Props>(), {
  colorVariant: 'blueish',
});

const colorClasses = computed(() => {
  switch (props.colorVariant) {
    case 'blueish':
      return props.active
        ? 'bg-prim-col-selected-1'
        : 'bg-prim-col-1/60 hover:bg-prim-col-1/80';
    case 'orangeish':
      return props.active
        ? 'bg-atk-orange'
        : 'bg-atk-orange/40 hover:bg-atk-orange/60';
    default:
      return props.active
        ? 'bg-prim-col-selected-1'
        : 'bg-prim-col-1/60 hover:bg-prim-col-1/80';
  }
});
</script>

<template>
  <button
    class="cursor-pointer flex items-center justify-center select-none"
    :class="colorClasses"
  >
    <slot />
  </button>
</template>