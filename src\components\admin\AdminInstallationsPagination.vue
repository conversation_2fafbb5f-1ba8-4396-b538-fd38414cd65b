<script lang="ts" setup>
import { computed } from 'vue';
import {
  Pagination,
  PaginationEllipsis,
  PaginationFirst,
  PaginationLast,
  PaginationList,
  PaginationListItem,
  PaginationNext,
  PaginationPrev,
} from '@/shadcn-components/ui/pagination';
import { Button } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shadcn-components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-vue-next';

interface Props {
  currentPage: number;
  totalPages: number;
  hasPrev: boolean;
  hasNext: boolean;
  perPage: number;
  totalItems: number;
  maxVisiblePages?: number;
}

const props = withDefaults(defineProps<Props>(), {
  maxVisiblePages: 5,
});

const emit = defineEmits<{
  pageChange: [page: number];
  perPageChange: [perPage: number];
}>();

const visiblePages = computed(() => {
  const { currentPage, totalPages, maxVisiblePages } = props;
  const pages: (number | 'ellipsis')[] = [];

  if (totalPages <= maxVisiblePages) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    const startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

    // Add ellipsis after first page if needed
    if (startPage > 2) {
      pages.push('ellipsis');
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pages.push('ellipsis');
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }

  return pages;
});

const handlePageChange = (page: number) => {
  if (page !== props.currentPage && page >= 1 && page <= props.totalPages) {
    emit('pageChange', page);
  }
};

const handleFirst = () => {
  handlePageChange(1);
};

const handleLast = () => {
  handlePageChange(props.totalPages);
};

const handlePrev = () => {
  handlePageChange(props.currentPage - 1);
};

const handleNext = () => {
  handlePageChange(props.currentPage + 1);
};

const handlePerPageChange = (newPerPage: number) => {
  emit('perPageChange', newPerPage);
};

const perPageOptions = [10, 15, 25, 50, 100];
</script>

<template>
  <div class="space-y-4">
    <!-- Per page selector and info -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-muted-foreground">
        {{ $t('admin.installations.pagination.showing', {from: ((currentPage - 1) * perPage) + 1, to: Math.min(currentPage * perPage, totalItems), total: totalItems }) }}
      </div>

      <div class="flex items-center gap-2">
        <span class="text-sm text-muted-foreground">{{ $t('misc.items-per-page') }}:</span>
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline" size="sm" class="h-8 gap-1">
              {{ perPage }}
              <ChevronDown class="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              v-for="option in perPageOptions"
              :key="option"
              class="cursor-pointer"
              @click="handlePerPageChange(option)"
            >
              {{ option }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <!-- Pagination controls -->
    <div class="flex justify-center">
      <Pagination
        v-if="totalPages > 1"
        :total="totalPages"
        :sibling-count="1"
        :show-edges="true"
        :default-page="currentPage"
        class="mx-0 w-auto"
      >
        <PaginationList class="flex items-center gap-1">
          <PaginationListItem :value="1">
            <PaginationFirst
              :disabled="currentPage === 1"
              @click="handleFirst"
            />
          </PaginationListItem>

          <!-- Previous page button -->
          <PaginationListItem :value="currentPage - 1">
            <PaginationPrev
              :disabled="!hasPrev"
              @click="handlePrev"
            />
          </PaginationListItem>

          <!-- Page numbers -->
          <template v-for="(page, index) in visiblePages" :key="index">
            <PaginationListItem v-if="page === 'ellipsis'" :value="0">
              <PaginationEllipsis />
            </PaginationListItem>

            <PaginationListItem v-else :value="page">
              <Button
                :variant="page === currentPage ? 'default' : 'outline'"
                size="sm"
                class="h-10 w-10 p-0"
                @click="handlePageChange(page)"
              >
                {{ page }}
              </Button>
            </PaginationListItem>
          </template>

          <!-- Next page button -->
          <PaginationListItem :value="currentPage + 1">
            <PaginationNext
              :disabled="!hasNext"
              @click="handleNext"
            />
          </PaginationListItem>

          <!-- Last page button -->
          <PaginationListItem :value="totalPages">
            <PaginationLast
              :disabled="currentPage === totalPages"
              @click="handleLast"
            />
          </PaginationListItem>
        </PaginationList>
      </Pagination>
    </div>
  </div>
</template>
