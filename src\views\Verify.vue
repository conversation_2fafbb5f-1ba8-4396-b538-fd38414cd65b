<script lang="ts" setup>
import { ChevronLeft } from 'lucide-vue-next';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import SolarCloudLogo from '@/assets/svg/antik-cloud-logo-w-text.svg?skipsvgo';
import PageLoader from '@/components/global/PageLoader.vue';
import GreenCheck from '@/components/states/GreenCheck.vue';
import RedFail from '@/components/states/RedFail.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { useAuthStore } from '@/stores/auth-store';
import { customAxios } from '@/util/axios';
import { sleep } from '@/util/fakers/sleep';
import { ComponentStateType } from '@/util/types/components';

const authStore = useAuthStore();
const { params } = useRoute();
const router = useRouter();
const componentState = ref(ComponentStateType.LOADING);

if (!params.token) {
  await router.replace({ name: routeMap.home.children.dashboardInstallations.name });
}

const verifyMail = async() => {
  await sleep(2000);

  try {
    await customAxios.get(`/verify-email/${params.token}`);
    await authStore.fetchUser();
    componentState.value = ComponentStateType.OK;
    if (authStore.user?.id) {
      await router.replace({ name: routeMap.home.children.dashboardInstallations.name });
    }
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

verifyMail();

</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2 bg-prim-col-1 lg:bg-transparent">
      <router-link
        :to="{name: routeMap.home.children.dashboardInstallations.name}"
        class="hidden bg-prim-col-2 lg:flex items-center justify-center"
      >
        <SolarCloudLogo class="h-52 [&_.st1]:fill-prim-col-selected-1!" />
      </router-link>
      <div class="flex items-center justify-center py-12 bg-prim-col-1">
        <div class="mx-auto w-[18rem] lw:w-[22rem]">
          <div class="w-full grid gap-6">
            <div class="grid gap-2 text-center justify-items-center">
              <h1
                v-if="componentState === ComponentStateType.LOADING"
                class="text-2xl font-bold"
              >
                {{ $t('register.verifying-email') }}
              </h1>
              <div v-if="componentState === ComponentStateType.LOADING">
                <PageLoader :flex-center="true" />
              </div>
              <div
                v-if="componentState === ComponentStateType.OK"
                class="grid gap-4 justify-items-center"
              >
                <GreenCheck class="w-16 h-16 mb-0.5" />
                <h1 class="text-2xl font-bold">
                  {{ $t('register.email-verified') }}!
                </h1>
                <router-link
                  class="w-fit block"
                  :to="{name: routeMap.home.children.dashboardInstallations.name}"
                >
                  <Button
                    v-if="!authStore.user?.id"
                    type="submit"
                    class="w-fit"
                  >
                    {{ $t('login.signIn') }}
                  </Button>
                </router-link>
              </div>
              <div
                v-if="componentState === ComponentStateType.ERROR"
                class="grid gap-4 justify-items-center"
              >
                <RedFail class="w-16 h-16 mb-0.5" />
                <h1 class="text-xl font-bold">
                  {{ $t('register.failed-verify') }}!
                </h1>
                <router-link
                  class="w-fit block"
                  :to="{name: routeMap.home.children.dashboardInstallations.name}"
                >
                  <Button
                    v-if="!authStore.user?.id"
                    type="submit"
                    class="w-fit"
                  >
                    {{ $t('misc.back-to-home') }}
                  </Button>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="cursor-pointer bg-prim-col-foreground-2/50 hover:bg-prim-col-foreground-2/80 active:bg-prim-col-foreground-2/80 absolute top-6 right-6 sm:top-4 sm:right-4 rounded-full w-10 h-10 flex items-center justify-center"
      @click="router.back()"
    >
      <ChevronLeft class="relative right-px text-white" />
    </div>
  </div>
</template>
