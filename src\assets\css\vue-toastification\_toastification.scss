// Import the regular Vue Toastification stylesheets (or create your own)
$vt-color-success: rgba(46, 173, 255, 0.95) !default;
$vt-color-error: rgba(255, 82, 82, 0.95) !default;
$vt-text-color-success: #ffffff;
$vt-color-info: rgba(70, 70, 70, 0.96) !default;
$vt-text-color-info: #ffffff !default;
$vt-color-success: #4caf50 !default;
$vt-text-color-success: #fff !default;
$vt-font-family: "Roboto", Helvetica, Arial, sans-serif !default;

@use "sass:meta";
@use "variables" with (
  $vt-color-info: $vt-color-info,
  $vt-text-color-info: $vt-text-color-info,
  $vt-color-success: $vt-color-success,
  $vt-text-color-success: $vt-text-color-success,
  $vt-color-error: $vt-color-error,
  $vt-font-family: $vt-font-family
);

@use "toastContainer";
@use "toast";

.#{variables.$vt-namespace}__toast {
  display: flex;
  align-items: center;
}

.Vue-Toastification__toast {
  max-height: 100px;
  min-height: 60px;
  padding: 10px 20px;
  max-width: 600px;
  min-width: 326px;
  @media screen and (max-width: 600px) {
    max-width: 100%;
  }
}

.Vue-Toastification__toast-body {
  font-size: 14px;
}

@include meta.load-css("closeButton");
@include meta.load-css("progressBar");
@include meta.load-css("icon");
@include meta.load-css("toastAnim");
