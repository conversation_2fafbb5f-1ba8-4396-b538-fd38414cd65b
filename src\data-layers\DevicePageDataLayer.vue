<script lang="ts" setup>
import { DateTime } from 'luxon';
import { onBeforeUnmount, ref } from 'vue';
import { useRoute } from 'vue-router';
import { customAxios } from '@/util/axios';
import type { DeviceLastMetricsApiResponse, DeviceStatsMetricsApiResponse } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';
// import { useI18n } from 'vue-i18n';
import DevicePage from '@/pages/device-detail/DevicePage.vue';
import type { DeviceInstance, InstallationDetailData } from '@/pages/installation/types/installation-types';

const metricsCurrentWorker = new Worker(new URL('@/workers/metrics-current.worker.ts', import.meta.url), {
  type: 'module',
});

const route = useRoute();
// const { t } = useI18n();
const { installationId, deviceId } = route.params as Record<string, string>;

const componentState = ref({
  global: ComponentStateType.LOADING,
  lastMetrics: ComponentStateType.LOADING,
  deviceStats: ComponentStateType.LOADING,
  deviceDetail: ComponentStateType.LOADING,
});
const fetchDataTimeouts = [] as ReturnType<typeof setTimeout>[];
const clearTimeouts = () => {
  fetchDataTimeouts.forEach(t => clearTimeout(t));
  fetchDataTimeouts.length = 0;
};

const deviceLastMetricsData = ref<DeviceLastMetricsApiResponse>();
const deviceStats = ref<DeviceStatsMetricsApiResponse>();
const deviceDetail = ref<any>();
const installationDetail = ref<InstallationDetailData>();

metricsCurrentWorker.onmessage = (event: MessageEvent<DeviceLastMetricsApiResponse>) => {
  deviceLastMetricsData.value = event.data;
  componentState.value.lastMetrics = ComponentStateType.OK;
};

const fetchRecentData = async() => {
  componentState.value.lastMetrics = ComponentStateType.LOADING;
  try {
    const axiosRes = await customAxios.get<DeviceLastMetricsApiResponse>(`user/device/last-metrics/${installationId}/${deviceId}`);
    metricsCurrentWorker.postMessage([axiosRes.data]); // We have to wrap in array so we can use the same worker as for multiple devices
    const nextFetchTime = DateTime.fromISO(axiosRes.data.time).plus({ seconds: (deviceLastMetricsData.value?.metrics?.small_packet_interval?.value ?? 60) + 3 });
    const now = DateTime.now();
    return nextFetchTime.diff(now).milliseconds;
  } catch (e: any) {
    deviceLastMetricsData.value = undefined;
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.lastMetrics = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.lastMetrics = ComponentStateType.ERROR;
  }
};

const fetchDeviceStats = async() => {
  componentState.value.deviceStats = ComponentStateType.LOADING;
  try {
    const axiosRes = await customAxios.get<DeviceStatsMetricsApiResponse>(`user/device/stats/${installationId}/${deviceId}`);
    deviceStats.value = axiosRes.data;
    componentState.value.deviceStats = ComponentStateType.OK;
  } catch (e: any) {
    deviceStats.value = undefined;
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.deviceStats = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.deviceStats = ComponentStateType.ERROR;
  }
};

const fetchDeviceDetail = async() => {
  componentState.value.deviceDetail = ComponentStateType.LOADING;
  try {
    const axiosRes = await customAxios.get<{data: DeviceInstance}>(`user/device/detail/${installationId}/${deviceId}`);
    deviceDetail.value = axiosRes.data.data;
    componentState.value.deviceDetail = ComponentStateType.OK;
  } catch (e: any) {
    deviceDetail.value = undefined;
    if (e?.response?.status === 403 || e?.response?.status === 404) {
      componentState.value.deviceDetail = ComponentStateType.NOT_FOUND_OR_FORBIDDEN;
      return;
    }
    componentState.value.deviceDetail = ComponentStateType.ERROR;
  }
};

const fetchDeviceData = async() => {
  clearTimeouts();
  // these do not include chart data
  const [millis] = await Promise.all([
    fetchRecentData(),
    fetchDeviceDetail(),
  ]);

  let delay = 30000;
  if (millis) {
    // Set a minimum delay of 30 seconds (30000 milliseconds)
    delay = millis > 0 ? Math.max(millis, 30000) : 30000;
  }

  fetchDataTimeouts.push(
    setTimeout(() => {
      fetchDeviceData();
    }, delay),
  );
};

const fetchInstallationDetail = async() => {
  try {
    const response = await customAxios.get<{data: InstallationDetailData}>(`/user/${route.params.installationId}/detail`);
    installationDetail.value = response.data.data;
  } catch (error) {
    console.error('Failed to fetch installation detail:', error);
  }
};

await Promise.all([
  fetchDeviceData(),
  fetchDeviceStats(),
  fetchInstallationDetail(),
]);

componentState.value.global = ComponentStateType.OK;

onBeforeUnmount(() => {
  fetchDataTimeouts.forEach(t => {
    clearTimeout(t);
  });
  fetchDataTimeouts.length = 0;
});
</script>

<template>
  <section>
    <DevicePage
      v-if="deviceLastMetricsData && deviceStats"
      :device-last-metrics-data="deviceLastMetricsData"
      :device-stats="deviceStats"
      :device-detail="deviceDetail"
      :installation-id="installationId"
      :installation-detail="installationDetail!"
      :device-id="deviceId"
    />
    <div v-else class="text-center opacity-50">
      {{ $t('misc.no-data') }}
    </div>
  </section>
</template>
