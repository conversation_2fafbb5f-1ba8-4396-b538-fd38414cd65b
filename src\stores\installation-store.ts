import { defineStore } from 'pinia';
import { computed, nextTick, ref } from 'vue';
import { customAxios } from '@/util/axios';
import type { InstallationDetailData } from '@/pages/installation/types/installation-types.ts';

export const useInstallationStore = defineStore('installation-store', () => {
  const installations = ref<InstallationDetailData[]>([]);

  const fetchInstallations = async() => {
    const { data: installationsResponse } = await customAxios.get<{data: InstallationDetailData[]}>('/user/installations');
    installations.value = installationsResponse.data;
    await nextTick();
  };

  const appendInstallation = (newInstallation: InstallationDetailData) => {
    installations.value.push(newInstallation);
  };

  const pairedDevices = computed<InstallationDetailData['deviceInstances']>(
    () =>
      installations.value
        .flatMap(item => item.deviceInstances)
  );

  const ownedInstallations = computed<InstallationDetailData[]>(() => installations.value?.filter(i => i.is_owner));

  const foreignInstallations = computed<InstallationDetailData[]>(() => installations.value?.filter(i => !i.is_owner));

  return {
    installations,
    fetchInstallations,
    appendInstallation,
    pairedDevices,
    ownedInstallations,
    foreignInstallations,
  };
});
