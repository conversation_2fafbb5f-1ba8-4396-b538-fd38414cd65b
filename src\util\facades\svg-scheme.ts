export interface TextOptions {
  fontWeight?: string,
  fontSize?: string,
  color?: string,
  offsetX?: number,
  offsetY?: number,
  classList?: string[],
}

export interface ExtendedTextOptions extends TextOptions {
  fractionDigits?: number;
  spaceBeforeUnit?: boolean;
}

export const drawLine = (pathElement: SVGPathElement | null, percentageValue: number) => {
  if (!pathElement) {
    return;
  }

  const pathLength = pathElement.getTotalLength(); // Get total path length
  const visibleLength = (percentageValue / 100) * pathLength; // Calculate visible length based on percentage
  pathElement.style.strokeDasharray = `${pathLength}`;
  pathElement.style.strokeDashoffset = `${pathLength - visibleLength}`;
};

export const updateTextPosition = (circleElement: SVGCircleElement | null, textValue: string, key: string, style?: TextOptions) => {
  if (!circleElement) {
    return;
  }

  const cx = parseFloat(circleElement.getAttribute('cx')!) + (style?.offsetX ?? 0);
  const cy = parseFloat(circleElement.getAttribute('cy')!) + (style?.offsetY ?? 0);

  let textElement = circleElement.parentElement!.querySelector(`#${key}`);
  if (!textElement) {
    textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    textElement.setAttribute('id', key);
    textElement.setAttribute('class', style?.classList?.join(' ') ?? '');
    textElement.setAttribute('font-family', '\'Roboto\'');
    textElement.setAttribute('font-weight', style?.fontWeight ?? '500');
    textElement.setAttribute('font-size', style?.fontSize ?? '4');
    textElement.setAttribute('fill', style?.color ?? '#dc6a26');
    textElement.setAttribute('text-anchor', 'middle');
    circleElement.parentElement!.appendChild(textElement);
  }

  textElement.setAttribute('x', cx.toString());
  textElement.setAttribute('y', cy.toString());
  textElement.setAttribute('dy', '.35em'); // Adjust to vertically center text
  textElement.textContent = textValue;
};

export const animateArrows = (groupElement: SVGElement | null, order: 'ascending' | 'descending' = 'ascending', isActive: boolean) => {
  if (!groupElement) {
    return;
  }

  if (isActive) {
    groupElement.classList.add('arrow-animate', order);
  } else {
    groupElement.classList.remove('arrow-animate', order);
  }
};
