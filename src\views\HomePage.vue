<script setup lang="ts">
import PageLoader from '@/components/global/PageLoader.vue';
import { isMobileSubdomain } from '@/composables/subdomain.ts';
import LoginPage from '@/pages/LoginPage.vue';
import RefreshPageMobile from '@/pages/RefreshPageMobile.vue';
import { useAuthStore } from '@/stores/auth-store';
import { executeWebkitMessage } from '@/util/facades/webkit-app';
import { watch } from 'vue';

const authStore = useAuthStore();

watch(() => authStore.user, newUser => {
  if (!newUser && isMobileSubdomain) {
    executeWebkitMessage('onAppReload');
  }
});
</script>

<template>
  <div>
    <RefreshPageMobile v-if="!authStore.user && isMobileSubdomain" />
    <LoginPage v-else-if="!authStore.user" />
    <router-view
      v-else
      v-slot="{ Component }"
    >
      <Suspense timeout="0">
        <template #default>
          <component :is="Component" />
        </template>
        <template #fallback>
          <PageLoader :flex-center="true" />
        </template>
      </Suspense>
    </router-view>
  </div>
</template>
