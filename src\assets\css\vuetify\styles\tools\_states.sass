@use 'sass:map'
@use 'sass:string'
@use '../settings'

@mixin states ($selector: '&::before', $active: true)
  @if string.slice(string.unquote($selector), 1, 1) != '&'
    $selector: #{'>'} #{$selector}

  &:hover
    #{$selector}
      opacity: calc(#{map.get(settings.$states, 'hover')} * var(--v-theme-overlay-multiplier))

  &:focus-visible
    #{$selector}
      opacity: calc(#{map.get(settings.$states, 'focus')} * var(--v-theme-overlay-multiplier))

  @supports not selector(:focus-visible)
    &:focus
      #{$selector}
        opacity: calc(#{map.get(settings.$states, 'focus')} * var(--v-theme-overlay-multiplier))

  @if ($active)
    &--active,
    &[aria-haspopup="menu"][aria-expanded="true"]
      @include active-states($selector)

@mixin active-states ($selector)
  #{$selector}
    opacity: calc(#{map.get(settings.$states, 'activated')} * var(--v-theme-overlay-multiplier))

  &:hover
    #{$selector}
      opacity: calc((#{map.get(settings.$states, 'activated')} + #{map.get(settings.$states, 'hover')}) * var(--v-theme-overlay-multiplier))

  &:focus-visible
    #{$selector}
      opacity: calc((#{map.get(settings.$states, 'activated')} + #{map.get(settings.$states, 'focus')}) * var(--v-theme-overlay-multiplier))

  @supports not selector(:focus-visible)
    &:focus
      #{$selector}
        opacity: calc((#{map.get(settings.$states, 'activated')} + #{map.get(settings.$states, 'focus')}) * var(--v-theme-overlay-multiplier))
