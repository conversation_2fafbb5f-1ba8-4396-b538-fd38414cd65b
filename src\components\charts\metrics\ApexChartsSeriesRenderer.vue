<script setup lang="ts">
import { MessageSquareWarning } from 'lucide-vue-next';
import { DateTime } from 'luxon';
import { computed, ref, nextTick, watch } from 'vue';
import VueApexCharts from 'vue3-apexcharts';
import PageLoader from '@/components/global/PageLoader.vue';
import type { DataSeries } from '@/util/types/data-series';
import type { ApexOptions } from 'apexcharts';
import { ComponentStateType } from '@/util/types/components';

const props = defineProps<{
  dataSeries?: DataSeries;
  componentState: { data: ComponentStateType };
  xAxisTimeFormat: string;
  options?: ApexOptions;
}>();

const chartRef = ref();
const lastZoom = ref<null | [number, number]>(null);

const getPrimaryColor = () => {
  const val = getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1');
  return `rgb(${val.replaceAll(' ', ',')})`;
};

const defaultOptions = computed<ApexOptions>(() => ({
  legend: { markers: { strokeWidth: 0 } },
  chart: {
    id: 'power',
    zoom: { enabled: true, type: 'x' },
    events: {
      beforeResetZoom: function() { lastZoom.value = null; },
      zoomed: function(_, value) { lastZoom.value = [value.xaxis.min, value.xaxis.max]; },
    },
  },
  theme: {
    monochrome: {
      enabled: false,
      color: getPrimaryColor(),
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  stroke: { curve: 'straight', width: 1.5 },
  xaxis: {
    type: 'datetime',
    labels: { datetimeUTC: false },
    formatter: function(value: number) {
      return DateTime.fromMillis(value)
        .setLocale('sk')
        .toFormat(props.xAxisTimeFormat);
    },
  },
  yaxis: {
    labels: {
      formatter: (value: number) =>
        new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(value),
    },
  },
  grid: { strokeDashArray: 2 },
  tooltip: {
    x: {
      formatter: (val: number) =>
        DateTime.fromMillis(val).toFormat('HH:mm:ss, dd LLL yyyy'),
    },
    y: {
      formatter: (val: number, { seriesIndex }: any) => {
        const unit = props.dataSeries?.[seriesIndex]?.unit || '';
        return `${val} ${unit}`;
      },
    },
  },
} as ApexOptions));

// Use default if no prop, merge props if provided
const chartOptions = computed<ApexOptions>(() => {
  if (props.options) {
    // Merge events and lastZoom logic into incoming options
    return {
      ...props.options,
      chart: {
        ...(props.options.chart ?? {}),
        events: {
          ...(props.options.chart?.events ?? {}),
          beforeResetZoom: function() { lastZoom.value = null; },
          zoomed: function(_, value) { lastZoom.value = [value.xaxis.min, value.xaxis.max]; },
        },
      },
    };
  }
  return defaultOptions.value;
});

// Re-apply lastZoom after data changes, if any
watch(
  () => props.dataSeries,
  async() => {
    if (
      chartRef.value &&
      lastZoom.value &&
      props.dataSeries?.length &&
      props.dataSeries[0]?.data?.length
    ) {
      await nextTick();
      chartRef.value.zoomX(lastZoom.value[0], lastZoom.value[1]);
    }
  }
);
</script>

<template>
  <div>
    <VueApexCharts
      v-if="dataSeries?.length && dataSeries[0]?.data"
      ref="chartRef"
      width="100%"
      height="100%"
      type="line"
      :options="chartOptions"
      :series="dataSeries"
    />
    <div
      v-else-if="componentState.data === ComponentStateType.ERROR"
      class="absolute-center p-1 rounded-md flex flex-col items-center gap-2"
    >
      <MessageSquareWarning class="h-14 w-14 opacity-30" />
      <div>{{ $t('misc.failed-to-get-data') }}</div>
    </div>
    <PageLoader
      v-if="componentState.data === ComponentStateType.LOADING"
      :absolute-center="true"
    />
  </div>
</template>
