import { DateTime } from 'luxon';

interface Metric {
  name: string;
  type: string;
  value: number;
  unit: string;
  time: string;
}

interface MetricGroup {
  time: string;
  metrics: Record<string, Metric>;
  expired: boolean;
}

interface ReducedMetricGroup {
  time: string;
  metrics: Record<string, Metric>;
  expired: boolean;
}

self.onmessage = ({ data }: MessageEvent<MetricGroup[]>) => {
  const metricsMap = new Map<string, Metric>();
  let latestTime = '';
  let latestTimeMillis = 0;
  let expired = false;

  for (const { time, metrics, expired: isExpired } of data) {
    const currentMillis = DateTime.fromISO(time).toMillis();

    if (currentMillis > latestTimeMillis) {
      latestTimeMillis = currentMillis;
      latestTime = time;
    }

    if (isExpired) {
      expired = true;
    }

    for (const metric of Object.values(metrics)) {
      const existing = metricsMap.get(metric.name);

      const existingMillis = existing ? DateTime.fromISO(existing.time).toMillis() : 0;
      const metricMillis = DateTime.fromISO(metric.time).toMillis();

      metricsMap.set(metric.name, existing
        ? {
          ...metric,
          value: existing.value + metric.value,
          time: metricMillis > existingMillis ? metric.time : existing.time,
        }
        : { ...metric }
      );
    }
  }

  const reduced: ReducedMetricGroup = {
    time: latestTime,
    metrics: Object.fromEntries(metricsMap),
    expired,
  };

  self.postMessage(reduced);
};

export {};
