<script lang="ts">
import { defineComponent, computed, h } from 'vue';

export default defineComponent({
  props: {
    level: {
      type: Number,
      default: 2,
    },
  },
  setup(props, { slots }) {
    const tag = computed(() => {
      if (props.level > 6) {
        return 'h6';
      }
      if (props.level < 1) {
        return 'h1';
      }
      return `h${props.level}`;
    });

    return () => h(tag.value, {}, slots.default ? slots.default() : undefined);
  },
});
</script>

<style scoped></style>
