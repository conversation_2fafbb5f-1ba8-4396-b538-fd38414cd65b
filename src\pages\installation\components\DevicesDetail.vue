<script setup lang="ts">
import WorkModeComponent from '@/pages/installation/components/WorkModeComponent.vue';
import { type InverterCurrentDataTransformed } from '@/util/types/api-responses';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
}

defineProps<Props>();

</script>

<template>
  <div class="flex flex-col lg:flex-row p-4 rounded">
    <div class="flex-1 lg:order-1">
      <ul class="mt-2">
        <li class="flex justify-between">
          <span>Model</span>
          <span>SP25T-KJ</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>SN</span>
          <span>9168FHYT957K7509</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Checkcode</span>
          <span>09876</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Country</span>
          <span>{{ inverterCurrentData.metrics.safety_country_text.value }}</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Inner Temperature</span>
          <span>40.5 ℃</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Rated Power</span>
          <span>10 kW</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Output Power</span>
          <span>490 W</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Output Voltage</span>
          <span>239.7/232/234.8 V</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Backup Output</span>
          <span>0 W</span>
        </li>
      </ul>
    </div>
    <div class="block! !lg:hidden hr" />
    <div class="flex-1 flex flex-col items-center justify-center order-3 lg:order-2">
      <div class="text-center">
        <p class="text-2xl">
          Status
        </p>
      </div>
      <div
        v-if="inverterCurrentData && inverterCurrentData.metrics?.work_mode"
        class="mt-2"
      >
        <WorkModeComponent :inverter-current-data="inverterCurrentData" />
      </div>
    </div>
    <div class="flex-1 lg:order-3">
      <ul class="lg:mt-2">
        <li class="flex justify-between">
          <span>DC Voltage/Current 1</span>
          <span>321.9/1.0 V/A</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>DC Voltage/Current 2</span>
          <span>303.7/1.0 V/A</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Battery</span>
          <span>0/0/0 V/A/W</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Battery Status</span>
          <span>--</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Warning (BMS)</span>
          <span>--</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Charge Current Limit (BMS)</span>
          <span>0 A</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>Discharge Current Limit (BMS)</span>
          <span>1 A</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>SOH</span>
          <span>0 %</span>
        </li>
        <li class="hr" />
        <li class="flex justify-between">
          <span>BMS Version</span>
          <span>0</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.hr {
  display: block;
  margin: 1px 0;
  background: rgb(var(--prim-col-foreground-2));
  height: 1px;
}

.dark {
  .hr {

  }
}
</style>
