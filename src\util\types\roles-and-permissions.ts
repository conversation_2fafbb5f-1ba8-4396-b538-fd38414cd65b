export type RoleData = {
  id: number,
  name: string,
  permissions: string[],
  created_at: string,
  updated_at: string,
}

export type PermissionData = {
  name: string,
}

export enum AvailablePermissions {
  ROLE_MANAGE = 'manage role',
  USER_MANAGE = 'manage user',
  SERVICE_INSTALLATION = 'service installations'
}

export enum AvailableRoles {
  SUPER_ADMIN = 'superadmin',
  CUSTOMER_UNVERIFIED_MAIL = 'customer_unverified_mail',
  CUSTOMER_VERIFIED_MAIL = 'customer_verified_mail',
  ORGANIZATION_ADMIN = 'organization_admin',
  ORGANIZATION_SERVICE_MAN = 'organization_service_man',
}