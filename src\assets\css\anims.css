@keyframes arrowFlowTranslate {
  0% {
    transform: translateY(var(--start-offset));
    opacity: 0;
    fill: var(--atk-orange) !important;
  }

  /* Starts above */
  20% {
    opacity: 1;
  }

  /* Becomes fully visible */
  80% {
    opacity: 1;
  }

  /* Stays visible while moving */
  100% {
    transform: translateY(var(--end-offset));
    opacity: 0;
    fill: var(--atk-orange-darker) !important;
  }

  /* Moves down and fades */
}

@keyframes arrowFlowColor {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.arrow-animate {
  >polygon {
    animation: arrowFlowTranslate 1s linear infinite;
    opacity: 0;

    &:first-child {
      --start-offset: -1px;
      --end-offset: 9px;
    }
  }

  >polygon:nth-child(2) {
    --start-offset: -4px;
    --end-offset: 6px;
    animation-delay: 370ms;
  }

  >polygon:nth-child(3) {
    --start-offset: -8px;
    --end-offset: 2px;
    animation-delay: 660ms;
  }

  &.descending {
    >polygon {
      opacity: 0;
      animation: arrowFlowTranslate 1s linear infinite;

      &:first-child {
        --start-offset: 8px;
        --end-offset: -2px;
        animation-delay: 680ms;
      }
    }

    >polygon:nth-child(2) {
      --start-offset: 4px;
      --end-offset: -6px;
      animation-delay: 400ms;
    }

    >polygon:nth-child(3) {
      --start-offset: 1px;
      --end-offset: -9px;
    }
  }
}

.mobile-scheme.vertical.arrow-animate {
  >polygon {
    animation: arrowFlowTranslate 1.5s linear infinite;
    opacity: 0;

    &:first-child {
      --start-offset: -1px;
      --end-offset: 12px;
    }
  }

  >polygon:nth-child(2) {
    --start-offset: -6px;
    --end-offset: 7px;
    animation-delay: 500ms;
  }

  >polygon:nth-child(3) {
    --start-offset: -11px;
    --end-offset: 2px;
    animation-delay: 1000ms;
  }

  &.descending {
    >polygon {
      opacity: 0;
      animation: arrowFlowTranslate 1.5s linear infinite;

      &:first-child {
        --start-offset: 11px;
        --end-offset: -2px;
        animation-delay: 1000ms;
      }
    }

    >polygon:nth-child(2) {
      --start-offset: 6.1px;
      --end-offset: -6.9px;
      animation-delay: 500ms;
    }

    >polygon:nth-child(3) {
      --start-offset: 1.2px;
      --end-offset: -11.8px;
    }
  }
}

@keyframes arrowFlowTranslateHorizontal {
  0% {
    transform: translateX(var(--start-offset));
    opacity: 0;
    fill: var(--atk-orange) !important;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateX(var(--end-offset));
    opacity: 0;
    fill: var(--atk-orange-darker) !important;
  }
}

.mobile-scheme.horizontal.arrow-animate {
  >polygon {
    animation: arrowFlowTranslateHorizontal 1.5s linear infinite;
    opacity: 0;

    &:first-child {
      --start-offset: -1px;
      --end-offset: 12px;
    }
  }

  >polygon:nth-child(2) {
    --start-offset: -6px;
    --end-offset: 7px;
    animation-delay: 500ms;
  }

  >polygon:nth-child(3) {
    --start-offset: -11px;
    --end-offset: 2px;
    animation-delay: 1000ms;
  }

  &.descending {
    >polygon {
      opacity: 0;
      animation: arrowFlowTranslateHorizontal 1.5s linear infinite;

      &:first-child {
        --start-offset: 11px;
        --end-offset: -2px;
        animation-delay: 1000ms;
      }
    }

    >polygon:nth-child(2) {
      --start-offset: 6.1px;
      --end-offset: -6.9px;
      animation-delay: 500ms;
    }

    >polygon:nth-child(3) {
      --start-offset: 1.2px;
      --end-offset: -11.8px;
    }
  }
}