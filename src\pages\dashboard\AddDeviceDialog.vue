<script setup lang="ts">
import { reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { useInstallationStore } from '@/stores/installation-store';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { InstallationDetailData } from '../installation/types/installation-types';

interface Props {
  installation?: InstallationDetailData,
}

const props = defineProps<Props>();

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const installationStore = useInstallationStore();
const newDevice = reactive({
  installation_id: props.installation?.id ?? '',
  pairingCode: '',
  customTitle: '',
});

if (installationStore.installations.length < 1) {
  await installationStore.fetchInstallations();
}

const emit = defineEmits(['deviceCreated']);

const onSubmit = async() => {
  try {
    // const { data: postResponse } =
    await customAxios.post(`/user/${newDevice.installation_id}/pair-device`, {
      pairing_code: newDevice.pairingCode,
      custom_title: newDevice.customTitle,
    });
    // installationStore.appendDevice(postResponse.data);
    newDevice.installation_id = props.installation?.id ?? '';
    newDevice.pairingCode = '';
    newDevice.customTitle = '';
    isOpened.value = false;
    emit('deviceCreated');
    deployToast(ToastType.SUCCESS, {
      text: t('installation.form.device-add-success'),
      timeout: 6000,
    });
  } catch (e: any) {
    if (e?.response?.status === 429) {
      deployToast(ToastType.ERROR, {
        text: t('misc.422-try-again-later'),
        timeout: 6000,
      });
      return;
    }
    if (e?.response?.data?.message === 'SERIAL_EXISTS') {
      deployToast(ToastType.ERROR, {
        text: t('installation.form.device-add-already-created'),
        timeout: 6000,
      });
      return;
    }
    deployToast(ToastType.ERROR, {
      text: t('installation.form.device-add-fail'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      :style="{margin: 'auto'}"
    >
      <form
        autocomplete="off"
        class="bg-prim-col-1 p-6 rounded-2xl w-[35rem] max-w-[90vw]"
        @submit.prevent="onSubmit"
      >
        <h3 class="font-bold text-xl mb-2">
          {{ $t('installation.add-device') }}
        </h3>
        <p class="text-black/50 dark:text-white/60 text-sm">
          {{ $t('installation.add-device-desc') }}
        </p>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label
              for="new-device-installation"
              class="text-right min-w-28 w-28"
            >
              {{ $t('installation.title-base') }}
            </Label>
            <v-select
              id="new-device-installation"
              v-model="newDevice.installation_id"
              :disabled="Boolean(installation)"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="installationStore.installations"
              item-title="title"
              item-value="id"
              variant="solo-filled"
              :density="'compact'"
              width="100%"
              name="installation"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-installation-pairing-code"
              class="text-right min-w-28 w-28"
            >
              {{ $t('installation.pairing-code') }}
            </Label>
            <Input
              id="new-installation-pairing-code"
              v-model="newDevice.pairingCode"
              name="pairing_code"
              type="text"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-installation-pairing-code"
              class="text-right min-w-28 w-28"
            >
              {{ $t('misc.own-title') }}
            </Label>
            <Input
              id="new-installation-pairing-code"
              v-model="newDevice.customTitle"
              name="custom_title"
              type="text"
            />
          </div>
        </div>
        <div class="flex items-center justify-end gap-2">
          <Button
            type="button"
            variant="default"
            class="bg-gray-400/80 hover:bg-gray-400"
            @click="isOpened = false"
          >
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button type="submit">
            {{ $t('misc.save') }}
          </Button>
        </div>
      </form>
    </v-dialog>
  </Teleport>
</template>
