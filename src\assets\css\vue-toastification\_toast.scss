@use "variables";

.#{variables.$vt-namespace}__toast {
  display: inline-flex;
  position: relative;
  max-height: variables.$vt-toast-max-height;
  min-height: variables.$vt-toast-min-height;
  box-sizing: border-box;
  margin-bottom: 1rem;
  padding: 22px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);
  justify-content: space-between;
  font-family: variables.$vt-font-family;
  max-width: variables.$vt-toast-max-width;
  min-width: variables.$vt-toast-min-width;
  pointer-events: auto;
  overflow: hidden;
  // overflow: hidden + border-radius does not work properly on Safari
  // The following magic line fixes it
  // https://stackoverflow.com/a/58283449
  transform: translateZ(0);
  direction: ltr;
  &--rtl {
    direction: rtl;
  }

  &--default {
    background-color: variables.$vt-color-default;
    color: variables.$vt-text-color-default;
  }
  &--info {
    background-color: variables.$vt-color-info;
    color: variables.$vt-text-color-info;
  }
  &--success {
    background-color: variables.$vt-color-success;
    color: variables.$vt-text-color-success;
  }
  &--error {
    background-color: variables.$vt-color-error;
    color: variables.$vt-text-color-error;
  }
  &--warning {
    background-color: variables.$vt-color-warning;
    color: variables.$vt-text-color-warning;
  }

  @media #{variables.$vt-mobile} {
    border-radius: 0px;
    margin-bottom: 0.5rem;
  }

  &-body {
    flex: 1;
    line-height: 24px;
    font-size: 16px;
    word-break: break-word;
    white-space: pre-wrap;
  }

  &-component-body {
    flex: 1;
  }

  &.disable-transition {
    animation: none !important;
  }
}
