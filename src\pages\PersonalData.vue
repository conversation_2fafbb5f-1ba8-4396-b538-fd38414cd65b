<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';
import { useAuthStore, type UserData } from '@/stores/auth-store';
import PageLoader from '@/components/global/PageLoader.vue';

const { t } = useI18n();
const componentState = ref<ComponentStateType>(ComponentStateType.LOADING);
const authStore = useAuthStore();

try {
  await authStore.fetchUser();
  componentState.value = ComponentStateType.OK;
} catch {
  componentState.value = ComponentStateType.ERROR;
}

const userData = ref<Pick<UserData, 'name' | 'surname' | 'phone' | 'email'>>({
  name: authStore.user?.name ?? '',
  surname: authStore.user?.surname ?? '',
  phone: authStore.user?.phone ?? '',
  email: authStore.user?.email ?? '',
});

const onSubmit = async() => {
  if (componentState.value === ComponentStateType.LOADING) {
    return;
  }

  componentState.value = ComponentStateType.LOADING;

  try {
    const response = await customAxios.put<{ data: UserData }>('/user/personal-data', userData.value);
    userData.value = response.data.data;
    componentState.value = ComponentStateType.OK;

    deployToast(ToastType.SUCCESS, {
      text: t('settings.personal.updated'),
      timeout: 6000,
    });
  } catch (e: any) {
    componentState.value = ComponentStateType.ERROR;

    if (e.response?.status === 422 && e.response.data?.message && e.response.data.message === 'validation.unique') {
      deployToast(ToastType.ERROR, {
        text: t('register.mail-already-used'),
        timeout: 6000,
      });
      return;
    }

    deployToast(ToastType.ERROR, {
      text: t('settings.personal.update-failed'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <div class="space-y-6 h-full">
    <div
      v-if="componentState === ComponentStateType.LOADING"
      class="flex justify-center py-8 w-60 lw:w-80 p-4 h-full items-center"
    >
      <PageLoader />
    </div>

    <form
      v-else
      autocomplete="off"
      class="space-y-4 w-60 lw:w-80 p-4"
      @submit.prevent="onSubmit"
    >
      <div class="grid gap-2">
        <Label for="new-user-name">
          {{ $t('misc.name') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-name"
          v-model="userData.name"
          name="name"
          autocomplete="off"
          type="text"
          required
          :disabled="componentState === ComponentStateType.LOADING as ComponentStateType"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-surname">
          {{ $t('misc.surname') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-surname"
          v-model="userData.surname"
          name="surname"
          autocomplete="off"
          type="text"
          required
          :disabled="componentState === ComponentStateType.LOADING as ComponentStateType"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-phone">
          {{ $t('misc.phone') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-phone"
          v-model="userData.phone"
          name="phone"
          autocomplete="off"
          type="tel"
          required
          :disabled="componentState === ComponentStateType.LOADING as ComponentStateType"
        />
      </div>

      <div class="grid gap-2">
        <Label for="new-user-email">
          {{ $t('misc.email') }} <span class="text-red-500">*</span>
        </Label>
        <Input
          id="new-user-email"
          v-model="userData.email"
          name="email"
          autocomplete="email"
          type="email"
          required
          :disabled="componentState === ComponentStateType.LOADING as ComponentStateType"
        />
      </div>

      <div class="pt-4">
        <Button
          type="submit"
          class="w-full"
          :disabled="componentState === ComponentStateType.LOADING as ComponentStateType"
        >
          <span v-if="componentState === ComponentStateType.LOADING as ComponentStateType">
            {{ $t('misc.loading') }}...
          </span>
          <span v-else>
            {{ $t('misc.send') }}
          </span>
        </Button>
      </div>
    </form>
  </div>
</template>
