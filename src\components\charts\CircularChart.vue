<template>
  <div class="w-full h-full flex flex-col justify-center items-center circle-container">
    <div
      class="relative h-full mt-5"
      :class="svgContainerClasses"
    >
      <svg
        :width="size"
        :height="size"
        viewBox="0 0 100 100"
        class="circle-svg relative"
      >
        <!-- Base circle -->
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          :style="{
            stroke: strokeBaseColor,
          }"
          :stroke-width="strokeWidth"
        />
        <!-- Active circle -->
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          :style="{
            stroke: strokeActiveColor,
          }"
          :stroke-width="strokeWidth"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashOffset"
          class="progress-circle"
        />
      </svg>
      <div class="absolute-center">
        <svg
          id="Capa_1"
          class="h-10"
          xmlns="http://www.w3.org/2000/svg"
          x="0px"
          y="0px"
          viewBox="0 0 378.6 800"
          enable-background="new 0 0 378.6 800"
          xml:space="preserve"
        >
          <g :style="{fill: iconFill<PERSON>olor}">
            <path
              d="M85.6,800c0,0,277.6-602.4,277.6-602.5l15.4-34.3c-59.7,17.2-193.1,49.8-193.6,51.2c14-39,74-214.4,76.6-214.4
			C204.6,0,147.5,0,90.4,0L73.1,82.7L0,382.5L193,330L85.6,800z"
            />
          </g>
        </svg>
      </div>
    </div>
    <div class="font-light text-xl mb-4 mt-auto">
      {{ percentage }}%
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  percentage?: number,
  strokeWidth?: number,
  strokeActiveColor?: string,
  strokeBaseColor?: string,
  svgContainerClasses?: string,
  iconFillColor?: string,
}

const props = withDefaults(defineProps<Props>(), {
  percentage: 0,
  strokeWidth: 10,
  strokeActiveColor: '#00bfff',
  strokeBaseColor: '#e6e6e6',
  svgContainerClasses: undefined,
  iconFillColor: undefined,
});

const size = '100%';
const radius = 45;
const circumference = 2 * Math.PI * radius;

const strokeDashOffset = computed(() => {
  return circumference - (props.percentage / 100) * circumference;
});
</script>

<style scoped>
.circle-container {
  width: 100%;
  height: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-svg {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
  height: 100%;
  transform: rotate(-180deg); /* Rotates to start from top */
}

.progress-circle {
  transition: stroke-dashoffset 0.35s;
  transform: rotate(0.25turn);
  transform-origin: 50% 50%;
}

.inner-icon {
  width: 30%; /* 30% of the outer circle's width */
  height: auto;
}
</style>
