<script lang="ts" setup>
import Bulb<PERSON><PERSON><PERSON> from '@/assets/lottie/bulb.json';
import EnergyJSON from '@/assets/lottie/energy-solar.json';
import UpJSON from '@/assets/lottie/up.json';
import type { InverterCurrentDataTransformed } from '@/util/types/api-responses';

interface Props {
  inverterCurrentData: InverterCurrentDataTransformed,
}

defineProps<Props>();
</script>

<template>
  <div class="flex flex-col p-1 h-full">
    <div class="grid grid-cols-4 md:grid-cols-4 place-items-center flex-1 gap-1 [&>div]:min-h-48 sm:min-h-48">
      <div class="relative rounded-xl h-fit w-full gap-1 flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.produced-energy') }}
        </div>
        <div class="relative mt-4 h-12 w-12 sm:h-24 sm:w-24 [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white">
          <LottieAnimation
            :animation-data="EnergyJSON"
            :height="`100%`"
            :width="`100%`"
          />
        </div>
        <div v-if="inverterCurrentData?.metrics?.atk_solar_energy?.value !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(inverterCurrentData.metrics.atk_solar_energy.value * 1000) / 1000 }} <br class="sm:hidden">{{ inverterCurrentData?.metrics?.atk_solar_energy?.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.energy-from-network') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white rotate-180">
          <LottieAnimation
            :animation-data="UpJSON"
            :height="`100%`"
            :width="`100%`"
          />
        </div>
        <div v-if="inverterCurrentData?.metrics?.atk_grid_energy_buy?.value !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(inverterCurrentData.metrics.atk_grid_energy_buy.value * 1000) / 1000 }} <br class="sm:hidden">{{ inverterCurrentData?.metrics?.atk_grid_energy_buy?.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.energy-to-network') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white">
          <LottieAnimation
            :animation-data="UpJSON"
            :height="`100%`"
            :width="`100%`"
          />
        </div>
        <div v-if="inverterCurrentData?.metrics?.atk_grid_energy_sell?.value !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round(inverterCurrentData.metrics.atk_grid_energy_sell.value * 1000) / 1000 }} <br class="sm:hidden">{{ inverterCurrentData?.metrics?.atk_grid_energy_sell?.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>
      <div class="relative rounded-xl h-fit w-full flex flex-col items-center sm:justify-center bg-prim-col-foreground-contrast/15">
        <div class="text-center min-w-full min-h-15 sm:text-left sm:min-w-0 sm:min-h-0 sm:absolute top-0 left-0 bg-prim-col-foreground-contrast/50 dark:bg-prim-col-foreground-contrast/15 rounded-xl sm:rounded-tl-xl sm:rounded-sm px-2 py-1 text-xs break-word">
          {{ $t('installation.house-consumption') }}
        </div>
        <div class="relative mt-4 h-14 w-14 sm:h-24 sm:w-24 [&_svg_*]:fill-prim-col-foreground-contrast [&_svg_*]:stroke-prim-col-foreground-contrast dark:[&_svg_*]:stroke-white dark:[&_svg_*]:fill-white">
          <LottieAnimation
            :animation-data="BulbJSON"
            :height="`100%`"
            :width="`100%`"
          />
        </div>
        <div v-if="inverterCurrentData?.metrics?.atk_solar_energy?.value !== undefined && inverterCurrentData?.metrics?.atk_grid_energy_sell?.value !== undefined && inverterCurrentData?.metrics?.atk_grid_energy_buy?.value !== undefined" class="font-light break-all sm:text-xl text-center sm:text-left">
          {{ Math.round((inverterCurrentData.metrics.atk_solar_energy.value - inverterCurrentData.metrics.atk_grid_energy_sell.value + inverterCurrentData.metrics.atk_grid_energy_buy.value) * 1000) / 1000 }} <br class="sm:hidden">{{ inverterCurrentData?.metrics?.atk_solar_energy?.unit ?? '?' }}
        </div>
        <div v-else class="font-light text-xl">
          -
        </div>
      </div>
    </div>
  </div>
</template>
