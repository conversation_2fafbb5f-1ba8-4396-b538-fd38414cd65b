<script lang="ts" setup>
import { computed } from 'vue';
import { ChevronDown, X } from 'lucide-vue-next';
import { Button } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shadcn-components/ui/dropdown-menu';
import type { RoleData } from '@/util/types/roles-and-permissions';

interface Props {
  roles: RoleData[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  'filter': [roleId: number | null];
}>();

const selectedRoleId = defineModel<number | null>({ default: null });

const selectedRole = computed(() => {
  if (!selectedRoleId.value) {return null;}
  return props.roles.find(role => role.id === selectedRoleId.value);
});

const handleRoleSelect = (roleId: number | null) => {
  selectedRoleId.value = roleId;
  emit('filter', roleId);
};

const clearFilter = () => {
  handleRoleSelect(null);
};
</script>

<template>
  <div class="flex items-center gap-2">
    <span class="text-sm text-muted-foreground whitespace-nowrap">{{ $t('user-management.filter-by-role') }}:</span>

    <div class="flex items-center gap-1">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button
            variant="outline"
            size="sm"
            class="h-10 gap-1 min-w-[120px] justify-between"
            :disabled="loading"
          >
            <span class="truncate">
              {{ selectedRole?.name || $t('misc.all') }}
            </span>
            <ChevronDown class="h-4 w-4 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" class="min-w-[200px]">
          <DropdownMenuItem
            class="cursor-pointer"
            :class="{ 'bg-accent': selectedRoleId === null }"
            @click="handleRoleSelect(null)"
          >
            {{ $t('misc.all') }}
          </DropdownMenuItem>
          <DropdownMenuItem
            v-for="role in roles"
            :key="role.id"
            class="cursor-pointer"
            :class="{ 'bg-accent': selectedRoleId === role.id }"
            @click="handleRoleSelect(role.id)"
          >
            {{ role.name }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        v-if="selectedRoleId !== null"
        variant="ghost"
        size="sm"
        class="h-10 w-10 p-0"
        @click="clearFilter"
      >
        <X class="h-4 w-4" />
        <span class="sr-only">Clear filter</span>
      </Button>
    </div>
  </div>
</template>
